package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2020/11/17 18:31
 */

@ThriftStruct
@Setter
@NoArgsConstructor
@ToString
public class ParamInfoDTO {

    private String paramName;
    private String fieldName;
    private String operator;
    private Integer ifEmpty;
    private String value;
    private String describe;

    @ThriftConstructor
    public ParamInfoDTO(String paramName, String fieldName, String operator, Integer ifEmpty, String value, String describe) {
        this.paramName = paramName;
        this.fieldName = fieldName;
        this.operator = operator;
        this.ifEmpty = ifEmpty;
        this.value = value;
        this.describe = describe;
    }

    @ThriftField(1)
    public String getParamName() {
        return paramName;
    }

    @ThriftField(2)
    public String getFieldName() {
        return fieldName;
    }

    @ThriftField(3)
    public String getOperator() {
        return operator;
    }

    @ThriftField(4)
    public Integer getIfEmpty() {
        return ifEmpty;
    }

    @ThriftField(5)
    public String getValue() {
        return value;
    }

    @ThriftField(6)
    public String getDescribe() {
        return describe;
    }
}
