@echo off
echo 启动Spark RPC服务 (使用157远程集群)...

set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_141

"%JAVA_HOME%\bin\java" ^
    -Xms512m ^
    -Xmx1g ^
    -Dserver.port=8081 ^
    -Dspring.profiles.active=dev ^
    -Dspring.application.name=ZYYJ-SPARK-THRIFT ^
    -Deureka.instance.appname=ZYYJ-SPARK-THRIFT ^
    -Deureka.instance.non-secure-port=8081 ^
    -Deureka.client.serviceUrl.defaultZone=******************************************/eureka/ ^
    -Dspring.main.allow-bean-definition-overriding=true ^
    -Dzyyj.rpc.thrift.server.listen_port=9009 ^
    -jar E:\dev\project\zyyj-cerebro\zyyj-spark-rpc-server\target\zyyj-spark-rpc-server.jar
