package com.zyyj.controller;

import com.zyyj.cere.pojo.dto.ApiNameDTO;
import com.zyyj.cere.pojo.dto.FilterBusinessAndTableDTO;
import com.zyyj.cere.pojo.dto.SubjectBusinessDTO;
import com.zyyj.cere.pojo.dto.SubjectDTO;
import com.zyyj.cere.pojo.entity.SubjectEntity;
import com.zyyj.cere.service.SubjectService;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.utils.PublicUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 主题域控制层只做设置路由和参数获取,service做逻辑处理
 *
 * <AUTHOR>
 * @date 2020/9/18 15:48
 */

@RestController
@RequestMapping("/api/v1/subject")
public class SubjectController {

    @Autowired
    SubjectService subjectService;

    /**
     * @Description: 主题域导航列表
     * @Param: []
     * @return: Map<String, List < SubjectBusinessDTO>>
     * @Author: bravelee
     * @Date: 2020/9/23
     */
    @GetMapping("/tree_list")
    public Map<String, List<SubjectBusinessDTO>> getSubjectNavigateList() {
        return subjectService.getSubjectNavigateList();
    }

    /**
     * @Description: 搜索业务集/数据表
     * @Param: [name]
     * @return: java.util.Map<java.lang.String, java.util.List < com.zyyj.cere.pojo.dto.ApiNameDTO>>
     * @Author: bravelee
     * @Date: 2020/11/26
     */
    @GetMapping("/filter/{name}")
    public Map<String, List<FilterBusinessAndTableDTO>> filterBusinessAndTable(@PathVariable String name, Integer isMinJie) {
        PublicUtils.checkNotEmptyArr(name);
        if (isMinJie == null) {
            isMinJie = 0;
        }
        return subjectService.filterBusinessAndTable(name, isMinJie);
    }

    /**
     * @Description: 主题域列表
     * @Param: []
     * @return: Map<String, List < SubjectDTO>>
     * @Author: bravelee
     * @Date: 2020/9/23
     */
    @GetMapping("/list")
    public Map<String, List<SubjectDTO>> getSubjectList() {
        return subjectService.getSubjectList();
    }

    /**
     * @Description: 添加主题域
     * @Param: [subjectEntity]
     * @return:
     * @Author: bravelee
     * @Date: 2020/9/23
     */
    @PostMapping("/add")
    //此处用jpa库映射参数:post参数名必须和数据库字段名一致，不一致则使用@RequestParam分别指定
    //Content-type为'multipart/form-data'(数据会自动进行映射不要添加任何注解)不能使用@RequestBody注解
    public void addSubject(@RequestBody SubjectEntity subjectEntity) {
        PublicUtils.checkNotEmptyArr(subjectEntity.getName());
        if (subjectEntity.getId() != null) {//防止注入id误更新
            throw new ApplicationException("参数错误");
        }
        String errMsg = subjectService.addSubject(subjectEntity);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }

    /**
     * @Description: 编辑主题域
     * @Param: [name, prefix, id]
     * @return:
     * @Author: bravelee
     * @Date: 2020/9/23
     */
    @PostMapping("/edit")
    public void editSubject(@RequestBody SubjectEntity subjectEntity) {
        //参数检测
        PublicUtils.checkNotEmptyArr(subjectEntity.getName());
        PublicUtils.checkIntNotNull(subjectEntity.getId().intValue());
        String errMsg = subjectService.editSubject(subjectEntity);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }

    }

    /**
     * @Description:
     * @Param: [subjectId]
     * @return:
     * @Author: bravelee
     * @Date: 2020/9/23
     */
    @PostMapping("/del/{id}")
    public void delSubject(@PathVariable Integer id) {
        PublicUtils.checkNotNullArr(id);
        String errMsg = subjectService.delSubject(id);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }

}
