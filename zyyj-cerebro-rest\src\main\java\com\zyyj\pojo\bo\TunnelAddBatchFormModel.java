package com.zyyj.pojo.bo;

import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

class TunnelAddBatchFormItemModel {
    // 原始表名(要抽取的表)
    private String sourceTable;
    // 不能中文
    private String tableName;

    private String tableComment;

    public String getSourceTable() {
        return sourceTable;
    }

    public String getTableName() {
        return tableName;
    }

    public String getTableComment() {
        return tableComment;
    }
}


@Setter
@NoArgsConstructor
public class TunnelAddBatchFormModel {

    private Long datasourceId;

    private Long businessId;

    private List<TunnelAddBatchFormItemModel> item;

    public Long getDatasourceId() {
        return datasourceId;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public List<TunnelAddBatchFormItemModel> getItem() {
        return item;
    }
}
