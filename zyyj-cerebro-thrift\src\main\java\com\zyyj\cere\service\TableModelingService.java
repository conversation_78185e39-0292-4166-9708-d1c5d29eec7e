package com.zyyj.cere.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.zyyj.cere.pojo.dto.DataMsgDTO;
import com.zyyj.cere.pojo.dto.PageListStringDTO;
import com.zyyj.cere.pojo.entity.TableModelingEntity;
import com.zyyj.domain.pagination.Paging;

import java.util.Map;


/**
 * <AUTHOR>
 * @date 2020/11/19 18:04
 */

@ThriftService
public interface TableModelingService {
    @ThriftMethod
    DataMsgDTO<Map<String, Integer>> addTableModelingComponent(Integer businessId, TableModelingEntity tableModelingEntity);

    @ThriftMethod
    PageListStringDTO previewData(String dataSql, Paging paging);


    @ThriftMethod
    TableModelingEntity getTableModelingComponent(Integer id);
}
