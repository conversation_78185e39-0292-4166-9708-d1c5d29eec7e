package com.zyyj.cere.pojo.entity;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.sdk.processor.annotation.ThriftPaged;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 数据源
 */

@ThriftStruct
@Setter
@NoArgsConstructor
@ToString
@Builder
@ApiModel(value = "DatasourceEntity", description = "数据源")
@Entity
@Table(name = "datasource")
@EntityListeners(DatasourceEntity.class)
public class DatasourceEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false,length = 11)
    @ApiModelProperty(name = "id", value = "ID")
    private Long id;


    /**
     * 数据源类型
     */
    @NotNull(message = "数据源类型不可为空")
    @ApiModelProperty(name = "type_id", value = "数据源类型")
    @Column(name = "type_id", nullable = false,length = 1)
    private Long typeId;

    /**
     * 名称
     */
    @NotEmpty(message = "名称不可为空")
    @ApiModelProperty(name = "name", value = "名称")
    @Column(name = "name", nullable = false,length = 10)
    private String name = "";

    /**
     * 描述
     */
    @ApiModelProperty(name = "describe", value = "描述")
    @Column(name = "`describe`",length = 100)
    private String describe = "";

    /**
     * 连接地址
     */
    @NotEmpty(message = "连接地址不可为空")
    @ApiModelProperty(name = "host", value = "连接地址")
    @Column(name = "host", nullable = false,length = 200)
    private String host = "";

    /**
     * 用户名
     */
    @NotEmpty(message = "用户名不可为空")
    @ApiModelProperty(name = "username", value = "用户名")
    @Column(name = "username", nullable = false,length = 50)
    private String username = "";

    /**
     * 密码
     */
    @NotEmpty(message = "密码不可为空")
    @ApiModelProperty(name = "password", value = "密码")
    @Column(name = "password", nullable = false,length = 50)
    private String password = "";

    /**
     * 数据库名称
     */
    @NotEmpty(message = "数据库名称不可为空")
    @ApiModelProperty(name = "database", value = "数据库名称")
    @Column(name = "`database`", nullable = false,length = 50)
    private String database = "";

    /**
     * 1 正常 2 删除
     */
    @ApiModelProperty(name = "status", value = "状态")
    @Column(name = "status", nullable = false)
    private Byte status = 1;

    @ThriftConstructor
    public DatasourceEntity(Long id, @NotNull(message = "数据源类型不可为空") Long typeId, @NotEmpty(message = "名称不可为空") String name, String describe, @NotEmpty(message = "连接地址不可为空") String host, @NotEmpty(message = "用户名不可为空") String username, @NotEmpty(message = "密码不可为空") String password, @NotEmpty(message = "数据库名称不可为空") String database, Byte status) {
        this.id = id;
        this.typeId = typeId;
        this.name = name;
        this.describe = describe;
        this.host = host;
        this.username = username;
        this.password = password;
        this.database = database;
        this.status = status;
    }

    @ThriftField(1)
    public Long getId() {
        return id;
    }

    @ThriftField(2)
    public Long getTypeId() {
        return typeId;
    }

    @ThriftField(3)
    public String getName() {
        return name;
    }

    @ThriftField(4)
    public String getDescribe() {
        return describe;
    }

    @ThriftField(5)
    public String getHost() {
        return host;
    }

    @ThriftField(6)
    public String getUsername() {
        return username;
    }

    @ThriftField(7)
    public String getPassword() {
        return password;
    }

    @ThriftField(8)
    public String getDatabase() {
        return database;
    }

    @ThriftField(9)
    public Byte getStatus() {
        return status;
    }
}
