package com.zyyj.cere.service;


import com.zyyj.cere.exception.CereExceptionEnum;
import com.zyyj.cere.pojo.entity.BusinessEntity;
import com.zyyj.cere.pojo.entity.TableEntity;
import com.zyyj.cere.repository.BusinessRepository;
import com.zyyj.cere.repository.SubjectRepository;
import com.zyyj.cere.repository.TableRepository;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.rpc.thrift.server.ThriftServiceHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/9/24 15:34
 */
@Slf4j
@Service
@ThriftServiceHandler
public class BusinessServiceImpl implements BusinessService {

    @Autowired
    BusinessRepository businessRepository;
    @Autowired
    SubjectRepository subjectRepository;
    @Autowired
    TableRepository tableRepository;

    @Override
    public Map<String, List<BusinessEntity>> getBusinessList(Integer subjectId) {
        //查询数据
        List<BusinessEntity> list = businessRepository.findAllBySubjectId(subjectId);
        Map<String, List<BusinessEntity>> data = new HashMap<>();
        data.put("list", list);
        return data;
    }

    @Override
    public String addBusiness(BusinessEntity businessEntity) {
        //检验主题域是否存在,避免前台注入参数污染数据库
        boolean exist = subjectRepository.existsByIdAndStatus(businessEntity.getSubjectId(), 1);
        if (!exist) {
            return "该主题域已被删除,请刷新页面";
        }
        //名称查重
        List<BusinessEntity> list = businessRepository.findByName(businessEntity.getName());
        if (list != null && list.size() > 0) {
            return "该业务集名称已存在";
        }
        BusinessEntity res = businessRepository.saveAndFlush(businessEntity);
        if (res == null) {
            throw new ApplicationException(CereExceptionEnum.ERROR_ADD);
        }
        return "";
    }

    @Override
    public String editBusiness(BusinessEntity businessEntity) {
        //检验业务集是否存在
        boolean exist = businessRepository.existsById(businessEntity.getId());
        if (!exist) {
            return "该业务集已被删除,请刷新页面";
        }
        //检验主题域是否存在,避免前台注入参数污染数据库
        exist = subjectRepository.existsByIdAndStatus(businessEntity.getSubjectId(), 1);
        if (!exist) {
            return "该主题域已被删除,请刷新页面";
        }
        //名称查重
        List<BusinessEntity> list = businessRepository.existByIdAndName(businessEntity.getId(), businessEntity.getName());
        if (list != null && list.size() > 0) {
            return "该业务集名称已存在";
        }
        BusinessEntity res = businessRepository.saveAndFlush(businessEntity);
        if (res == null) {
            throw new ApplicationException(CereExceptionEnum.ERROR_EDIT);
        }
        return "";
    }

    @Override
    public String delBusiness(Integer id) {
        List<TableEntity> list = tableRepository.findByBusinessId((long) id);
        if (list != null && list.size() > 0) {
            return "该业务集下还有表不可删除";
        }
        businessRepository.deleteById(id);
        return "";
    }
}
