# zyyj-cerebro 项目运行准备指南

## 项目概述

zyyj-cerebro 是一个基于 Spring Boot 和 Apache Thrift 构建的分布式大数据处理系统，采用微服务架构，主要用于数据分析和处理。项目集成了 Apache Spark 生态系统，支持大规模数据处理和机器学习任务。

## 技术栈详情

### 核心框架
- **Spring Boot**: 2.1.0.RELEASE
- **Java**: JDK 1.8
- **Maven**: 构建工具
- **Apache Thrift**: RPC 通信框架 (1.4.2)

### 大数据处理
- **Apache Spark**: 3.0.0
- **Scala**: 2.12.10
- **Delta Lake**: 0.8.0 (数据湖存储)
- **Hadoop**: 3.2.0 兼容

### 数据存储
- **MySQL**: 8.0+ (主数据库)
- **Redis**: 6.0+ (缓存)
- **Apache Kafka**: 消息队列 (可选)
- **Kafka Connect**: 数据连接器 (可选)

### ORM 和数据访问
- **Spring Data JPA**: 数据持久化
- **QueryDSL**: 动态查询支持
- **PageHelper**: 分页插件 (1.2.10)
- **MySQL Connector**: 8.0.13

### Web 和 API
- **Spring Boot Web**: RESTful API
- **Swagger**: 2.9.2 (API 文档)
- **Spring Boot Actuator**: 监控和管理

### 服务发现和注册
- **Eureka**: 服务注册中心

### 文档和工具
- **Apache POI**: Excel 处理 (4.1.2)
- **iText**: PDF 生成 (9.1.5)
- **Jackson**: JSON 处理
- **Lombok**: 代码简化

## 项目模块结构

### 1. zyyj-cerebro-thrift
- **功能**: Thrift 接口定义和数据结构
- **作用**: 服务间通信契约层
- **技术**: Apache Thrift, QueryDSL

### 2. zyyj-cerebro-rpc-server
- **功能**: 核心 RPC 服务
- **端口**: HTTP 8006, Thrift 9006
- **数据库**: MySQL (cerebro)
- **缓存**: Redis
- **主类**: `com.zyyj.cere.CerebroRPCServerApplication`

### 3. zyyj-cerebro-rest
- **功能**: REST API 服务
- **端口**: HTTP 8005
- **特性**: Swagger 文档, CORS 支持, PDF/Excel 处理
- **主类**: `com.zyyj.CerebroRestApplication`

### 4. zyyj-spark-thrift
- **功能**: Spark 相关 Thrift 接口定义
- **作用**: Spark 服务通信契约

### 5. zyyj-spark-rpc-server
- **功能**: Spark 大数据处理服务
- **端口**: HTTP 8008, Thrift 9008
- **特性**: Spark SQL, Streaming, MLlib, Delta Lake
- **主类**: `com.zyyj.spark.SparkRPCServerApplication`

## 环境要求

### 基础环境
- **操作系统**: Windows 10+, Linux, macOS
- **JDK**: 1.8+ (推荐 Oracle JDK 8 或 OpenJDK 8)
- **Maven**: 3.6+
- **内存**: 最少 8GB RAM (推荐 16GB+)

### 必需服务

#### 1. MySQL 8.0+
```bash
# 安装 MySQL
# Ubuntu/Debian
sudo apt-get install mysql-server

# CentOS/RHEL
sudo yum install mysql-server

# Windows: 下载 MySQL Installer
```

#### 2. Redis 6.0+
```bash
# 安装 Redis
# Ubuntu/Debian
sudo apt-get install redis-server

# CentOS/RHEL
sudo yum install redis

# Windows: 下载 Redis for Windows
```

#### 3. Eureka 服务注册中心
- 需要独立部署 Eureka Server
- 默认地址: http://***************:8761/eureka/

### 可选服务

#### 1. Apache Spark 3.0.0 (用于集群模式)
```bash
# 下载 Spark 3.0.0
wget https://archive.apache.org/dist/spark/spark-3.0.0/spark-3.0.0-bin-hadoop3.2.tgz
tar -xzf spark-3.0.0-bin-hadoop3.2.tgz
```

#### 2. Hadoop 3.2.0 (用于 HDFS 支持)
```bash
# 下载 Hadoop 3.2.0
wget https://archive.apache.org/dist/hadoop/common/hadoop-3.2.0/hadoop-3.2.0.tar.gz
tar -xzf hadoop-3.2.0.tar.gz
```

#### 3. Apache Kafka (消息队列)
```bash
# 下载 Kafka
wget https://archive.apache.org/dist/kafka/2.6.0/kafka_2.12-2.6.0.tgz
tar -xzf kafka_2.12-2.6.0.tgz
```

#### 4. Kafka Connect (数据连接器)
```bash
# Kafka Connect 包含在 Kafka 发行版中
# 需要额外配置连接器插件

# 下载常用连接器
# MySQL CDC Connector (Debezium)
wget https://repo1.maven.org/maven2/io/debezium/debezium-connector-mysql/1.6.4.Final/debezium-connector-mysql-1.6.4.Final-plugin.tar.gz

# JDBC Connector
wget https://packages.confluent.io/maven/io/confluent/kafka-connect-jdbc/10.2.0/kafka-connect-jdbc-10.2.0.jar
```

#### 5. Apache Hive (元数据存储)
```bash
# 下载 Hive 3.1.2
wget https://archive.apache.org/dist/hive/hive-3.1.2/apache-hive-3.1.2-bin.tar.gz
tar -xzf apache-hive-3.1.2-bin.tar.gz
```

## 环境配置

### 环境变量设置

#### Windows
```batch
set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_141
set MAVEN_HOME=C:\apache-maven-3.6.3
set SPARK_HOME=C:\spark-3.0.0-bin-hadoop3.2
set HADOOP_HOME=C:\hadoop-3.2.0
set PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%SPARK_HOME%\bin;%HADOOP_HOME%\bin;%PATH%
```

#### Linux/macOS
```bash
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk
export MAVEN_HOME=/opt/apache-maven-3.6.3
export SPARK_HOME=/opt/spark-3.0.0-bin-hadoop3.2
export HADOOP_HOME=/opt/hadoop-3.2.0
export PATH=$JAVA_HOME/bin:$MAVEN_HOME/bin:$SPARK_HOME/bin:$HADOOP_HOME/bin:$PATH
```

### 数据库初始化

1. **创建数据库**:
```sql
CREATE DATABASE IF NOT EXISTS `cerebro` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
```

2. **执行初始化脚本**:
```bash
mysql -u root -p cerebro < database_init.sql
```

### Maven 仓库配置

项目使用私有 Maven 仓库，需要配置 `settings.xml`:
```xml
<repositories>
    <repository>
        <id>nexus-public</id>
        <url>http://192.168.120.192:8081/repository/maven-public/</url>
    </repository>
</repositories>
```

## 部署环境配置

### 1. 本地环境 (local)
- **用途**: 开发调试
- **数据库**: 外部 MySQL 服务器
- **Redis**: 外部 Redis 服务器
- **Spark**: 本地模式 (local[*])

### 2. 开发环境 (dev)
- **用途**: 测试和集成测试
- **数据库**: ***************:3306
- **Redis**: 192.168.121.11:6379
- **Eureka**: ***************:8761
- **Kafka**: ***************:9092
- **Kafka Connect**: http://***************:9093

### 3. 生产环境 (pro)
- **用途**: 正式部署
- **数据库**: ************:3306
- **Redis**: *************:6379
- **Eureka**: ************:8761
- **Spark**: 集群模式
- **Kafka**: ************:9092
- **Kafka Connect**: http://************:8083

## 快速启动步骤

### 1. 环境检查
```bash
# 检查 Java 版本
java -version

# 检查 Maven 版本
mvn -version

# 检查 MySQL 连接
mysql -h localhost -u root -p

# 检查 Redis 连接
redis-cli ping

# 检查 Kafka Connect 状态 (可选)
curl http://***************:9093/connectors
```

### 2. 项目编译
```bash
# 进入项目根目录
cd zyyj-cerebro

# 编译打包 (选择环境)
mvn clean package -P local -DskipTests   # 本地环境
mvn clean package -P dev -DskipTests     # 开发环境
mvn clean package -P pro -DskipTests     # 生产环境
```

### 3. 服务启动顺序

**重要**: 必须按以下顺序启动服务

#### 第一步: 启动 RPC 服务器
```bash
cd zyyj-cerebro-rpc-server/target
java -Xms1024M -Xmx1024M -jar zyyj-cerebro-rpc-server.jar
```

#### 第二步: 启动 REST 服务器
```bash
cd zyyj-cerebro-rest/target
java -Xms1024M -Xmx1024M -jar zyyj-cerebro-rest.jar
```

#### 第三步: 启动 Spark 服务器 (可选)
```bash
cd zyyj-spark-rpc-server/target
java -Xms1g -Xmx2g \
  -Dspring.profiles.active=dev \
  -Dspark.master=local[*] \
  -Dspark.appName=ZYYJ-SPARK-RPC-SERVER \
  -jar zyyj-spark-rpc-server.jar
```

## 服务验证

### 健康检查
```bash
# RPC 服务健康检查
curl http://localhost:8006/actuator/health

# REST 服务健康检查
curl http://localhost:8005/actuator/health

# Spark 服务健康检查
curl http://localhost:8008/actuator/health
```

### 服务访问地址
- **REST API 文档**: http://localhost:8005/swagger-ui.html
- **Eureka 控制台**: http://***************:8761/
- **Spark UI**: http://localhost:4040 (Spark 运行时)

## 常见问题解决

### 1. 端口冲突
```bash
# 查看端口占用
netstat -tulpn | grep :8005
netstat -tulpn | grep :8006
netstat -tulpn | grep :8008

# 停止占用进程
kill -9 <PID>
```

### 2. 数据库连接失败
- 检查 MySQL 服务状态
- 验证数据库用户权限
- 确认防火墙设置

### 3. Redis 连接失败
- 检查 Redis 服务状态
- 验证密码配置
- 确认网络连通性

### 4. Spark 启动失败
- 检查 JAVA_HOME 设置
- 验证 Hadoop 环境变量
- 确认 Delta Lake JAR 文件路径

## 性能调优建议

### JVM 参数优化
```bash
# RPC 服务器
-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200

# Spark 服务器
-Xms4g -Xmx8g -XX:+UseG1GC -XX:+UseStringDeduplication
```

### 数据库连接池配置
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
```

### Redis 连接池配置
```yaml
spring:
  redis:
    jedis:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
```

## Kafka Connect 配置说明

### Kafka Connect 简介
Kafka Connect 是 Apache Kafka 的一个组件，用于在 Kafka 和其他系统之间可扩展且可靠地流式传输数据。项目中使用 Kafka Connect 来实现数据的实时同步和处理。

### 配置信息
根据项目配置文件，Kafka Connect 的访问地址为：
- **开发环境**: http://***************:9093
- **生产环境**: http://************:8083

### 启动 Kafka Connect
```bash
# 进入 Kafka 安装目录
cd $KAFKA_HOME

# 启动 Kafka Connect (分布式模式)
bin/connect-distributed.sh config/connect-distributed.properties

# 或者启动单机模式
bin/connect-standalone.sh config/connect-standalone.properties
```

### 常用 Kafka Connect API
```bash
# 查看所有连接器
curl http://***************:9093/connectors

# 查看连接器状态
curl http://***************:9093/connectors/{connector-name}/status

# 创建连接器
curl -X POST http://***************:9093/connectors \
  -H "Content-Type: application/json" \
  -d @connector-config.json

# 删除连接器
curl -X DELETE http://***************:9093/connectors/{connector-name}
```

### 连接器配置示例
```json
{
  "name": "mysql-source-connector",
  "config": {
    "connector.class": "io.debezium.connector.mysql.MySqlConnector",
    "database.hostname": "***************",
    "database.port": "3306",
    "database.user": "root",
    "database.password": "password",
    "database.server.id": "184054",
    "database.server.name": "cerebro",
    "database.include.list": "cerebro",
    "database.history.kafka.bootstrap.servers": "***************:9092",
    "database.history.kafka.topic": "schema-changes.cerebro"
  }
}
```

---

**注意**: 首次部署前请确保所有依赖服务已正确安装和配置，并执行数据库初始化脚本。如果使用 Kafka Connect，请确保相关连接器插件已正确安装。
