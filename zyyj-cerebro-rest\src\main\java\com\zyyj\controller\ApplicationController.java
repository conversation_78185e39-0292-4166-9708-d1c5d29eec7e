package com.zyyj.controller;

import com.zyyj.cere.pojo.dto.PagedAppApiDTO;
import com.zyyj.cere.pojo.dto.PagedApplicationDTO;
import com.zyyj.cere.pojo.entity.ApplicationEntity;
import com.zyyj.cere.pojo.entity.ApplicationTypeEntity;
import com.zyyj.cere.service.ApplicationService;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.domain.pagination.Paging;
import com.zyyj.json.SetApiModel;
import com.zyyj.utils.PublicUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * 应用管理只做设置路由和参数获取,service做逻辑处理
 *
 * <AUTHOR>
 * @date 2020/11/10 16:06
 */
@RestController
@RequestMapping("/api/v1/application")
public class ApplicationController {

    @Autowired
    ApplicationService applicationService;

    /**
     * @Description: 应用列表
     * @Param: [key, typeId , paging]
     * @return: com.zyyj.cere.pojo.dto.PagedApplicationDTO
     * @Author: bravelee
     * @Date: 2020/11/11
     */
    @GetMapping("/list")
    public PagedApplicationDTO getApplicationList(String key, Integer typeId, Paging paging) {
        if (key == null) {
            key = "";
        }
        if (typeId == null) {
            typeId = 0;
        }
        if (paging.getPage() == 0) {
            paging.setPage(1);
        }
        if (paging.getSize() == 0) {
            paging.setSize(10);
        }
        return applicationService.getApplicationList(key, typeId, paging);

    }

    /**
     * @Description: 应用详情
     * @Param: [id]
     * @return: com.zyyj.cere.pojo.entity.ApplicationEntity
     * @Author: bravelee
     * @Date: 2020/11/11
     */
    @GetMapping("/detail/{id}")
    public ApplicationEntity getApplicationDetail(@PathVariable Integer id) {
        return applicationService.getApplicationDetail(id);
    }

    /**
     * @Description: 添加应用
     * @Param: [applicationEntity]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/11
     */
    @PostMapping("add")
    public void addApplication(@RequestBody ApplicationEntity applicationEntity) {
        PublicUtils.checkNotEmptyArr(applicationEntity.getName());
        PublicUtils.checkIntNotNull(applicationEntity.getTypeId());
        if (applicationEntity.getId() != null) {//防止注入id误更新
            throw new ApplicationException("参数错误");
        }
        String errMsg = applicationService.addApplication(applicationEntity);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }

    /**
     * @Description: 编辑应用
     * @Param: [applicationEntity]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/11
     */
    @PostMapping("/edit")
    public void editApplication(@RequestBody ApplicationEntity applicationEntity) {
        //参数检测
        PublicUtils.checkNotEmptyArr(applicationEntity.getName());
        PublicUtils.checkIntNotNull(applicationEntity.getId());
        PublicUtils.checkIntNotNull(applicationEntity.getTypeId());
        String errMsg = applicationService.editApplication(applicationEntity);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }

    /**
     * @Description: 删除应用
     * @Param: [id]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/11
     */
    @PostMapping("/del/{id}")
    public void delApplication(@PathVariable Integer id) {
        PublicUtils.checkIntNotNull(id);
        String errMsg = applicationService.delApplication(id);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }

    /**
     * @Description: 应用分类列表
     * @Param: []
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/12
     */
    @GetMapping("/type_list")
    public Map<String, List<ApplicationTypeEntity>> getApplicationTypes() {
        return applicationService.getApplicationTypes();
    }

    /**
     * @Description: 添加应用分类
     * @Param: [applicationTypeEntity]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/12
     */
    @PostMapping("/add_type")
    public void addApplicationType(@RequestBody ApplicationTypeEntity applicationTypeEntity) {
        PublicUtils.checkNotEmptyArr(applicationTypeEntity.getName());
        if (applicationTypeEntity.getId() != null) {//防止注入id误更新
            throw new ApplicationException("参数错误");
        }
        String errMsg = applicationService.addApplicationType(applicationTypeEntity);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }

    /**
     * @Description: 编辑应用分类
     * @Param: [applicationTypeEntity]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/12
     */
    @PostMapping("/edit_type")
    public void editApplicationType(@RequestBody ApplicationTypeEntity applicationTypeEntity) {
        PublicUtils.checkNotEmptyArr(applicationTypeEntity.getName());
        PublicUtils.checkIntNotNull(applicationTypeEntity.getId());
        String errMsg = applicationService.editApplicationType(applicationTypeEntity);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }

    /**
     * @Description: 删除应用分类
     * @Param: [id]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/12
     */
    @PostMapping("/del_type/{id}")
    public void delApplicationType(@PathVariable Integer id) {
        PublicUtils.checkIntNotNull(id);
        String errMsg = applicationService.delApplicationType(id);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }

    /**
     * @Description: 应用API列表
     * @Param: [applicationId, paging]
     * @return: com.zyyj.domain.pagination.Paged<com.zyyj.cere.pojo.dto.ApiApplicationListDTO>
     * @Author: bravelee
     * @Date: 2020/11/16
     */
    @GetMapping("/api_list/{applicationId}")
    public PagedAppApiDTO getApiApplicationList(@PathVariable Integer applicationId, Paging paging) {
        PublicUtils.checkIntNotNull(applicationId);
        if (paging.getPage() == 0) {
            paging.setPage(1);
        }
        if (paging.getSize() == 0) {
            paging.setSize(10);
        }
        return applicationService.getApiApplicationList(applicationId, paging);
    }

    /**
     * @Description: 应用分配API
     * @Param: [applicationId, apiIds]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/16
     */
    @PostMapping("/set_api")
    public void setApplicationApi(@RequestBody SetApiModel setApiModel) {
        PublicUtils.checkIntNotNull(setApiModel.getApplicationId());
        PublicUtils.checkNotEmptyArr(setApiModel.getApiIds());
        applicationService.setApplicationApi(setApiModel.getApplicationId(), setApiModel.getApiIds());
    }

    /**
     * @Description: 应用移除API
     * @Param: [id]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/16
     */
    @PostMapping("/del_api/{id}")
    public void delApplicationApi(@PathVariable Integer id) {
        PublicUtils.checkIntNotNull(id);
        applicationService.delApplicationApi(id);
    }


}
