package com.zyyj.cere.service;

import com.zyyj.cere.CerebroRPCServerApplication;
import com.zyyj.cere.pojo.dto.PagedDatasourceDTO;
import com.zyyj.cere.pojo.entity.DatasourceEntity;
import com.zyyj.domain.pagination.Paging;
import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CerebroRPCServerApplication.class)
public class DatasourceServiceImplTest extends TestCase {

    @Autowired
    DatasourceService datasourceService;

    @Test
    public void getPage(){
        Paging p = new Paging(1,5);

        PagedDatasourceDTO pd = datasourceService.getListPage(p);
        System.out.println(pd);

        System.out.println(pd.getTotal());

        pd.getData().forEach(System.out::println);
    }
}