package com.zyyj.cere.service;

import com.google.gson.Gson;
import com.zyyj.cere.pojo.dto.ParamDataDTO;
import com.zyyj.cere.pojo.dto.ParamInfoDTO;
import com.zyyj.cere.pojo.entity.ApiEntity;
import com.zyyj.cere.pojo.entity.ApiServiceEntity;
import com.zyyj.cere.pojo.entity.TableEntity;
import com.zyyj.cere.repository.ApiRepository;
import com.zyyj.cere.repository.ApiServiceRepository;
import com.zyyj.cere.repository.TableRepository;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.rpc.thrift.server.ThriftServiceHandler;
import com.zyyj.spark.service.DeltaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/17 18:41
 */

@Slf4j
@Service
@ThriftServiceHandler
public class ApiTextServiceImpl implements ApiTestService {

    @Autowired
    ApiServiceRepository apiServiceRepository;
    @Autowired
    ApiRepository apiRepository;
    @Autowired
    TableRepository tableRepository;
    @Autowired
    DeltaService deltaService;


    @Override
    public Map<String, List<String>> getAPiData(String serverName, String apiName, Map<String, String> map) {
        List<ApiServiceEntity> res = apiServiceRepository.findByName(serverName);
        if (res == null || res.size() == 0) {
            throw new ApplicationException("api不存在");
        }
        List<ApiEntity> list = apiRepository.findByServiceIdAndName(res.get(0).getId(), apiName);
        if (list == null || list.size() == 0) {
            throw new ApplicationException("api不存在");
        }
        try {
            TableEntity tableEntity = tableRepository.findById((long) list.get(0).getTableId()).get();
            String tableName = tableEntity.getTableName();
            ParamDataDTO p = new Gson().fromJson(list.get(0).getDataJson(), ParamDataDTO.class);
            p.setTableName(tableName);
            if (p.getReturns().size() == 0) {
                throw new ApplicationException("api没有设定返回参数");
            }
            //获取参数
            if (p.getParams() != null && p.getParams().size() > 0) {
                for (int i = 0; i < p.getParams().size(); i++) {
                    if (map.containsKey(p.getParams().get(i).getParamName())) {
                        String str = map.get(p.getParams().get(i).getParamName());//获取请求参数值
                        if (p.getParams().get(i).getIfEmpty() == 1 && str.isEmpty()) {//不能为空字段为空报错
                            throw new ApplicationException("参数错误");
                        }
                        p.getParams().get(i).setValue(str);
                    }
                }
            }
            String size = map.get("size");//查询条数

            //生成spark查询sql
            String sql = "";
            String sqlWhere = "";
            //组件sql
            sql = "SELECT ";
            List<String> heads = new ArrayList<>();
            for (ParamInfoDTO r : p.getReturns()) {
                heads.add(r.getParamName());//保存表头
                if (r.getFieldName().equals("describe")) {
                    r.setFieldName("`describe`");
                }
                if (r.getParamName().equals("describe")) {
                    r.setParamName("`describe`");
                }
                sql += r.getFieldName() + " AS " + r.getParamName() + ",";//拼接所需要查询的字段
            }
            sql = sql.substring(0, sql.length() - 1) + " FROM " + p.getTableName();//去除逗号

            if (p.getParams().size() > 0) {
                for (ParamInfoDTO m : p.getParams()) {
                    if (m.getValue() == null || m.getValue().isEmpty()) {
                        continue;
                    }
                    sqlWhere += m.getFieldName() + " " + m.getOperator() + " '" + m.getValue() + "' AND ";
                }
                if (!sqlWhere.isEmpty()) {
                    sqlWhere = sqlWhere.substring(0, sqlWhere.length() - 4);
                }
            }

            if (!sqlWhere.isEmpty()) {
                sql += " WHERE " + sqlWhere;
            }

            if (size != null && !size.isEmpty()) {
                sql += " LIMIT " + size;
            }
            List<String> resLake = deltaService.getTableListBySql(sql);

            Map<String, List<String>> mapReturn = new HashMap<>();
            mapReturn.put("list", resLake);
            return mapReturn;
        } catch (Exception e) {
            throw new ApplicationException(e.getMessage());
        }

    }

    @Override
    public String postAPiData(String serverName, String apiName, Map<String, String> map) {
        List<ApiServiceEntity> res = apiServiceRepository.findByName(serverName);
        if (res == null || res.size() == 0) {
            return "api不存在";
        }
        List<ApiEntity> list = apiRepository.findByServiceIdAndName(res.get(0).getId(), apiName);
        if (list == null || list.size() == 0) {
            return "api不存在";
        }
        try {
            ParamDataDTO p = new Gson().fromJson(list.get(0).getDataJson(), ParamDataDTO.class);
            p.setTableName(list.get(0).getTableName());
            if (p.getReturns().size() == 0) {
                return "api没有设定返回参数";
            }
            //获取参数
            if (p.getParams() != null && p.getParams().size() > 0) {
                for (int i = 0; i < p.getParams().size(); i++) {
                    if (map.containsKey(p.getParams().get(i).getParamName())) {
                        String str = map.get(p.getParams().get(i).getParamName());//获取请求参数值
                        if (p.getParams().get(i).getIfEmpty() == 1 && str.isEmpty()) {//不能为空字段为空报错
                            return "参数错误";
                        }
                        p.getParams().get(i).setValue(str);
                    }
                }
            }

            String sqlIser = " (";
            String sqlVal = " (";
            String sql = "";
            //组件sql
            sql = "INSERT INTO  " + p.getTableName();
            if (p.getParams().size() == 0) {
                return "没有请求数据";
            }

            for (ParamInfoDTO m : p.getParams()) {
                if (m.getFieldName().equals("describe")) {
                    m.setFieldName("`describe`");//关键字处理
                }
                sqlIser += m.getFieldName() + ",";
                sqlVal += "'" + m.getValue() + "',";
            }
            if (!sqlIser.equals("") && !sqlVal.equals("")) {
                sql += sqlIser.substring(0, sqlIser.length() - 1) + ") VALUES" + sqlVal.substring(0, sqlVal.length() - 1) + ")";
            }
            //执行添加
            deltaService.execSql(sql);

        } catch (Exception e) {
            throw new ApplicationException(e.getMessage());
        }
        return "";
    }

    @Override
    public List<String> getSparkSqlRes(String sql) {

        return deltaService.querySql(sql);
    }

}
