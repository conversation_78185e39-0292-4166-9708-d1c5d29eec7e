<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %5p %5t --- [%15.15t] %-40.40c{1.} : %m%n"/>
        </Console>
        
        <File name="FileAppender" fileName="logs/application.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %5p %5t --- [%15.15t] %-40.40c{1.} : %m%n"/>
        </File>
    </Appenders>
    
    <Loggers>
        <!-- 抑制Jersey资源扫描警告 -->
        <Logger name="org.glassfish.jersey.server.internal.scanning" level="ERROR" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FileAppender"/>
        </Logger>
        
        <!-- 抑制Spark UI相关的Jersey警告 -->
        <Logger name="org.sparkproject.jetty.server.handler.ContextHandler" level="ERROR" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FileAppender"/>
        </Logger>
        
        <!-- 应用程序日志级别 -->
        <Logger name="com.zyyj" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FileAppender"/>
        </Logger>
        
        <!-- Spring框架日志级别 -->
        <Logger name="org.springframework" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FileAppender"/>
        </Logger>
        
        <!-- Spark日志级别 -->
        <Logger name="org.apache.spark" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FileAppender"/>
        </Logger>
        
        <!-- 根日志级别 -->
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FileAppender"/>
        </Root>
    </Loggers>
</Configuration>
