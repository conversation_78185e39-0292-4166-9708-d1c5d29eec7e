package com.zyyj.cere.pojo.entity;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 数据标准属性
 */

@ThriftStruct
@Data
@Entity
@NoArgsConstructor
@Table(name = "dataset_attribute")
@ApiModel(value = "DatasetAttributeEntity", description = "数据标准属性")
public class DatasetAttributeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false,length = 11)
    @ApiModelProperty(name = "id", value = "ID")
    private Integer id;

    /**
     * 属性代码
     */
    @NotNull(message = "属性代码不可为空")
    @ApiModelProperty(name = "code", value = "编号")
    @Column(name = "code", length = 100)
    private String code;

    /**
     * 属性名称
     */
    @NotNull(message = "属性名称不可为空")
    @ApiModelProperty(name = "name", value = "属性名称")
    @Column(name = "name", length = 100)
    private String name;

    /**
     * 属性类型ID（1：基础属性2：业务属性3：技术属性4：管理属性5：质量属性6：自定义属性）
     */
    @NotNull(message = "属性类型ID")
    @ApiModelProperty(name = "propertyTypeId", value = "属性类型ID")
    @Column(name = "property_type_id", length = 11)
    private Integer propertyTypeId = 6;

    /**
     * 字段长度
     */
    @ApiModelProperty(name = "fieldLength", value = "字段长度")
    @Column(name = "field_length", length = 11)
    private Integer fieldLength;

    /**
     * 字段精度
     */
    @ApiModelProperty(name = "fieldPrecision", value = "字段精度")
    @Column(name = "field_precision", length = 11)
    private Integer fieldPrecision;

    /**
     * 状态2删除
     */
    @ApiModelProperty(name = "status", value = "状态")
    @Column(name = "status", nullable = false)
    private Integer status = 0;

    /**
     * 1必填 0默认不填
     */
    @ApiModelProperty(name = "isNull", value = "是否必填")
    @Column(name = "is_null")
    private Byte isNull = 0;

    /**
     * 描述
     */
    @ApiModelProperty(name = "comment", value = "描述")
    @Column(name = "comment", length = 100)
    private String comment;

    /**
     * 属性类型名称（1：基础属性2：业务属性3：技术属性4：管理属性5：质量属性6：自定义属性）
     */
    @NotNull(message = "属性类型名称")
    @ApiModelProperty(name = "propertyTypeName", value = "属性类型名称")
    @Column(name = "property_type_name", length = 100)
    private String propertyTypeName = "自定义属性";

    /**
     * 字段类型
     */
    @ApiModelProperty(name = "type", value = "字段类型")
    @Column(name = "type", length = 20)
    private String type;

    /**
     * 字段类型
     */
    @ApiModelProperty(name = "options", value = "下拉选")
    @Column(name = "options")
    private String options;

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public String getCode() {
        return code;
    }

    @ThriftField(3)
    public String getName() {
        return name;
    }

    @ThriftField(4)
    public Integer getPropertyTypeId() {
        return propertyTypeId;
    }

    @ThriftField(5)
    public Integer getFieldLength() {
        return fieldLength;
    }

    @ThriftField(6)
    public Integer getFieldPrecision() {
        return fieldPrecision;
    }

    @ThriftField(7)
    public Integer getStatus() {
        return status;
    }

    @ThriftField(8)
    public Byte getIsNull() {
        return isNull;
    }

    @ThriftField(9)
    public String getComment() {
        return comment;
    }

    @ThriftField(10)
    public String getPropertyTypeName() {
        return propertyTypeName;
    }

    @ThriftField(11)
    public String getType() {
        return type;
    }

    @ThriftField(12)
    public String getOptions() {
        return options;
    }

    @ThriftConstructor
    public DatasetAttributeEntity(Integer id, @NotNull(message = "属性代码不可为空") String code, @NotNull(message = "属性名称不可为空") String name, @NotNull(message = "属性类型ID") Integer propertyTypeId, Integer fieldLength, Integer fieldPrecision, Integer status, Byte isNull, String comment, @NotNull(message = "属性类型名称") String propertyTypeName, String type, String options) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.propertyTypeId = propertyTypeId;
        this.fieldLength = fieldLength;
        this.fieldPrecision = fieldPrecision;
        this.status = status;
        this.isNull = isNull;
        this.comment = comment;
        this.propertyTypeName = propertyTypeName;
        this.type = type;
        this.options = options;
    }
}
