<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>zyyj-cerebro 项目解读</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        h2 {
            color: #2980b9;
            margin-top: 25px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        h3 {
            color: #3498db;
            margin-top: 20px;
        }
        ul, ol {
            padding-left: 25px;
        }
        li {
            margin-bottom: 8px;
        }
        code {
            background-color: #f0f0f0;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: Consolas, monospace;
        }
        pre {
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        pre code {
            background-color: transparent;
            padding: 0;
        }
        strong {
            color: #2c3e50;
        }
        .module {
            background-color: #fff;
            border-left: 4px solid #3498db;
            padding: 10px 15px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .tech-item {
            display: inline-block;
            background-color: #e8f4fc;
            padding: 5px 10px;
            margin: 5px;
            border-radius: 3px;
        }
        .step {
            background-color: #fff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <h1>zyyj-cerebro 项目解读</h1>

    <h2>项目概述</h2>
    <p>zyyj-cerebro 是一个基于 Spring Boot 和 Apache Thrift 构建的分布式服务系统，主要用于数据处理和分析。项目采用了微服务架构，包含多个模块，每个模块负责不同的功能。</p>

    <h2>服务构成</h2>
    <p>项目由以下几个主要模块组成：</p>

    <div class="module">
        <h3>1. zyyj-cerebro-thrift</h3>
        <ul>
            <li>定义了 Thrift 接口和数据结构</li>
            <li>作为服务间通信的契约层</li>
            <li>包含 QueryDSL 支持，用于 JPA 动态查询</li>
        </ul>
    </div>

    <div class="module">
        <h3>2. zyyj-cerebro-rpc-server</h3>
        <ul>
            <li>基于 Spring Boot 的 RPC 服务实现</li>
            <li>提供核心业务逻辑处理</li>
            <li>使用 JPA 进行数据库操作</li>
            <li>集成 Redis 缓存</li>
            <li>支持多环境配置（本地、开发、生产）</li>
        </ul>
    </div>

    <div class="module">
        <h3>3. zyyj-cerebro-rest</h3>
        <ul>
            <li>提供 RESTful API 接口</li>
            <li>集成 Swagger 文档</li>
            <li>支持 PDF 生成、Excel 处理等功能</li>
            <li>作为系统的对外服务入口</li>
        </ul>
    </div>

    <div class="module">
        <h3>4. zyyj-spark-thrift</h3>
        <ul>
            <li>Spark 相关的 Thrift 接口定义</li>
        </ul>
    </div>

    <div class="module">
        <h3>5. zyyj-spark-rpc-server</h3>
        <ul>
            <li>基于 Spark 的数据处理服务</li>
            <li>集成了 Spark SQL、Spark Streaming、Spark MLlib 等组件</li>
            <li>支持 Delta Lake 数据湖</li>
            <li>提供大数据处理和分析能力</li>
        </ul>
    </div>

    <h2>技术栈</h2>
    <div>
        <span class="tech-item"><strong>基础框架</strong>：Spring Boot 2.1.0</span>
        <span class="tech-item"><strong>RPC 框架</strong>：Apache Thrift</span>
        <span class="tech-item"><strong>数据库</strong>：MySQL 8.0</span>
        <span class="tech-item"><strong>缓存</strong>：Redis</span>
        <span class="tech-item"><strong>ORM</strong>：Spring Data JPA + QueryDSL</span>
        <span class="tech-item"><strong>大数据处理</strong>：Apache Spark 3.0.0</span>
        <span class="tech-item"><strong>数据湖</strong>：Delta Lake 0.8.0</span>
        <span class="tech-item"><strong>API 文档</strong>：Swagger 2.9.2</span>
        <span class="tech-item"><strong>构建工具</strong>：Maven</span>
    </div>

    <h2>部署方法</h2>
    <p>项目支持三种环境部署：</p>

    <div class="module">
        <h3>1. 本地环境 (local)</h3>
        <ul>
            <li>默认激活的环境配置</li>
            <li>适用于开发调试</li>
        </ul>
    </div>

    <div class="module">
        <h3>2. 开发环境 (dev)</h3>
        <ul>
            <li>用于测试和集成测试</li>
        </ul>
    </div>

    <div class="module">
        <h3>3. 生产环境 (pro)</h3>
        <ul>
            <li>用于正式部署</li>
        </ul>
    </div>

    <h3>部署步骤：</h3>

    <div class="step">
        <h4>1. 编译打包</h4>
        <pre><code>mvn clean package -P [环境标识] -DskipTests</code></pre>
        <p>环境标识可以是 local、dev 或 pro</p>
    </div>

    <div class="step">
        <h4>2. 服务启动</h4>
        <ul>
            <li>zyyj-cerebro-rest：<code>java -jar zyyj-cerebro-rest.jar</code></li>
            <li>zyyj-cerebro-rpc-server：<code>java -jar zyyj-cerebro-rpc-server.jar</code></li>
            <li>zyyj-spark-rpc-server：<code>java -jar zyyj-spark-rpc-server.jar</code></li>
        </ul>
    </div>

    <div class="step">
        <h4>3. 数据库初始化</h4>
        <p>项目包含 database_init.sql 文件，需要在部署前执行以初始化数据库</p>
    </div>

    <h2>系统架构特点</h2>
    <ol>
        <li><strong>微服务架构</strong>：通过 Thrift 实现服务间通信</li>
        <li><strong>多环境支持</strong>：通过 Maven profiles 实现不同环境的配置管理</li>
        <li><strong>大数据处理能力</strong>：集成 Spark 生态系统，支持复杂数据分析</li>
        <li><strong>RESTful API</strong>：提供标准化的 HTTP 接口</li>
        <li><strong>文档自动化</strong>：集成 Swagger 实现 API 文档自动生成</li>
    </ol>

    <p>这个项目是一个典型的企业级应用，结合了微服务架构和大数据处理能力，适用于需要处理和分析大量数据的业务场景。</p>
</body>
</html>