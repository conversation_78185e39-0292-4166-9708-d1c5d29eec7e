package com.zyyj.spark.service;


import com.zyyj.rpc.thrift.server.ThriftServiceHandler;
import com.zyyj.spark.config.SparkConfig;
import com.zyyj.spark.pojo.dto.DTMappingModel;
import com.zyyj.spark.pojo.dto.DataTunnelModel;
import com.zyyj.spark.pojo.vo.KafkaOffset;
import com.zyyj.spark.util.DeltaUtil;
import io.delta.tables.DeltaTable;

import lombok.extern.slf4j.Slf4j;
import org.apache.avro.Schema;
import org.apache.spark.api.java.function.MapFunction;
import org.apache.spark.api.java.function.VoidFunction2;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.streaming.OutputMode;
import org.apache.spark.sql.streaming.StreamingQuery;
import org.apache.spark.sql.types.DataTypes;
import org.apache.spark.sql.types.StructField;
import org.apache.spark.sql.types.StructType;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeoutException;

@Slf4j
@Service
@ThriftServiceHandler
public class DeltaTunnelServiceImpl implements DeltaTunnelService {

    private static String KAKFA_START_EARLIEST = "earliest";

    @Autowired
    SparkConfig config;

    @Autowired
    DeltaUtil util;

    public SparkSession getSparkSession() {
        return config.getSession();
    }



    @Override
    public void initTunnel(DataTunnelModel model) {
        SparkSession session = getSparkSession();

        // 配置模型
        String config = model.toJSON();
        String tableName = model.getTableName();
        String topicName = model.getTopicName();
        String bootstrapServers = model.getBootstrapServers();


        log.warn("=== 通道开启中 ===");
        log.warn(tableName);
        log.warn(topicName);


        // 获取数据湖中表
        DeltaTable table = util.getDeltaTable(model);
        // 查询 对应 topic 的 offset
        KafkaOffset offsetModel = util.queryKafkaOffsetBy(topicName);

        String startingOffsets = offsetModel.getOffsets().isEmpty() ? KAKFA_START_EARLIEST : offsetModel.getStartingOffsetJSON();

        log.warn(startingOffsets);

        Dataset<Row> df = session
                .readStream()
                .format("kafka")
                .option("kafka.bootstrap.servers", bootstrapServers)
                .option("startingOffsets", startingOffsets) //"{\"test.k12_platform.users\":{\"0\":30}}
                .option("subscribe", topicName)
                .option("failOnDataLoss", false)
                .load();

        df = df.selectExpr("partition", "offset", "CAST(value AS STRING)");

        // 表结构
        StructType structType = table.toDF().schema();
        List<DTMappingModel> conf = model.getMappingConf();
        Dataset<Row> finalDf = df;

        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    // 数据库中字段名和原始字段名对应关系
                    HashMap<String, String> relation =  new HashMap<>();
                    for (DTMappingModel con: conf) {
                        relation.put(con.getField(), con.getSourceField());
                    }

                    StreamingQuery sq = finalDf.writeStream()
                            //1.complete -> 显示所有数据,必须有聚合 2.append（无聚合时 和 update 一样） -> 追加模式，聚合必须加水印 .update: 更新模式
                            .outputMode(OutputMode.Append())
//                            .option("checkpointLocation", "/tmp/delta/checkpoint12")
//                            .option("truncate", false)
                            .foreachBatch(new VoidFunction2<Dataset<Row>, Long>() {
                                @Override
                                public void call(Dataset<Row> rowDataset, Long aLong) throws Exception {

                                    log.warn("==== 开始处理数据 ====");

                                    rowDataset.collectAsList().forEach((value)->{

                                        String str = value.getAs("value");

                                        if (str==null){
                                            return;
                                        }

                                        JSONObject jsonObject = new JSONObject(str);
                                        //JSONObject schema = jsonObject.getJSONObject("schema");
                                        JSONObject payload = jsonObject.getJSONObject("payload");

                                        //c = create, u = update, d = delete
                                        String op = payload.getString("op");
                                        JSONObject before = payload.optJSONObject("before");
                                        JSONObject after = payload.optJSONObject("after");

                                        List<String> values;
                                        String sql="";

                                        switch (op){
                                            //删除
                                            case "d":
                                                values = new ArrayList<>();
                                                for (StructField sf : structType.fields()){
                                                    // 对应表的 原始 字段 名
                                                    String sfName = relation.get(sf.name());
                                                    // 获取更新 前内容。
                                                    Object o = before.get(sfName);
                                                    // 更新前结果为null
                                                    if (o==null){
                                                        continue;
                                                    }
                                                    if (sf.dataType() == DataTypes.StringType) {
                                                        values.add(sf.name() + "=" + "'" + o + "'");
                                                    } else {
                                                        values.add(sf.name() + "=" + o);
                                                    }
                                                }
                                                String condition = String.join(" and ",values);
                                                table.delete(condition);
                                                break;
                                            // 更新
                                            case "u":
                                                // 更新后内容，作为值
                                                ArrayList<String> setValues = new ArrayList<>();
                                                // 更新前内容，作为 条件
                                                ArrayList<String> oriValues = new ArrayList<>();

                                                for (StructField sf : structType.fields()){
                                                    if (!relation.containsKey(sf.name())){
                                                        continue;
                                                    }

                                                    // 对应表的 原始 字段 名
                                                    String sfName = relation.get(sf.name());
                                                    // 获取 更新后内容
                                                    Object v = after.get(sfName);
                                                    // 获取更新 前内容。
                                                    Object o = before.get(sfName);

                                                    // 更新结果为 null， 不去处理
                                                    if (sf.dataType() == DataTypes.StringType) {
                                                        if (v!=JSONObject.NULL){
                                                            setValues.add(sf.name() + "=" + "'" + v + "'");
                                                        }
                                                        if (o!=JSONObject.NULL){
                                                            oriValues.add(sf.name() + "=" + "'" + o + "'");
                                                        }
                                                    } else {
                                                        if (v!=JSONObject.NULL){
                                                            setValues.add(sf.name() + "=" + v);
                                                        }
                                                        if (o!=JSONObject.NULL){
                                                            oriValues.add(sf.name() + "=" + o);
                                                        }
                                                    }
                                                }

                                                sql = "UPDATE " + tableName + " SET " + String.join(", ",setValues) + " WHERE " + String.join(" and ", oriValues);
                                                break;
                                            // 创建
                                            case "c":
                                                values = new ArrayList<>();

                                                for (StructField sf : structType.fields()){

                                                    System.out.println(relation.toString());
                                                    System.out.println(sf.name());
                                                    System.out.println(after);
                                                    // 包含对应表字段
                                                    if(relation.containsKey(sf.name())){
                                                        // 对应表的 原始 字段 名
                                                        String sfName = relation.get(sf.name());
                                                        // 获取 内容
                                                        Object v = after.get(sfName);
                                                        if (v==null){
                                                            v = "";
                                                        }
                                                        if (sf.dataType() == DataTypes.StringType) {
                                                            values.add("'" + v + "'");
                                                        } else {
                                                            values.add("" + v);
                                                        }
                                                    } else {
                                                        values.add("");
                                                    }
                                                }
                                                String v = String.join(",", values);

                                                sql = "INSERT INTO " + tableName + " values(" + v + ")";
                                                break;
                                            default:break;
                                        }

                                        // 打印 执行sql
                                        log.warn(sql);
                                        if (!sql.isEmpty()){
                                            session.sql(sql);
                                        }

                                        // 处理完 数据
                                        // 保存kafka偏移量到delta库中
                                        Long offset = (Long) value.getAs("offset");
                                        Integer partition = (Integer) value.getAs("partition");
                                        offsetModel.setOffset(String.valueOf(partition), offset+1);
                                        util.updateKafkaOffset(offsetModel);
                                    });
                                }
                            })
                            .start();

                    // 保存 流信息
                    util.upsertSQ(tableName, sq.id().toString(), config);

                    sq.awaitTermination();

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        thread.start();
    }

    @Override
    public String getTunnelInfo(String tableName) {
        String id = util.getSQIdBy(tableName);
        System.out.println("id:" + id);
        if (id.isEmpty()){
            return "";
        }
        SparkSession session = getSparkSession();
        StreamingQuery streamingQuery =  session.streams().get(id);
        if (streamingQuery==null){
            log.warn("流为空");
            return "";
        }
        String status = streamingQuery.status().json();
        log.warn(status);
        return status;
    }

    @Override
    public void stopTunnel(String tableName) {
        log.warn("开始删除通道");
        String id = util.getSQIdBy(tableName);
        System.out.println("删除通道,  id:" + id);
        if (id.isEmpty()){
            return;
        }
        // 通过id删除
        util.deleteSQBy(id);
        try {
            SparkSession session = getSparkSession();
            StreamingQuery streamingQuery =  session.streams().get(id);
            if (streamingQuery != null){
                streamingQuery.stop();
            } else {
                System.out.println("流为空,不处理 可能是已停止的通道");
            }
        } catch (TimeoutException e) {
            e.printStackTrace();
        }
    }

}
