#!/bin/bash

# =============================================================================
# 192.168.121.157 服务器环境检查脚本
# =============================================================================

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查系统基本信息
check_system_info() {
    log_step "检查系统基本信息..."
    
    echo "=== 系统信息 ==="
    echo "操作系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
    echo "内核版本: $(uname -r)"
    echo "主机名: $(hostname)"
    echo "IP地址: $(hostname -I | awk '{print $1}')"
    echo "当前用户: $(whoami)"
    echo "当前目录: $(pwd)"
    echo ""
    
    echo "=== 资源信息 ==="
    echo "CPU信息: $(nproc) 核心"
    echo "内存信息:"
    free -h
    echo ""
    echo "磁盘空间:"
    df -h /
    echo ""
}

# 检查Java环境
check_java_environment() {
    log_step "检查Java环境..."
    
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1)
        log_info "✅ Java已安装: $JAVA_VERSION"
        
        if [ -n "$JAVA_HOME" ]; then
            log_info "✅ JAVA_HOME已设置: $JAVA_HOME"
        else
            log_warn "❌ JAVA_HOME未设置"
            # 尝试自动检测
            JAVA_PATH=$(which java)
            if [ -n "$JAVA_PATH" ]; then
                JAVA_HOME_GUESS=$(readlink -f $JAVA_PATH | sed "s:/bin/java::")
                log_info "建议设置: export JAVA_HOME=$JAVA_HOME_GUESS"
            fi
        fi
    else
        log_error "❌ Java未安装"
        log_info "请先安装Java 8:"
        log_info "  apt update && apt install -y openjdk-8-jdk"
        return 1
    fi
}

# 检查/root目录下的安装包
check_installation_packages() {
    log_step "检查/root目录下的安装包..."
    
    echo "=== /root目录内容 ==="
    ls -la /root/
    echo ""
    
    # 检查Hadoop安装包
    HADOOP_PACKAGES=$(ls /root/ | grep -i hadoop || true)
    if [ -n "$HADOOP_PACKAGES" ]; then
        log_info "✅ 发现Hadoop安装包:"
        echo "$HADOOP_PACKAGES" | while read package; do
            echo "  - $package"
        done
    else
        log_warn "❌ 未发现Hadoop安装包"
    fi
    
    # 检查Spark安装包
    SPARK_PACKAGES=$(ls /root/ | grep -i spark || true)
    if [ -n "$SPARK_PACKAGES" ]; then
        log_info "✅ 发现Spark安装包:"
        echo "$SPARK_PACKAGES" | while read package; do
            echo "  - $package"
        done
    else
        log_warn "❌ 未发现Spark安装包"
    fi
    
    # 检查Hive安装包
    HIVE_PACKAGES=$(ls /root/ | grep -i hive || true)
    if [ -n "$HIVE_PACKAGES" ]; then
        log_info "✅ 发现Hive安装包:"
        echo "$HIVE_PACKAGES" | while read package; do
            echo "  - $package"
        done
    else
        log_warn "❌ 未发现Hive安装包"
    fi
    echo ""
}

# 检查已安装的服务
check_installed_services() {
    log_step "检查已安装的服务..."
    
    # 检查Hadoop
    if command -v hadoop &> /dev/null; then
        HADOOP_VERSION=$(hadoop version 2>/dev/null | head -n 1)
        log_info "✅ Hadoop已安装: $HADOOP_VERSION"
        log_info "   HADOOP_HOME: ${HADOOP_HOME:-未设置}"
        log_info "   Hadoop路径: $(which hadoop)"
    else
        log_warn "❌ Hadoop未安装或未在PATH中"
    fi
    
    # 检查Spark
    if command -v spark-submit &> /dev/null; then
        SPARK_VERSION=$(spark-submit --version 2>&1 | grep version | head -n 1)
        log_info "✅ Spark已安装: $SPARK_VERSION"
        log_info "   SPARK_HOME: ${SPARK_HOME:-未设置}"
        log_info "   Spark路径: $(which spark-submit)"
    else
        log_warn "❌ Spark未安装或未在PATH中"
    fi
    
    # 检查Hive
    if command -v hive &> /dev/null; then
        HIVE_VERSION=$(hive --version 2>/dev/null | head -n 1 || echo "版本信息获取失败")
        log_info "✅ Hive已安装: $HIVE_VERSION"
        log_info "   Hive路径: $(which hive)"
    else
        log_warn "❌ Hive未安装或未在PATH中"
    fi
    echo ""
}

# 检查运行中的服务
check_running_services() {
    log_step "检查运行中的服务..."
    
    echo "=== Java进程 ==="
    if command -v jps &> /dev/null; then
        JPS_OUTPUT=$(jps)
        if [ -n "$JPS_OUTPUT" ]; then
            echo "$JPS_OUTPUT"
            
            # 分析进程
            if echo "$JPS_OUTPUT" | grep -q "NameNode"; then
                log_info "✅ HDFS NameNode 正在运行"
            else
                log_warn "❌ HDFS NameNode 未运行"
            fi
            
            if echo "$JPS_OUTPUT" | grep -q "DataNode"; then
                log_info "✅ HDFS DataNode 正在运行"
            else
                log_warn "❌ HDFS DataNode 未运行"
            fi
            
            if echo "$JPS_OUTPUT" | grep -q "ResourceManager"; then
                log_info "✅ YARN ResourceManager 正在运行"
            else
                log_warn "❌ YARN ResourceManager 未运行"
            fi
            
            if echo "$JPS_OUTPUT" | grep -q "NodeManager"; then
                log_info "✅ YARN NodeManager 正在运行"
            else
                log_warn "❌ YARN NodeManager 未运行"
            fi
            
            if echo "$JPS_OUTPUT" | grep -q "Master"; then
                log_info "✅ Spark Master 正在运行"
            else
                log_warn "❌ Spark Master 未运行"
            fi
            
            if echo "$JPS_OUTPUT" | grep -q "Worker"; then
                log_info "✅ Spark Worker 正在运行"
            else
                log_warn "❌ Spark Worker 未运行"
            fi
        else
            log_warn "❌ 没有Java进程在运行"
        fi
    else
        log_warn "❌ jps命令不可用"
    fi
    echo ""
}

# 检查端口监听状态
check_port_status() {
    log_step "检查端口监听状态..."

    echo "=== 端口监听状态 ==="

    # 检查各个端口
    check_single_port() {
        local port=$1
        local name=$2
        if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
            log_info "✅ $name (端口 $port) - 正在监听"
        else
            log_warn "❌ $name (端口 $port) - 未监听"
        fi
    }

    check_single_port 7077 "Spark Master"
    check_single_port 8080 "Spark Web UI"
    check_single_port 9000 "HDFS NameNode"
    check_single_port 9083 "Hive Metastore"
    check_single_port 8088 "YARN ResourceManager"
    check_single_port 9870 "HDFS Web UI (新)"
    check_single_port 50070 "HDFS Web UI (旧)"

    echo ""
}

# 检查配置文件
check_configuration_files() {
    log_step "检查配置文件..."
    
    # 检查Hadoop配置
    if [ -n "$HADOOP_HOME" ] && [ -d "$HADOOP_HOME/etc/hadoop" ]; then
        log_info "✅ Hadoop配置目录存在: $HADOOP_HOME/etc/hadoop"
        echo "Hadoop配置文件:"
        ls -la "$HADOOP_HOME/etc/hadoop/" | grep -E "\.(xml|properties)$" || true
    else
        log_warn "❌ Hadoop配置目录不存在"
    fi
    
    # 检查Spark配置
    if [ -n "$SPARK_HOME" ] && [ -d "$SPARK_HOME/conf" ]; then
        log_info "✅ Spark配置目录存在: $SPARK_HOME/conf"
        echo "Spark配置文件:"
        ls -la "$SPARK_HOME/conf/" || true
    else
        log_warn "❌ Spark配置目录不存在"
    fi
    echo ""
}

# 生成环境变量设置建议
generate_environment_suggestions() {
    log_step "生成环境变量设置建议..."
    
    echo "=== 建议的环境变量设置 ==="
    
    # Java环境
    if [ -z "$JAVA_HOME" ]; then
        JAVA_PATH=$(which java 2>/dev/null || true)
        if [ -n "$JAVA_PATH" ]; then
            SUGGESTED_JAVA_HOME=$(readlink -f $JAVA_PATH | sed "s:/bin/java::")
            echo "export JAVA_HOME=$SUGGESTED_JAVA_HOME"
        fi
    else
        echo "export JAVA_HOME=$JAVA_HOME"
    fi
    
    # Hadoop环境
    if [ -d "/opt/hadoop" ]; then
        echo "export HADOOP_HOME=/opt/hadoop"
    elif [ -d "/usr/local/hadoop" ]; then
        echo "export HADOOP_HOME=/usr/local/hadoop"
    else
        echo "# export HADOOP_HOME=/path/to/hadoop  # 需要根据实际安装路径设置"
    fi
    
    # Spark环境
    if [ -d "/opt/spark" ]; then
        echo "export SPARK_HOME=/opt/spark"
    elif [ -d "/usr/local/spark" ]; then
        echo "export SPARK_HOME=/usr/local/spark"
    else
        echo "# export SPARK_HOME=/path/to/spark  # 需要根据实际安装路径设置"
    fi
    
    # PATH设置
    echo "export PATH=\$JAVA_HOME/bin:\$HADOOP_HOME/bin:\$HADOOP_HOME/sbin:\$SPARK_HOME/bin:\$SPARK_HOME/sbin:\$PATH"
    
    echo ""
    echo "将以上环境变量添加到 ~/.bashrc 或 /etc/environment 中"
    echo ""
}

# 主函数
main() {
    echo "============================================================================="
    echo "192.168.121.157 服务器环境检查脚本"
    echo "============================================================================="
    echo "检查时间: $(date)"
    echo ""
    
    check_system_info
    check_java_environment
    check_installation_packages
    check_installed_services
    check_running_services
    check_port_status
    check_configuration_files
    generate_environment_suggestions
    
    echo "============================================================================="
    echo "检查完成！"
    echo "============================================================================="
}

# 执行主函数
main "$@"
