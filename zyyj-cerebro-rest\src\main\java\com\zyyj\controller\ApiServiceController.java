package com.zyyj.controller;

import com.zyyj.cere.pojo.dto.ApiNameDTO;
import com.zyyj.cere.pojo.dto.PageApiDTO;
import com.zyyj.cere.pojo.entity.ApiEntity;
import com.zyyj.cere.pojo.entity.ApiServiceEntity;
import com.zyyj.cere.service.ApiService;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.domain.pagination.Paging;
import com.zyyj.utils.PublicUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/16 16:08
 */
@RestController
@RequestMapping("/api/v1/api")
@CrossOrigin(origins = "*", maxAge = 3600)
public class ApiServiceController {

    @Autowired
    ApiService apiService;

    /**
     * @Description: 服务单元列表
     * @Param: []
     * @return: java.util.Map<java.lang.String, java.util.List < com.zyyj.cere.pojo.entity.ApiServiceEntity>>
     * @Author: bravelee
     * @Date: 2020/11/16
     */
    @GetMapping("/service_list")
    public Map<String, List<ApiServiceEntity>> getServerList() {
        return apiService.getServerList();
    }

    /**
     * @Description: 服务单元详情
     * @Param: []
     * @return: com.zyyj.cere.pojo.entity.ApiServiceEntity
     * @Author: bravelee
     * @Date: 2020/11/16
     */
    @GetMapping("/service_detail/{id}")
    public ApiServiceEntity getServerDetail(@PathVariable Integer id) {
        return apiService.getServerDetail(id);
    }

    /**
     * @Description: 添加服务单元
     * @Param: [apiServiceEntity]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/16
     */
    @PostMapping("/service_add")
    public void addServer(@RequestBody ApiServiceEntity apiServiceEntity) {
        PublicUtils.checkNotEmptyArr(apiServiceEntity.getName());
        if (apiServiceEntity.getId() != null) {//防止注入id误更新
            throw new ApplicationException("参数错误");
        }
        String errMsg = apiService.addServer(apiServiceEntity);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }

    /**
     * @Description: 编辑服务单元
     * @Param: [apiServiceEntity]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/16
     */
    @PostMapping("/service_edit")
    public void editServer(@RequestBody ApiServiceEntity apiServiceEntity) {
        PublicUtils.checkNotEmptyArr(apiServiceEntity.getName());
        PublicUtils.checkIntNotNull(apiServiceEntity.getId());
        String errMsg = apiService.editServer(apiServiceEntity);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }

    /**
     * @Description: 删除服务单元
     * @Param: [id]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/16
     */
    @PostMapping("/service_del/{id}")
    public void delServer(@PathVariable Integer id) {
        PublicUtils.checkIntNotNull(id);
        String errMsg = apiService.delServer(id);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }

    /**
     * @Description: API分页列表
     * @Param: [serviceId, key, paging]
     * @return: com.zyyj.cere.pojo.dto.PageApiDTO
     * @Author: bravelee
     * @Date: 2020/11/17
     */
    @GetMapping("/page_list/{serviceId}")
    public PageApiDTO getApiPageList(@PathVariable Integer serviceId, String key, Paging paging) {
        if (key == null) {
            key = "";
        }
        if (serviceId == null) {
            serviceId = 0;
        }
        if (paging.getPage() == 0) {
            paging.setPage(1);
        }
        if (paging.getSize() == 0) {
            paging.setSize(10);
        }
        return apiService.getPageApiList(serviceId, key, paging);
    }

    /**
     * @Description: API列表(选择框)
     * @Param: [serviceId]
     * @return: java.util.Map<java.lang.String, java.util.List < com.zyyj.cere.pojo.dto.ApiNameDTO>>
     * @Author: bravelee
     * @Date: 2020/11/17
     */
    @GetMapping("/list/{serviceId}")
    public Map<String, List<ApiNameDTO>> getApiList(@PathVariable Integer serviceId) {
        return apiService.getApiList(serviceId);
    }

    /**
     * @Description: API详情
     * @Param: [id]
     * @return: com.zyyj.cere.pojo.entity.ApiEntity
     * @Author: bravelee
     * @Date: 2020/11/17
     */
    @GetMapping("/detail/{id}")
    public ApiEntity getApiDetail(@PathVariable Integer id) {
        PublicUtils.checkIntNotNull(id);

        return apiService.getApiDetail(id);
    }

    /**
     * @Description: 添加API
     * @Param: [apiEntity]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/17
     */
    @PostMapping("/add")
    public void addApi(@RequestBody ApiEntity apiEntity) {
        if (apiEntity.getId() != null) {//防止注入id误更新
            throw new ApplicationException("参数错误");
        }
        PublicUtils.checkNotEmptyArr(apiEntity.getName());
        PublicUtils.checkIntNotNull(apiEntity.getServiceId());
        PublicUtils.checkIntNotNull(apiEntity.getMethod());
        PublicUtils.checkIntNotNull(apiEntity.getReturnType());
        PublicUtils.checkIntNotNull(apiEntity.getSubjectId());
        PublicUtils.checkIntNotNull(apiEntity.getBusinessId());
        PublicUtils.checkNotEmptyArr(apiEntity.getTableName());
        PublicUtils.checkNotEmptyArr(apiEntity.getDataJson());
        apiEntity.setStatus(1);//1未发布2已发布

        String errMsg = apiService.addApi(apiEntity);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }

    /**
     * @Description: 编辑API
     * @Param: [apiEntity]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/17
     */
    @PostMapping("/edit")
    public void editApi(@RequestBody ApiEntity apiEntity) {
        PublicUtils.checkIntNotNull(apiEntity.getId());
        PublicUtils.checkNotEmptyArr(apiEntity.getName());
        PublicUtils.checkIntNotNull(apiEntity.getServiceId());
        PublicUtils.checkIntNotNull(apiEntity.getMethod());
        PublicUtils.checkIntNotNull(apiEntity.getReturnType());
        PublicUtils.checkIntNotNull(apiEntity.getSubjectId());
        PublicUtils.checkIntNotNull(apiEntity.getBusinessId());
        PublicUtils.checkNotEmptyArr(apiEntity.getTableName());
        PublicUtils.checkNotEmptyArr(apiEntity.getDataJson());

        String errMsg = apiService.editApi(apiEntity);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }

    /**
     * @Description: 发布/删除 1未发布2已发布3删除
     * @Param: [apiEntity]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/17
     */

    @PostMapping("/operation")
    public void operationApi(@RequestBody ApiEntity apiEntity) {
        PublicUtils.checkIntNotNull(apiEntity.getId());
        PublicUtils.checkIntNotNull(apiEntity.getStatus());
        String errMsg = apiService.operationApi(apiEntity);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }


}
