package com.zyyj.cere.pojo.entity;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 字典
 */

@ThriftStruct
@Data
@Table(name = "dictionary")
@ApiModel(value = "DictionaryEntity", description = "字典")
public class DictionaryEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false,length = 11)
    @ApiModelProperty(name = "id", value = "ID")
    private Integer id;


    /**
     * 类型名
     */
    @NotNull(message = "类型名不可为空")
    @ApiModelProperty(name = "type_name", value = "类型名")
    @Column(name = "type_name", nullable = false,length = 100)
    private String typeName;

    /**
     * 类型ID
     */
    @ApiModelProperty(name = "type_id", value = "父ID")
    @Column(name = "type_id" ,length = 4)
    private Integer typeId;

    /**
     * 具体分类Id
     */
    @ApiModelProperty(name = "detail_id", value = "具体分类Id")
    @Column(name = "`detail_id`",length = 4)
    private Integer detailId;

    /**
     * 具体分类名称
     */
    @ApiModelProperty(name = "detail_name", value = "具体分类名称")
    @Column(name = "detail_name" ,length = 100)
    private String detailName ;

    /**
     * 0 正常 2 删除
     */
    @ApiModelProperty(name = "status", value = "状态")
    @Column(name = "status", nullable = false)
    private Byte status;

    @ThriftConstructor
    public DictionaryEntity(Integer id, @NotNull(message = "类型名不可为空") String typeName, Integer typeId, Integer detailId, String detailName, Byte status) {
        this.id = id;
        this.typeName = typeName;
        this.typeId = typeId;
        this.detailId = detailId;
        this.detailName = detailName;
        this.status = status;
    }

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public String getTypeName() {
        return typeName;
    }

    @ThriftField(3)
    public Integer getTypeId() {
        return typeId;
    }

    @ThriftField(4)
    public Integer getDetailId() {
        return detailId;
    }

    @ThriftField(5)
    public String getDetailName() {
        return detailName;
    }

    @ThriftField(6)
    public Byte getStatus() {
        return status;
    }
}
