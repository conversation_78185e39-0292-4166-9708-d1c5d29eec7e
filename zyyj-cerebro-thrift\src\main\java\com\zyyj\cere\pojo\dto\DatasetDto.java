package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 数据集
 */

@ThriftStruct
@Data
@NoArgsConstructor
public class DatasetDto implements Serializable {

    private Integer id;

    private String name;

    private Integer parentId;

    private Integer type;

    private List<DatasetDto> list ;

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public String getName() {
        return name;
    }

    @ThriftField(3)
    public Integer getParentId() {
        return parentId;
    }

    @ThriftField(4)
    public Integer getType() {
        return type;
    }

    @ThriftField(5)
    public List<DatasetDto> getList() {
        return list;
    }

    @ThriftConstructor
    public DatasetDto(Integer id, String name, Integer parentId, Integer type) {
        this.id = id;
        this.name = name;
        this.parentId = parentId;
        this.type = type;
    }




}
