package com.zyyj.cere;

import com.zyyj.cere.service.KafkaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

//@Component  // 暂时禁用，避免启动时Kafka连接问题
public class CerebroRPCServerInit implements ApplicationRunner {

    @Autowired
    KafkaService service;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        service.deleteConnector();
    }
}
