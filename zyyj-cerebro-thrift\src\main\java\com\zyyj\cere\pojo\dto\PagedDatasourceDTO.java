package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.acc.thrift.pojo.dto.StudentDTO;
import com.zyyj.cere.pojo.entity.DatasourceEntity;
import com.zyyj.domain.pagination.Paged;
import java.util.List;
import org.springframework.data.domain.Page;

@ThriftStruct
public class PagedDatasourceDTO extends Paged<DatasourceEntity> {

    @ThriftConstructor
    public PagedDatasourceDTO(long total, List<DatasourceEntity> data) {
        super(total, data);
    }

    public PagedDatasourceDTO(Page<DatasourceEntity> page) {
        super(page);
    }
}