package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.zyyj.sdk.processor.annotation.ThriftPaged;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@ThriftStruct
@Setter
@NoArgsConstructor
@ToString
@Builder
public class TableStructDTO {
    private String col_name;
    private String data_type;
    private String comment;

    @ThriftConstructor
    public TableStructDTO(String col_name, String data_type, String comment) {
        this.col_name = col_name;
        this.data_type = data_type;
        this.comment = comment;
    }

    @ThriftField(1)
    public String getCol_name() {
        return col_name;
    }

    @ThriftField(2)
    public String getData_type() {
        return data_type;
    }

    @ThriftField(3)
    public String getComment() {
        return comment;
    }

    public static List<TableStructDTO> getList(List<String> json) {
        List<TableStructDTO> l = new ArrayList<TableStructDTO>();
        for (String row : json
        ) {
            TableStructDTO t = new Gson().fromJson(row, TableStructDTO.class);
            if (Objects.equals(t.getCol_name(), "")) {
                break;
            }
            l.add(t);
        }
        return l;
    }
}

