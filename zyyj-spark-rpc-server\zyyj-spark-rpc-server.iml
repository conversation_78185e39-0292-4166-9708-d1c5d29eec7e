<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="Spring" name="Spring">
      <configuration />
    </facet>
    <facet type="web" name="Web">
      <configuration>
        <webroots />
        <sourceRoots>
          <root url="file://$MODULE_DIR$/src/main/java" />
          <root url="file://$MODULE_DIR$/src/main/resources" />
        </sourceRoots>
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-actuator:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-actuator-autoconfigure:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-actuator:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.7" level="project" />
    <orderEntry type="library" name="Maven: io.micrometer:micrometer-core:1.1.0" level="project" />
    <orderEntry type="library" name="Maven: org.hdrhistogram:HdrHistogram:2.1.9" level="project" />
    <orderEntry type="library" name="Maven: org.latencyutils:LatencyUtils:2.0.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-web:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-json:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.9.7" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-parameter-names:2.9.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-tomcat:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-core:9.0.12" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-el:9.0.12" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-websocket:9.0.12" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.validator:hibernate-validator:6.0.13.Final" level="project" />
    <orderEntry type="library" name="Maven: org.jboss.logging:jboss-logging:3.3.2.Final" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml:classmate:1.4.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-web:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webmvc:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-data-redis:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-redis:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-keyvalue:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-tx:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-oxm:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context-support:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: io.lettuce:lettuce-core:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: io.projectreactor:reactor-core:3.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.reactivestreams:reactive-streams:1.0.2" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-common:4.1.29.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-transport:4.1.29.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-buffer:4.1.29.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-resolver:4.1.29.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-handler:4.1.29.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-codec:4.1.29.Final" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-log4j2:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-slf4j-impl:2.11.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.11.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-core:2.11.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-jul:2.11.1" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jul-to-slf4j:1.7.25" level="project" />
    <orderEntry type="library" name="Maven: com.zyyj.sdk:zyyj-thrift-boot-starter:1.0.2-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.zyyj.sdk:zyyj-thrift-boot-autoconfigure:1.0.2-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter-netflix-eureka-client:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-context:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-rsa:1.0.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcpkix-jdk15on:1.60" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcprov-jdk15on:1.60" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-netflix-hystrix:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-netflix-eureka-client:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.eureka:eureka-client:1.9.12" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.netflix.netflix-commons:netflix-eventbus:0.3.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.netflix.netflix-commons:netflix-infix:0.3.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: commons-jxpath:commons-jxpath:1.3" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.apache.commons:commons-math:2.2" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.archaius:archaius-core:0.7.6" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.netflix.servo:servo-core:0.12.21" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.sun.jersey:jersey-client:1.19.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.sun.jersey.contribs:jersey-apache-client4:1.19.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.github.vlsi.compactmap:compactmap:1.2.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.github.andrewoma.dexx:dexx-collections:0.2" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.eureka:eureka-core:1.9.12" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-netflix-archaius:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: commons-configuration:commons-configuration:1.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.ribbon:ribbon:2.3.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.netflix.ribbon:ribbon-transport:2.3.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: io.reactivex:rxnetty-contexts:0.4.9" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: io.reactivex:rxnetty-servo:0.4.9" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.netflix.hystrix:hystrix-core:1.4.3" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: io.reactivex:rxnetty:0.4.9" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.ribbon:ribbon-core:2.3.0" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.ribbon:ribbon-httpclient:2.3.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.netflix.netflix-commons:netflix-commons-util:0.1.1" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.ribbon:ribbon-loadbalancer:2.3.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.netflix.netflix-commons:netflix-statistics:0.1.1" level="project" />
    <orderEntry type="library" name="Maven: io.reactivex:rxjava:1.3.8" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.ribbon:ribbon-eureka:2.3.0" level="project" />
    <orderEntry type="library" name="Maven: com.thoughtworks.xstream:xstream:1.4.10" level="project" />
    <orderEntry type="library" name="Maven: xmlpull:xmlpull:1.1.3.1" level="project" />
    <orderEntry type="library" name="Maven: xpp3:xpp3_min:1.1.4c" level="project" />
    <orderEntry type="module" module-name="zyyj-spark-thrift" />
    <orderEntry type="library" name="Maven: com.zyyj.sdk:zyyj-domain-common:1.0.2-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-collections4:4.2" level="project" />
    <orderEntry type="library" name="Maven: apache-beanutils:commons-beanutils:1.7.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-commons:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.belerweb:pinyin4j:2.5.0" level="project" />
    <orderEntry type="library" name="Maven: com.zyyj.sdk:zyyj-rpc-thrift-common:1.0.2-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.zyyj.sdk:zyyj-processor-annotation:1.0.0-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-lang3:3.8.1" level="project" />
    <orderEntry type="library" name="Maven: com.facebook.swift:swift-annotations:0.23.1" level="project" />
    <orderEntry type="library" name="Maven: javax.validation:validation-api:2.0.1.Final" level="project" />
    <orderEntry type="library" name="Maven: io.swagger:swagger-annotations:1.5.13" level="project" />
    <orderEntry type="library" name="Maven: com.zyyj.sdk:zyyj-rpc-thrift:1.0.2-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.facebook.swift:swift-service:0.23.1-zyyj" level="project" />
    <orderEntry type="library" name="Maven: com.facebook.swift:swift-codec:0.23.1" level="project" />
    <orderEntry type="library" name="Maven: jp.skypencil.guava:helper:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: com.facebook.nifty:nifty-client:0.23.0" level="project" />
    <orderEntry type="library" name="Maven: com.facebook.nifty:nifty-core:0.23.0" level="project" />
    <orderEntry type="library" name="Maven: com.google.inject.extensions:guice-multibindings:4.0" level="project" />
    <orderEntry type="library" name="Maven: com.facebook.nifty:nifty-ssl:0.23.0" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-tcnative-boringssl-static:2.0.17.Final" level="project" />
    <orderEntry type="library" name="Maven: io.airlift:configuration:0.119" level="project" />
    <orderEntry type="library" name="Maven: cglib:cglib-nodep:2.2.2" level="project" />
    <orderEntry type="library" name="Maven: io.airlift:stats:0.119" level="project" />
    <orderEntry type="library" name="Maven: io.airlift:slice:0.10" level="project" />
    <orderEntry type="library" name="Maven: org.openjdk.jol:jol-core:0.1" level="project" />
    <orderEntry type="library" name="Maven: io.airlift:units:0.119" level="project" />
    <orderEntry type="library" name="Maven: io.airlift:log:0.119" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty:3.10.5.Final" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.findbugs:annotations:2.0.3" level="project" />
    <orderEntry type="library" name="Maven: org.weakref:jmxutils:1.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-commons:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-crypto:5.1.1.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.zyyj:zyyj-acc-thrift:1.0.0-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.zyyj.sdk:zyyj-processor:1.0.0-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.google.auto.service:auto-service:1.0-rc4" level="project" />
    <orderEntry type="library" name="Maven: com.google.auto:auto-common:0.8" level="project" />
    <orderEntry type="library" name="Maven: com.squareup:javapoet:1.10.0" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.javax.persistence:hibernate-jpa-2.1-api:1.0.0.Final" level="project" />
    <orderEntry type="library" name="Maven: tk.mybatis:mapper-core:1.1.1" level="project" />
    <orderEntry type="library" name="Maven: javax.persistence:persistence-api:1.0" level="project" />
    <orderEntry type="library" name="Maven: mysql:mysql-connector-java:8.0.13" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hbase.thirdparty:hbase-shaded-miscellaneous:2.1.0" level="project" />
    <orderEntry type="library" name="Maven: io.delta:delta-core_2.12:0.8.0" level="project" />
    <orderEntry type="library" name="Maven: org.scala-lang:scala-library:2.12.10" level="project" />
    <orderEntry type="library" name="Maven: org.antlr:antlr4:4.7" level="project" />
    <orderEntry type="library" name="Maven: org.antlr:antlr-runtime:3.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.antlr:ST4:4.0.8" level="project" />
    <orderEntry type="library" name="Maven: org.abego.treelayout:org.abego.treelayout.core:1.0.3" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish:javax.json:1.0.4" level="project" />
    <orderEntry type="library" name="Maven: com.ibm.icu:icu4j:58.2" level="project" />
    <orderEntry type="library" name="Maven: org.antlr:antlr4-runtime:4.7" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-core_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.avro:avro:1.8.2" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.jackson:jackson-core-asl:1.9.13" level="project" />
    <orderEntry type="library" name="Maven: org.tukaani:xz:1.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.avro:avro-mapred:hadoop2:1.8.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.avro:avro-ipc:1.8.2" level="project" />
    <orderEntry type="library" name="Maven: com.twitter:chill_2.12:0.9.5" level="project" />
    <orderEntry type="library" name="Maven: com.esotericsoftware:kryo-shaded:4.0.2" level="project" />
    <orderEntry type="library" name="Maven: com.esotericsoftware:minlog:1.3.0" level="project" />
    <orderEntry type="library" name="Maven: com.twitter:chill-java:0.9.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.xbean:xbean-asm7-shaded:4.15" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-client:2.7.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-hdfs:2.7.4" level="project" />
    <orderEntry type="library" name="Maven: xmlenc:xmlenc:0.52" level="project" />
    <orderEntry type="library" name="Maven: xerces:xercesImpl:2.9.1" level="project" />
    <orderEntry type="library" name="Maven: xml-apis:xml-apis:1.4.01" level="project" />
    <orderEntry type="library" name="Maven: org.apache.htrace:htrace-core:3.1.0-incubating" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-mapreduce-client-app:2.7.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-mapreduce-client-common:2.7.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-mapreduce-client-shuffle:2.7.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-mapreduce-client-core:2.7.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-mapreduce-client-jobclient:2.7.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-launcher_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-kvstore_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.fusesource.leveldbjni:leveldbjni-all:1.8" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-network-common_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-network-shuffle_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-unsafe_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: javax.activation:activation:1.1.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.curator:curator-recipes:2.7.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.curator:curator-framework:2.7.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.zookeeper:zookeeper:3.4.14" level="project" />
    <orderEntry type="library" name="Maven: org.apache.yetus:audience-annotations:0.5.0" level="project" />
    <orderEntry type="library" name="Maven: javax.servlet:javax.servlet-api:4.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-math3:3.4.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-text:1.6" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.findbugs:jsr305:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.25" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jcl-over-slf4j:1.7.25" level="project" />
    <orderEntry type="library" name="Maven: log4j:log4j:1.2.17" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-log4j12:1.7.25" level="project" />
    <orderEntry type="library" name="Maven: com.ning:compress-lzf:1.0.3" level="project" />
    <orderEntry type="library" name="Maven: org.xerial.snappy:snappy-java:1.1.7.5" level="project" />
    <orderEntry type="library" name="Maven: org.lz4:lz4-java:1.7.1" level="project" />
    <orderEntry type="library" name="Maven: com.github.luben:zstd-jni:1.4.4-3" level="project" />
    <orderEntry type="library" name="Maven: org.roaringbitmap:RoaringBitmap:0.7.45" level="project" />
    <orderEntry type="library" name="Maven: org.roaringbitmap:shims:0.7.45" level="project" />
    <orderEntry type="library" name="Maven: commons-net:commons-net:3.1" level="project" />
    <orderEntry type="library" name="Maven: org.scala-lang.modules:scala-xml_2.12:1.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.scala-lang:scala-reflect:2.12.10" level="project" />
    <orderEntry type="library" name="Maven: org.json4s:json4s-jackson_2.12:3.6.6" level="project" />
    <orderEntry type="library" name="Maven: org.json4s:json4s-core_2.12:3.6.6" level="project" />
    <orderEntry type="library" name="Maven: org.json4s:json4s-ast_2.12:3.6.6" level="project" />
    <orderEntry type="library" name="Maven: org.json4s:json4s-scalap_2.12:3.6.6" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jersey.core:jersey-client:2.27" level="project" />
    <orderEntry type="library" name="Maven: javax.ws.rs:javax.ws.rs-api:2.1" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.hk2.external:javax.inject:2.5.0-b42" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jersey.core:jersey-common:2.27" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.hk2:osgi-resource-locator:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jersey.core:jersey-server:2.27" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jersey.media:jersey-media-jaxb:2.27" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jersey.containers:jersey-container-servlet:2.27" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jersey.containers:jersey-container-servlet-core:2.27" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jersey.inject:jersey-hk2:2.27" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.hk2:hk2-locator:2.5.0-b42" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.hk2.external:aopalliance-repackaged:2.5.0-b42" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.hk2:hk2-api:2.5.0-b42" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.hk2:hk2-utils:2.5.0-b42" level="project" />
    <orderEntry type="library" name="Maven: org.javassist:javassist:3.22.0-CR2" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-all:4.1.29.Final" level="project" />
    <orderEntry type="library" name="Maven: com.clearspring.analytics:stream:2.9.6" level="project" />
    <orderEntry type="library" name="Maven: io.dropwizard.metrics:metrics-core:4.0.3" level="project" />
    <orderEntry type="library" name="Maven: io.dropwizard.metrics:metrics-jvm:4.0.3" level="project" />
    <orderEntry type="library" name="Maven: io.dropwizard.metrics:metrics-json:4.0.3" level="project" />
    <orderEntry type="library" name="Maven: io.dropwizard.metrics:metrics-graphite:4.0.3" level="project" />
    <orderEntry type="library" name="Maven: io.dropwizard.metrics:metrics-jmx:4.0.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.ivy:ivy:2.4.0" level="project" />
    <orderEntry type="library" name="Maven: oro:oro:2.0.8" level="project" />
    <orderEntry type="library" name="Maven: net.razorvine:pyrolite:4.30" level="project" />
    <orderEntry type="library" name="Maven: net.sf.py4j:py4j:0.10.9" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-tags_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-crypto:1.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.spark-project.spark:unused:1.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-sql_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: com.univocity:univocity-parsers:2.8.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-sketch_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-catalyst_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.arrow:arrow-vector:0.15.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.arrow:arrow-format:0.15.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.arrow:arrow-memory:0.15.1" level="project" />
    <orderEntry type="library" name="Maven: com.google.flatbuffers:flatbuffers-java:1.9.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.orc:orc-core:1.5.10" level="project" />
    <orderEntry type="library" name="Maven: org.apache.orc:orc-shims:1.5.10" level="project" />
    <orderEntry type="library" name="Maven: commons-lang:commons-lang:2.6" level="project" />
    <orderEntry type="library" name="Maven: io.airlift:aircompressor:0.10" level="project" />
    <orderEntry type="library" name="Maven: org.threeten:threeten-extra:1.5.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.orc:orc-mapreduce:1.5.10" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hive:hive-storage-api:2.7.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.parquet:parquet-column:1.10.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.parquet:parquet-common:1.10.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.parquet:parquet-encoding:1.10.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.parquet:parquet-hadoop:1.10.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.parquet:parquet-format:2.4.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.parquet:parquet-jackson:1.10.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-streaming_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-mllib_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.scala-lang.modules:scala-parser-combinators_2.12:1.1.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-graphx_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: com.github.fommil.netlib:core:1.1.2" level="project" />
    <orderEntry type="library" name="Maven: net.sourceforge.f2j:arpack_combined_all:0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-mllib-local_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.scalanlp:breeze_2.12:1.0" level="project" />
    <orderEntry type="library" name="Maven: org.scalanlp:breeze-macros_2.12:1.0" level="project" />
    <orderEntry type="library" name="Maven: net.sf.opencsv:opencsv:2.3" level="project" />
    <orderEntry type="library" name="Maven: com.github.wendykierp:JTransforms:3.1" level="project" />
    <orderEntry type="library" name="Maven: pl.edu.icm:JLargeArrays:1.5" level="project" />
    <orderEntry type="library" name="Maven: com.chuusai:shapeless_2.12:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: org.typelevel:macro-compat_2.12:1.1.1" level="project" />
    <orderEntry type="library" name="Maven: org.typelevel:spire_2.12:0.17.0-M1" level="project" />
    <orderEntry type="library" name="Maven: org.typelevel:spire-macros_2.12:0.17.0-M1" level="project" />
    <orderEntry type="library" name="Maven: org.typelevel:spire-platform_2.12:0.17.0-M1" level="project" />
    <orderEntry type="library" name="Maven: org.typelevel:spire-util_2.12:0.17.0-M1" level="project" />
    <orderEntry type="library" name="Maven: org.typelevel:machinist_2.12:0.6.8" level="project" />
    <orderEntry type="library" name="Maven: org.typelevel:algebra_2.12:2.0.0-M2" level="project" />
    <orderEntry type="library" name="Maven: org.typelevel:cats-kernel_2.12:2.0.0-M4" level="project" />
    <orderEntry type="library" name="Maven: org.scala-lang.modules:scala-collection-compat_2.12:2.1.1" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jaxb:jaxb-runtime:2.3.1" level="project" />
    <orderEntry type="library" name="Maven: javax.xml.bind:jaxb-api:2.3.1" level="project" />
    <orderEntry type="library" name="Maven: com.sun.istack:istack-commons-runtime:3.0.7" level="project" />
    <orderEntry type="library" name="Maven: javax.activation:javax.activation-api:1.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-hive_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hive:hive-common:2.3.7" level="project" />
    <orderEntry type="library" name="Maven: jline:jline:2.12" level="project" />
    <orderEntry type="library" name="Maven: com.tdunning:json:1.8" level="project" />
    <orderEntry type="library" name="Maven: com.github.joshelser:dropwizard-metrics-hadoop-metrics2-reporter:0.1.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hive:hive-exec:core:2.3.7" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hive:hive-vector-code-gen:2.3.7" level="project" />
    <orderEntry type="library" name="Maven: org.apache.velocity:velocity:1.5" level="project" />
    <orderEntry type="library" name="Maven: stax:stax-api:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hive:hive-metastore:2.3.7" level="project" />
    <orderEntry type="library" name="Maven: javolution:javolution:5.5.1" level="project" />
    <orderEntry type="library" name="Maven: com.jolbox:bonecp:0.8.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.zaxxer:HikariCP:3.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.datanucleus:datanucleus-api-jdo:4.2.4" level="project" />
    <orderEntry type="library" name="Maven: org.datanucleus:datanucleus-rdbms:4.1.19" level="project" />
    <orderEntry type="library" name="Maven: commons-pool:commons-pool:1.6" level="project" />
    <orderEntry type="library" name="Maven: commons-dbcp:commons-dbcp:1.4" level="project" />
    <orderEntry type="library" name="Maven: javax.jdo:jdo-api:3.0.1" level="project" />
    <orderEntry type="library" name="Maven: javax.transaction:jta:1.1" level="project" />
    <orderEntry type="library" name="Maven: org.datanucleus:javax.jdo:3.2.0-m3" level="project" />
    <orderEntry type="library" name="Maven: javax.transaction:transaction-api:1.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hive:hive-serde:2.3.7" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hive:hive-shims:2.3.7" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hive.shims:hive-shims-common:2.3.7" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.apache.hive.shims:hive-shims-0.23:2.3.7" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.apache.hive.shims:hive-shims-scheduler:2.3.7" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hive:hive-llap-common:2.3.7" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hive:hive-llap-client:2.3.7" level="project" />
    <orderEntry type="library" name="Maven: commons-httpclient:commons-httpclient:3.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpclient:4.5.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpcore:4.4.10" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.jackson:jackson-mapper-asl:1.9.13" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.11" level="project" />
    <orderEntry type="library" name="Maven: org.jodd:jodd-core:3.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.datanucleus:datanucleus-core:4.1.17" level="project" />
    <orderEntry type="library" name="Maven: org.apache.thrift:libthrift:0.12.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.thrift:libfb303:0.9.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.derby:derby:10.14.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-yarn_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-yarn-api:2.7.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-yarn-common:2.7.4" level="project" />
    <orderEntry type="library" name="Maven: org.mortbay.jetty:jetty-util:6.1.26" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.jackson:jackson-jaxrs:1.9.13" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.jackson:jackson-xc:1.9.13" level="project" />
    <orderEntry type="library" name="Maven: com.google.inject.extensions:guice-servlet:3.0" level="project" />
    <orderEntry type="library" name="Maven: com.google.inject:guice:3.0" level="project" />
    <orderEntry type="library" name="Maven: javax.inject:javax.inject:1" level="project" />
    <orderEntry type="library" name="Maven: aopalliance:aopalliance:1.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-yarn-server-web-proxy:2.7.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-yarn-server-common:2.7.4" level="project" />
    <orderEntry type="library" name="Maven: org.mortbay.jetty:jetty:6.1.26" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-yarn-client:2.7.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-sql-kafka-0-10_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-token-provider-kafka-0-10_2.12:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kafka:kafka-clients:2.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-pool2:2.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.janino:janino:3.0.9" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.janino:commons-compiler:3.0.9" level="project" />
    <orderEntry type="library" name="Maven: joda-time:joda-time:2.9.9" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-common:3.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-annotations:3.2.0" level="project" />
    <orderEntry type="library" name="Maven: commons-cli:commons-cli:1.2" level="project" />
    <orderEntry type="library" name="Maven: commons-io:commons-io:2.5" level="project" />
    <orderEntry type="library" name="Maven: commons-collections:commons-collections:3.2.2" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty-server:9.4.12.v20180830" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty-http:9.4.12.v20180830" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty-io:9.4.12.v20180830" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty-util:9.4.12.v20180830" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty-servlet:9.4.12.v20180830" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty-security:9.4.12.v20180830" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty-webapp:9.4.12.v20180830" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty-xml:9.4.12.v20180830" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: javax.servlet.jsp:jsp-api:2.1" level="project" />
    <orderEntry type="library" name="Maven: com.sun.jersey:jersey-core:1.19" level="project" />
    <orderEntry type="library" name="Maven: javax.ws.rs:jsr311-api:1.1.1" level="project" />
    <orderEntry type="library" name="Maven: com.sun.jersey:jersey-servlet:1.19" level="project" />
    <orderEntry type="library" name="Maven: com.sun.jersey:jersey-json:1.19" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.jettison:jettison:1.1" level="project" />
    <orderEntry type="library" name="Maven: com.sun.xml.bind:jaxb-impl:2.2.3-1" level="project" />
    <orderEntry type="library" name="Maven: com.sun.jersey:jersey-server:1.19" level="project" />
    <orderEntry type="library" name="Maven: commons-logging:commons-logging:1.1.3" level="project" />
    <orderEntry type="library" name="Maven: commons-beanutils:commons-beanutils:1.9.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-configuration2:2.1.1" level="project" />
    <orderEntry type="library" name="Maven: com.google.re2j:re2j:1.1" level="project" />
    <orderEntry type="library" name="Maven: com.google.protobuf:protobuf-java:2.5.0" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.gson:gson:2.8.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-auth:3.2.0" level="project" />
    <orderEntry type="library" name="Maven: com.nimbusds:nimbus-jose-jwt:4.41.1" level="project" />
    <orderEntry type="library" name="Maven: com.github.stephenc.jcip:jcip-annotations:1.0-1" level="project" />
    <orderEntry type="library" name="Maven: net.minidev:json-smart:2.3" level="project" />
    <orderEntry type="library" name="Maven: net.minidev:accessors-smart:1.2" level="project" />
    <orderEntry type="library" name="Maven: org.ow2.asm:asm:5.0.4" level="project" />
    <orderEntry type="library" name="Maven: com.jcraft:jsch:0.1.54" level="project" />
    <orderEntry type="library" name="Maven: org.apache.curator:curator-client:2.12.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.htrace:htrace-core4:4.1.0-incubating" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-compress:1.4.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kerby:kerb-simplekdc:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kerby:kerb-client:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kerby:kerby-config:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kerby:kerb-core:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kerby:kerby-pkix:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kerby:kerby-asn1:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kerby:kerby-util:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kerby:kerb-common:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kerby:kerb-crypto:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kerby:kerb-util:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kerby:token-provider:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kerby:kerb-admin:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kerby:kerb-server:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kerby:kerb-identity:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kerby:kerby-xdr:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.woodstox:stax2-api:3.1.4" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.woodstox:woodstox-core:5.0.3" level="project" />
    <orderEntry type="library" name="Maven: dnsjava:dnsjava:2.1.7" level="project" />
    <orderEntry type="library" name="Maven: org.jetbrains:annotations:20.1.0" level="project" />
    <orderEntry type="library" name="Maven: com.google.guava:guava:20.0" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.11.2" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.11.2" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.11.2" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-scala_2.12:2.11.2" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-paranamer:2.9.7" level="project" />
    <orderEntry type="library" name="Maven: com.thoughtworks.paranamer:paranamer:2.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-configuration-processor:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-autoconfigure:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: javax.annotation:javax.annotation-api:1.3.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jcl:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.yaml:snakeyaml:1.23" level="project" />
    <orderEntry type="library" name="Maven: org.projectlombok:lombok:1.18.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-starter-test:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test-autoconfigure:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.jayway.jsonpath:json-path:2.4.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: junit:junit:4.12" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.assertj:assertj-core:3.11.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.mockito:mockito-core:2.23.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.bytebuddy:byte-buddy:1.9.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.bytebuddy:byte-buddy-agent:1.9.3" level="project" />
    <orderEntry type="library" name="Maven: org.objenesis:objenesis:2.6" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest-core:1.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest-library:1.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.skyscreamer:jsonassert:1.5.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.vaadin.external.google:android-json:0.0.20131108.vaadin1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework:spring-test:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.xmlunit:xmlunit-core:2.6.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-aop:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.aspectj:aspectjweaver:1.9.2" level="project" />
    <orderEntry type="library" name="Maven: com.github.pagehelper:pagehelper-spring-boot-starter:1.2.10" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis.spring.boot:mybatis-spring-boot-starter:1.3.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-jdbc:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jdbc:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis.spring.boot:mybatis-spring-boot-autoconfigure:1.3.2" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis:3.4.6" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis-spring:1.3.2" level="project" />
    <orderEntry type="library" name="Maven: com.github.pagehelper:pagehelper-spring-boot-autoconfigure:1.2.10" level="project" />
    <orderEntry type="library" name="Maven: com.github.pagehelper:pagehelper:5.1.8" level="project" />
    <orderEntry type="library" name="Maven: com.github.jsqlparser:jsqlparser:1.2" level="project" />
  </component>
</module>