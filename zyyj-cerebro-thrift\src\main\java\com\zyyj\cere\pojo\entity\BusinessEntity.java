package com.zyyj.cere.pojo.entity;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.sdk.processor.annotation.ThriftPaged;
import io.swagger.annotations.ApiModel;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 业务集
 */
@Setter
@ThriftStruct
@ThriftPaged
@NoArgsConstructor
@ToString
@Builder
@ApiModel(value = "BusinessEntity", description = "业务集")
@Entity
@Table(name = "business")
@EntityListeners(BusinessEntity.class)
public class BusinessEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, length = 11)
    private Integer id;

    /**
     * 主题域id
     */
    @Column(name = "subject_id", nullable = false, length = 11)
    private Integer subjectId;

    /**
     * 名称
     */
    @NotEmpty
    @Column(name = "name", nullable = false, length = 11)
    private String name = "";

    @ThriftConstructor
    public BusinessEntity(Integer id, Integer subjectId, @NotEmpty String name) {
        this.id = id;
        this.subjectId = subjectId;
        this.name = name;
    }

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public Integer getSubjectId() {
        return subjectId;
    }

    @ThriftField(3)
    public String getName() {
        return name;
    }
}
