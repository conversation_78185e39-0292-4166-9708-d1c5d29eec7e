package com.zyyj.cere.repository;

import com.zyyj.cere.pojo.entity.ApplicationEntity;
import com.zyyj.cere.pojo.entity.ApplicationTypeEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2020/11/12 14:46
 */
public interface ApplicationTypeRepository extends JpaRepository<ApplicationTypeEntity, Integer> {
    List<ApplicationTypeEntity> findByName(String name);

    @Query(value = "SELECT * FROM application_type at  WHERE at.id != ?1 AND at.name = ?2", nativeQuery = true)
    List<ApplicationTypeEntity> existByIdAndName(Integer id, String name);
}
