package com.zyyj.cere.pojo.entity;

import com.facebook.swift.codec.ThriftStruct;
import lombok.*;

import javax.persistence.*;

//kafka connector config : kcc
@ThriftStruct
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Entity
@Builder
@Table(name = "`kcc`")
@EntityListeners(TableTunnelEntity.class)
public class KccEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "`id`", nullable = false)
    private Long id;

    /* datasource 表 id*/
    @Column(name = "`datasource_id`", nullable = false)
    private Long datasourceId;

    /* 管道id */
    @Column(name = "`tunnel_id`", nullable = false)
    private Long tunnelId;

    /* 表中文名 */
    @Column(name = "`server_name`", nullable = false)
    @Builder.Default
    private String serverName="";

    /* 表中文名 */
    @Column(name = "`db_name`", nullable = false)
    @Builder.Default
    private String dbName="";

    /* 类型 1 source 入  2 think 出 */
    @Column(name = "`type`", nullable = false)
    private Integer type;

    /* 表中文名 */
    @Column(name = "`name`", nullable = false)
    @Builder.Default
    private String name="";

    /* 配置信息 */
    @Column(name = "`config`", nullable = false)
    @Builder.Default
    private String config="";

    /* 1 正常 2 停用 3 删除 */
    @Column(name = "`status`", nullable = false)
    @Builder.Default
    private Integer status=1;

    public Long getId() {
        return id;
    }

    public Long getDatasourceId() {
        return datasourceId;
    }

    public Long getTunnelId() {
        return tunnelId;
    }

    public String getServerName() {
        return serverName;
    }

    public String getDbName() {
        return dbName;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public String getConfig() {
        return config;
    }

    public Integer getStatus() {
        return status;
    }


    // 设置删除状态
    public void setDeleteStatus(){
        this.status = 3;
    }

}
