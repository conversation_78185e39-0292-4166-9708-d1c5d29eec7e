@echo off
echo ========================================
echo Stopping All ZYYJ Cerebro Services
echo ========================================

echo.
echo Stopping all Java processes...
taskkill /F /IM java.exe 2>nul

echo.
echo Checking if services are stopped...
timeout /t 3 /nobreak > nul

tasklist | findstr java.exe > nul
if %errorlevel% == 0 (
    echo Warning: Some Java processes are still running
    tasklist | findstr java.exe
) else (
    echo All Java services have been stopped successfully
)

echo.
echo ========================================
echo Service Stop Complete
echo ========================================

pause
