# 使用远程Spark集群启动Spark RPC服务

Write-Host "🚀 启动 Spark RPC 服务 (使用***************远程集群)..."

$env:JAVA_HOME = "C:\Program Files\Java\jdk1.8.0_141"

Write-Host "配置信息:"
Write-Host "- 远程Spark Master: spark://***************:7077"
Write-Host "- 远程HDFS: hdfs://***************:9000/"
Write-Host "- 远程Hive Metastore: thrift://***************:9083"
Write-Host "- 本地Thrift端口: 9009"
Write-Host "- 本地HTTP端口: 8081"

# 使用配置文件中的远程集群设置，不覆盖关键参数
& "$env:JAVA_HOME\bin\java" -Xms512m -Xmx1g -Dserver.port=8081 -Dspring.profiles.active=dev -Dspring.application.name=ZYYJ-SPARK-THRIFT -Deureka.instance.appname=ZYYJ-SPARK-THRIFT -Deureka.instance.non-secure-port=8081 -Deureka.client.serviceUrl.defaultZone=******************************************/eureka/ -Dspring.main.allow-bean-definition-overriding=true -Dzyyj.rpc.thrift.server.listen_port=9009 -jar "E:\dev\project\zyyj-cerebro\zyyj-spark-rpc-server\target\zyyj-spark-rpc-server.jar"
