package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.sdk.processor.annotation.ThriftPaged;
import lombok.*;

@ThriftStruct
@ThriftPaged
@Setter
@NoArgsConstructor
@ToString
@Builder
public class TableListDTO {

    private Long id;

    private String name;

    private String tableName;

    private Byte typeId;

    private Byte Status;

    private Byte source;

    private Integer tunnelId;

    private Integer modelingId;

    @ThriftConstructor
    public TableListDTO(Long id, String name, String tableName, Byte typeId, Byte status, Byte source, Integer tunnelId, Integer modelingId) {
        this.id = id;
        this.name = name;
        this.tableName = tableName;
        this.typeId = typeId;
        Status = status;
        this.source = source;
        this.tunnelId = tunnelId;
        this.modelingId = modelingId;
    }

    @ThriftField(1)
    public Long getId() {
        return id;
    }

    @ThriftField(2)
    public String getName() {
        return name;
    }

    @ThriftField(3)
    public String getTableName() {
        return tableName;
    }

    @ThriftField(4)
    public Byte getTypeId() {
        return typeId;
    }

    @ThriftField(5)
    public Byte getStatus() {
        return Status;
    }

    @ThriftField(6)
    public Byte getSource() {
        return source;
    }

    @ThriftField(7)
    public Integer getTunnelId() {
        return tunnelId;
    }

    @ThriftField(8)
    public Integer getModelingId() {
        return modelingId;
    }
}
