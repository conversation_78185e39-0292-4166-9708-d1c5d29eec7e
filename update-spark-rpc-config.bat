@echo off
echo 🔄 更新158服务器上的Spark RPC日志配置...

echo 1. 复制配置文件到158服务器...
scp -o StrictHostKeyChecking=no zyyj-spark-rpc-server\src\main\resources\log4j2-spring.xml root@***************:/tmp/

echo 2. 在158服务器上执行更新操作...
ssh -o StrictHostKeyChecking=no root@*************** "
echo '查找Spark RPC部署位置...'
SPARK_RPC_DIR=\$(find /opt -name '*spark-rpc*' -type d 2>/dev/null | head -1)
if [ -z \"\$SPARK_RPC_DIR\" ]; then
    echo '未找到Spark RPC部署目录，尝试其他位置...'
    SPARK_RPC_DIR=\$(find /home -name '*spark-rpc*' -type d 2>/dev/null | head -1)
fi

if [ -n \"\$SPARK_RPC_DIR\" ]; then
    echo \"找到Spark RPC目录: \$SPARK_RPC_DIR\"
    
    # 备份原配置文件
    if [ -f \"\$SPARK_RPC_DIR/log4j2-spring.xml\" ]; then
        cp \"\$SPARK_RPC_DIR/log4j2-spring.xml\" \"\$SPARK_RPC_DIR/log4j2-spring.xml.backup\"
        echo '已备份原配置文件'
    fi
    
    # 复制新配置文件
    cp /tmp/log4j2-spring.xml \"\$SPARK_RPC_DIR/\"
    echo '已更新配置文件'
    
    # 重启Spark RPC服务
    echo '正在重启Spark RPC服务...'
    PID=\$(ps aux | grep spark-rpc | grep -v grep | awk '{print \$2}')
    if [ -n \"\$PID\" ]; then
        echo \"停止进程 \$PID\"
        kill -15 \$PID
        sleep 5
        
        # 检查进程是否还在运行
        if ps -p \$PID > /dev/null; then
            echo '强制停止进程'
            kill -9 \$PID
        fi
    fi
    
    echo '配置文件已更新，请手动重启Spark RPC服务'
else
    echo '未找到Spark RPC部署目录'
    echo '请手动查找并更新配置文件'
fi
"

echo 3. 清理临时文件...
ssh -o StrictHostKeyChecking=no root@*************** "rm -f /tmp/log4j2-spring.xml"

echo ✅ 配置更新完成！
pause
