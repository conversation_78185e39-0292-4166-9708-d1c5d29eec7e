package com.zyyj.cere.repository;

import com.zyyj.cere.pojo.dto.ApiApplicationListDTO;
import com.zyyj.cere.pojo.entity.ApiApplicationEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/13 10:07
 */
public interface ApiApplicationRepository extends JpaRepository<ApiApplicationEntity, Integer> {

    @Query(value = "SELECT new com.zyyj.cere.pojo.dto.ApiApplicationListDTO(aa.id,a.name AS apiName,a.describe,ase.name AS serviceName,a.method) " +
            "FROM ApiApplicationEntity aa " +
            "LEFT JOIN ApiEntity a ON a.id = aa.apiId " +
            "LEFT JOIN ApiServiceEntity ase ON ase.id = a.serviceId " +
            "WHERE aa.applicationId = ?1")
    Page<ApiApplicationListDTO> findApiByApplicationId(Integer applicationId, Pageable pageable);

    List<ApiApplicationEntity> findByApplicationId(Integer applicationId);

}
