package com.zyyj.utils;

import com.zyyj.cere.exception.CereExceptionEnum;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.domain.util.PreChecks;

import java.util.regex.Pattern;

/**
 * 共用方法工具类
 */
public class PublicUtils {

    /**
     * 封装一层 CheckNull 只能检测参数是否为null
     *
     * @param reference
     * @param <T>
     */
    @SafeVarargs
    public static <T> void checkNotNullArr(T... reference) {
        for (T t : reference) {
            PreChecks.checkNotNull(t, CereExceptionEnum.PARAM_NOT_NULL);
        }
    }


    /**
     * 封装一层 checkNotEmpty  //检测字符串是否为null以及为""
     *
     * @param reference
     */
    public static void checkNotEmptyArr(String... reference) {
        for (String s : reference) {
            PreChecks.checkNotEmpty(s, CereExceptionEnum.PARAM_NOT_NULL);
        }
    }

    /**
     * 封装一层 检测int不能0
     *
     * @param value
     */
    public static void checkIntNotNull(Integer value) {
        if (value == null || value == 0) {
            throw new ApplicationException("参数错误");
        }
    }

    public static void checkLongNotNull(Long value) {
        if (value == null || value == 0) {
            throw new ApplicationException("参数错误");
        }
    }

    /**
     * @Description: 检验表名是否合法
     * @Param: [tableName]
     * @return: boolean
     * @Author: bravelee
     * @Date: 2020/11/25
     */
    public static boolean checkTableName(String tableName) {
        String regexp = "^[a-zA-Z]\\w{1,50}$";
        boolean isMatch = Pattern.matches(regexp, tableName);
        return isMatch;
    }


}
