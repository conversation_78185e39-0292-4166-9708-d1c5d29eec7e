package com.zyyj.cere.repository;

import com.zyyj.cere.pojo.entity.DatasetStandardEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Component;


@Component
public interface DatasetStandardRepository extends JpaRepository<DatasetStandardEntity, Integer>, JpaSpecificationExecutor<DatasetStandardEntity> {

    @Override
    @Query(value = "update dataset_standard set status = 1 where id = ?1",nativeQuery = true)
    @Modifying
    void deleteById(Integer id);
}