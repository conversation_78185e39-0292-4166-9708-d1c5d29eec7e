package com.zyyj.cere.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/17 18:26
 */

@ThriftService
public interface ApiTestService {
    @ThriftMethod
    Map<String, List<String>> getAPiData(String serverName, String apiName, Map<String, String> map);

    @ThriftMethod
    String postAPiData(String serverName, String apiName, Map<String, String> map);

    @ThriftMethod
    List<String> getSparkSqlRes(String sql);

}
