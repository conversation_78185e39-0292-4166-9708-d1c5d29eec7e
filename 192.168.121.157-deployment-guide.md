# *************** 服务器 Hadoop/Spark 部署指南

## 📋 概述

本指南提供了在***************服务器上部署和管理Hadoop/Spark集群的详细步骤，适用于生产环境部署。

## 🎯 目标架构

```
*************** (Ubuntu Server)
├── Hadoop 集群
│   ├── HDFS (端口: 9000, 9870)
│   └── YARN (端口: 8088)
├── Spark 集群
│   ├── Master (端口: 7077, 8080)
│   └── Worker
└── Hive Metastore (端口: 9083)
```

## 🔧 部署步骤

### 1. 准备工作

#### 1.1 上传脚本到服务器

```bash
# 将脚本文件上传到服务器
scp check-server-157.sh root@***************:/root/
scp install-hadoop-spark-157.sh root@***************:/root/
scp start-hadoop-spark-157.sh root@***************:/root/
scp stop-hadoop-spark-157.sh root@***************:/root/

# 设置执行权限
ssh root@*************** "chmod +x /root/*.sh"
```

#### 1.2 确保安装包已上传

确保以下安装包已上传到 `/root/` 目录：
- `hadoop-*.tar.gz` - Hadoop安装包
- `spark-*.tgz` - Spark安装包  
- `*hive*.tar.gz` - Hive安装包（可选）

### 2. 环境检查

```bash
# SSH到服务器
ssh root@***************

# 运行环境检查脚本
./check-server-157.sh
```

检查脚本会输出：
- 系统基本信息
- Java环境状态
- 安装包情况
- 已安装服务状态
- 运行中的进程
- 端口监听状态
- 配置建议

### 3. 安装服务

如果检查发现服务未安装，运行安装脚本：

```bash
# 运行安装脚本（需要root权限）
./install-hadoop-spark-157.sh
```

安装脚本会：
- 安装Java 8（如果未安装）
- 解压并安装Hadoop到 `/opt/hadoop`
- 解压并安装Spark到 `/opt/spark`
- 解压并安装Hive到 `/opt/hive`（如果有安装包）
- 配置基本的集群设置
- 设置环境变量
- 初始化Hadoop文件系统

### 4. 启动服务

```bash
# 启动所有服务
./start-hadoop-spark-157.sh
```

启动脚本会按顺序：
1. 设置环境变量
2. 检查环境
3. 停止现有服务
4. 启动Hadoop (HDFS + YARN)
5. 启动Spark (Master + Worker)
6. 启动Hive Metastore
7. 验证服务状态

### 5. 验证部署

#### 5.1 检查进程状态

```bash
# 查看Java进程
jps

# 应该看到以下进程：
# - NameNode (HDFS)
# - DataNode (HDFS)
# - ResourceManager (YARN)
# - NodeManager (YARN)
# - Master (Spark)
# - Worker (Spark)
```

#### 5.2 检查端口监听

```bash
# 检查关键端口
netstat -tlnp | grep -E ':7077|:8080|:9000|:9083|:8088|:9870'

# 应该看到所有端口都在监听
```

#### 5.3 访问Web界面

- **Spark Master**: http://***************:8080
- **HDFS Web UI**: http://***************:9870
- **YARN ResourceManager**: http://***************:8088

#### 5.4 测试HDFS

```bash
# 测试HDFS操作
hdfs dfs -ls /
hdfs dfs -mkdir /test
hdfs dfs -put /etc/hosts /test/
hdfs dfs -ls /test
```

#### 5.5 测试Spark

```bash
# 运行Spark示例
spark-submit --class org.apache.spark.examples.SparkPi \
    --master spark://$(hostname):7077 \
    $SPARK_HOME/examples/jars/spark-examples*.jar 10
```

## 🔄 日常管理

### 启动服务

```bash
./start-hadoop-spark-157.sh
```

### 停止服务

```bash
./stop-hadoop-spark-157.sh
```

### 检查状态

```bash
# 使用生成的状态检查脚本
./check-status.sh

# 或手动检查
jps
netstat -tlnp | grep -E ':7077|:8080|:9000|:9083|:8088|:9870'
```

### 查看日志

```bash
# Hadoop日志
tail -f /var/log/hadoop/*.log

# Spark日志
tail -f /var/log/spark/*.out

# Hive Metastore日志
tail -f /var/log/hive-metastore.log
```

## 🚨 故障排除

### 常见问题

#### 1. 服务启动失败

```bash
# 检查Java环境
java -version
echo $JAVA_HOME

# 检查环境变量
echo $HADOOP_HOME
echo $SPARK_HOME

# 检查磁盘空间
df -h

# 检查内存
free -h
```

#### 2. 端口冲突

```bash
# 查看端口占用
netstat -tlnp | grep :7077
lsof -i :7077

# 杀掉占用进程
kill -9 <PID>
```

#### 3. HDFS问题

```bash
# 重新格式化NameNode（注意：会删除所有数据）
hdfs namenode -format -force

# 检查HDFS状态
hdfs dfsadmin -report
```

#### 4. 权限问题

```bash
# 修复目录权限
chown -R root:root /opt/hadoop /opt/spark /opt/hive
chmod -R 755 /opt/hadoop /opt/spark /opt/hive
```

### 重置环境

如果需要完全重置环境：

```bash
# 停止所有服务
./stop-hadoop-spark-157.sh

# 清理数据目录
rm -rf /tmp/hadoop/*
rm -rf /tmp/spark/*

# 重新初始化
hdfs namenode -format -force

# 重新启动
./start-hadoop-spark-157.sh
```

## 📊 性能优化

### Hadoop配置优化

编辑 `/opt/hadoop/etc/hadoop/` 下的配置文件：

- `core-site.xml` - 核心配置
- `hdfs-site.xml` - HDFS配置
- `yarn-site.xml` - YARN配置
- `mapred-site.xml` - MapReduce配置

### Spark配置优化

编辑 `/opt/spark/conf/spark-defaults.conf`：

```properties
spark.executor.memory          2g
spark.executor.cores           2
spark.sql.adaptive.enabled     true
spark.sql.adaptive.coalescePartitions.enabled true
```

## 🔐 安全配置

### 防火墙设置

```bash
# 开放必要端口
ufw allow 7077  # Spark Master
ufw allow 8080  # Spark Web UI
ufw allow 9000  # HDFS NameNode
ufw allow 9083  # Hive Metastore
ufw allow 8088  # YARN ResourceManager
ufw allow 9870  # HDFS Web UI
```

### 用户权限

建议创建专门的hadoop用户而不是使用root：

```bash
# 创建hadoop用户
useradd -m -s /bin/bash hadoop
usermod -aG sudo hadoop

# 修改目录所有者
chown -R hadoop:hadoop /opt/hadoop /opt/spark /opt/hive
```

## 📝 维护计划

### 日常维护

- 每日检查服务状态
- 监控磁盘空间使用
- 检查日志文件大小
- 备份重要配置文件

### 定期维护

- 每周重启服务（可选）
- 每月清理临时文件
- 每季度更新安全补丁
- 每年评估硬件升级需求

## 📞 支持联系

如有问题，请检查：
1. 日志文件中的错误信息
2. 系统资源使用情况
3. 网络连接状态
4. 配置文件正确性

---

**注意**: 本指南适用于开发和测试环境。生产环境部署需要额外的安全和高可用性配置。
