package com.zyyj.spark.config;

import com.zyyj.spark.SparkRPCServerApplication;
import com.zyyj.spark.init.SparkSessionInit;
import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = SparkRPCServerApplication.class)
public class SparkConfigTest extends TestCase {

    @Autowired
    SparkConfig sparkConfig;

    @Test
    public void testGetSession() {
        sparkConfig.getSession();
    }
}