package com.zyyj.cere.pojo.entity;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.sdk.processor.annotation.ThriftPaged;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 数据源类型
 */
@ThriftStruct
@ThriftPaged
@Setter
@NoArgsConstructor
@ToString
@Builder
@ApiModel(value = "DatasourceEntity", description = "数据源类型")
@Table(name = "datasource_type")
@Entity
public class DatasourceTypeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, length = 11)
    @ApiModelProperty(name = "id", value = "ID")
    private Integer id;

    /**
     * 名称
     */
    @Column(name = "name", nullable = false, length = 20)
    @ApiModelProperty(name = "name", value = "名称")
    private String name = "";

    @ThriftConstructor
    public DatasourceTypeEntity(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public String getName() {
        return name;
    }
}

