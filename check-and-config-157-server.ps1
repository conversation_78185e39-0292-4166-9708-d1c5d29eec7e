# =============================================================================
# 检查和配置***************服务器的Hadoop/Spark环境
# =============================================================================

param(
    [string]$ServerIP = "***************",
    [string]$Username = "root",
    [switch]$Help
)

if ($Help) {
    Write-Host "用法: .\check-and-config-157-server.ps1 [-ServerIP <IP>] [-Username <用户名>]"
    Write-Host ""
    Write-Host "参数:"
    Write-Host "  -ServerIP  服务器IP地址 (默认: ***************)"
    Write-Host "  -Username  SSH用户名 (默认: root)"
    Write-Host "  -Help      显示帮助信息"
    exit 0
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colors = @{
        "Red" = "Red"
        "Green" = "Green" 
        "Yellow" = "Yellow"
        "Blue" = "Blue"
        "White" = "White"
    }
    
    Write-Host $Message -ForegroundColor $colors[$Color]
}

function Log-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Green"
}

function Log-Warn {
    param([string]$Message)
    Write-ColorOutput "[WARN] $Message" "Yellow"
}

function Log-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

function Log-Step {
    param([string]$Message)
    Write-ColorOutput "[STEP] $Message" "Blue"
}

# 执行SSH命令
function Invoke-SSHCommand {
    param(
        [string]$Command,
        [string]$Description = ""
    )
    
    if ($Description) {
        Log-Info $Description
    }
    
    try {
        # 使用plink或ssh命令（根据系统可用性）
        $result = ssh "${Username}@${ServerIP}" "$Command" 2>&1
        return $result
    } catch {
        Log-Error "SSH命令执行失败: $($_.Exception.Message)"
        return $null
    }
}

# 检查SSH连接
function Test-SSHConnection {
    Log-Step "测试SSH连接..."
    
    $result = Invoke-SSHCommand "echo 'SSH连接测试成功'" "测试SSH连接"
    if ($result -and $result -like "*SSH连接测试成功*") {
        Log-Info "✅ SSH连接正常"
        return $true
    } else {
        Log-Error "❌ SSH连接失败"
        Log-Info "请确保:"
        Log-Info "1. SSH密钥已配置或可以密码登录"
        Log-Info "2. 服务器IP地址正确: $ServerIP"
        Log-Info "3. 用户名正确: $Username"
        return $false
    }
}

# 检查系统信息
function Check-SystemInfo {
    Log-Step "检查系统信息..."
    
    $osInfo = Invoke-SSHCommand "cat /etc/os-release | grep PRETTY_NAME" "获取操作系统信息"
    $javaInfo = Invoke-SSHCommand "java -version 2>&1 | head -n 1" "检查Java版本"
    $memInfo = Invoke-SSHCommand "free -h | grep Mem" "检查内存信息"
    $diskInfo = Invoke-SSHCommand "df -h /" "检查磁盘空间"
    
    Write-Host ""
    Write-Host "=== 系统信息 ==="
    Write-Host "操作系统: $osInfo"
    Write-Host "Java版本: $javaInfo"
    Write-Host "内存信息: $memInfo"
    Write-Host "磁盘空间: $diskInfo"
}

# 检查Hadoop安装
function Check-HadoopInstallation {
    Log-Step "检查Hadoop安装..."
    
    # 检查/root目录下的Hadoop包
    $hadoopPackages = Invoke-SSHCommand "ls -la /root/ | grep -i hadoop" "检查/root目录下的Hadoop包"
    if ($hadoopPackages) {
        Log-Info "发现Hadoop安装包:"
        Write-Host $hadoopPackages
    } else {
        Log-Warn "未在/root目录找到Hadoop安装包"
    }
    
    # 检查是否已安装Hadoop
    $hadoopVersion = Invoke-SSHCommand "hadoop version 2>/dev/null | head -n 1" "检查已安装的Hadoop"
    if ($hadoopVersion -and $hadoopVersion -like "*Hadoop*") {
        Log-Info "✅ Hadoop已安装: $hadoopVersion"
        
        # 检查Hadoop配置
        $hadoopHome = Invoke-SSHCommand "echo `$HADOOP_HOME" "获取HADOOP_HOME"
        Log-Info "HADOOP_HOME: $hadoopHome"
        
        # 检查Hadoop进程
        $hadoopProcesses = Invoke-SSHCommand "jps | grep -E 'NameNode|DataNode|ResourceManager|NodeManager'" "检查Hadoop进程"
        if ($hadoopProcesses) {
            Log-Info "✅ Hadoop进程运行中:"
            Write-Host $hadoopProcesses
        } else {
            Log-Warn "❌ Hadoop进程未运行"
        }
    } else {
        Log-Warn "❌ Hadoop未安装或未配置环境变量"
    }
}

# 检查Spark安装
function Check-SparkInstallation {
    Log-Step "检查Spark安装..."
    
    # 检查Spark版本
    $sparkVersion = Invoke-SSHCommand "spark-submit --version 2>&1 | grep version" "检查Spark版本"
    if ($sparkVersion) {
        Log-Info "✅ Spark已安装: $sparkVersion"
        
        # 检查Spark配置
        $sparkHome = Invoke-SSHCommand "echo `$SPARK_HOME" "获取SPARK_HOME"
        Log-Info "SPARK_HOME: $sparkHome"
        
        # 检查Spark进程
        $sparkProcesses = Invoke-SSHCommand "jps | grep -E 'Master|Worker'" "检查Spark进程"
        if ($sparkProcesses) {
            Log-Info "✅ Spark进程运行中:"
            Write-Host $sparkProcesses
        } else {
            Log-Warn "❌ Spark进程未运行"
        }
        
        # 检查Spark配置文件
        $sparkConf = Invoke-SSHCommand "ls -la `$SPARK_HOME/conf/ 2>/dev/null" "检查Spark配置文件"
        if ($sparkConf) {
            Log-Info "Spark配置目录内容:"
            Write-Host $sparkConf
        }
    } else {
        Log-Warn "❌ Spark未安装或未配置环境变量"
    }
}

# 检查端口监听状态
function Check-PortStatus {
    Log-Step "检查关键端口状态..."
    
    $ports = @(
        @{Port=7077; Name="Spark Master"},
        @{Port=8080; Name="Spark Web UI"},
        @{Port=9000; Name="HDFS NameNode"},
        @{Port=9083; Name="Hive Metastore"},
        @{Port=8088; Name="YARN ResourceManager"},
        @{Port=50070; Name="HDFS Web UI (旧版)"},
        @{Port=9870; Name="HDFS Web UI (新版)"}
    )
    
    foreach ($portInfo in $ports) {
        $listening = Invoke-SSHCommand "netstat -tlnp | grep :$($portInfo.Port)" "检查端口$($portInfo.Port)"
        if ($listening) {
            Log-Info "✅ $($portInfo.Name) (端口 $($portInfo.Port)) - 正在监听"
        } else {
            Log-Warn "❌ $($portInfo.Name) (端口 $($portInfo.Port)) - 未监听"
        }
    }
}

# 生成Hadoop/Spark启动脚本
function Generate-StartupScripts {
    Log-Step "生成Hadoop/Spark启动脚本..."
    
    # 创建启动脚本
    $startupScript = @'
#!/bin/bash

# Hadoop/Spark 集群启动脚本

echo "=== 启动Hadoop/Spark集群 ==="

# 设置环境变量
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export HADOOP_HOME=/opt/hadoop
export SPARK_HOME=/opt/spark
export PATH=$JAVA_HOME/bin:$HADOOP_HOME/bin:$HADOOP_HOME/sbin:$SPARK_HOME/bin:$SPARK_HOME/sbin:$PATH

echo "环境变量设置完成"

# 启动Hadoop
echo "启动Hadoop..."
if [ -d "$HADOOP_HOME" ]; then
    echo "启动HDFS..."
    $HADOOP_HOME/sbin/start-dfs.sh
    
    echo "启动YARN..."
    $HADOOP_HOME/sbin/start-yarn.sh
    
    echo "等待Hadoop服务启动..."
    sleep 10
else
    echo "警告: HADOOP_HOME不存在: $HADOOP_HOME"
fi

# 启动Spark
echo "启动Spark..."
if [ -d "$SPARK_HOME" ]; then
    echo "启动Spark Master..."
    $SPARK_HOME/sbin/start-master.sh
    
    echo "启动Spark Worker..."
    $SPARK_HOME/sbin/start-worker.sh spark://$(hostname):7077
    
    echo "等待Spark服务启动..."
    sleep 10
else
    echo "警告: SPARK_HOME不存在: $SPARK_HOME"
fi

# 启动Hive Metastore (如果需要)
echo "启动Hive Metastore..."
if command -v hive &> /dev/null; then
    nohup hive --service metastore > /var/log/hive-metastore.log 2>&1 &
    echo "Hive Metastore启动完成"
else
    echo "警告: Hive未安装"
fi

echo "=== 启动完成 ==="
echo "检查服务状态:"
jps

echo ""
echo "Web界面:"
echo "Spark Master: http://$(hostname -I | awk '{print $1}'):8080"
echo "HDFS: http://$(hostname -I | awk '{print $1}'):9870"
echo "YARN: http://$(hostname -I | awk '{print $1}'):8088"
'@

    # 上传启动脚本
    $tempFile = [System.IO.Path]::GetTempFileName()
    $startupScript | Out-File -FilePath $tempFile -Encoding UTF8
    
    try {
        # 使用scp上传文件
        scp $tempFile "${Username}@${ServerIP}:/tmp/start-hadoop-spark.sh"
        
        # 设置执行权限并移动到合适位置
        Invoke-SSHCommand "chmod +x /tmp/start-hadoop-spark.sh && mv /tmp/start-hadoop-spark.sh /root/" "设置启动脚本权限"
        
        Log-Info "✅ 启动脚本已创建: /root/start-hadoop-spark.sh"
        
        Remove-Item $tempFile -Force
    } catch {
        Log-Error "上传启动脚本失败: $($_.Exception.Message)"
        Remove-Item $tempFile -Force
    }
}

# 生成配置建议
function Generate-ConfigRecommendations {
    Log-Step "生成配置建议..."
    
    Write-Host ""
    Write-Host "=== 配置建议 ==="
    Write-Host ""
    
    Write-Host "1. 如果Hadoop未安装，请执行:"
    Write-Host "   ssh $Username@$ServerIP"
    Write-Host "   cd /root"
    Write-Host "   tar -xzf hadoop-*.tar.gz"
    Write-Host "   mv hadoop-* /opt/hadoop"
    Write-Host "   export HADOOP_HOME=/opt/hadoop"
    Write-Host ""
    
    Write-Host "2. 如果Spark未安装，请执行:"
    Write-Host "   tar -xzf spark-*.tgz"
    Write-Host "   mv spark-* /opt/spark"
    Write-Host "   export SPARK_HOME=/opt/spark"
    Write-Host ""
    
    Write-Host "3. 启动集群:"
    Write-Host "   ssh $Username@$ServerIP '/root/start-hadoop-spark.sh'"
    Write-Host ""
    
    Write-Host "4. 检查服务状态:"
    Write-Host "   ssh $Username@$ServerIP 'jps'"
    Write-Host ""
    
    Write-Host "5. Web界面访问:"
    Write-Host "   Spark Master: http://$ServerIP:8080"
    Write-Host "   HDFS: http://$ServerIP:9870"
    Write-Host "   YARN: http://$ServerIP:8088"
}

# 主函数
function Main {
    Log-Info "检查和配置***************服务器的Hadoop/Spark环境"
    Log-Info "服务器: $ServerIP"
    Log-Info "用户: $Username"
    
    # 测试SSH连接
    if (-not (Test-SSHConnection)) {
        return
    }
    
    # 检查系统信息
    Check-SystemInfo
    
    # 检查Hadoop安装
    Check-HadoopInstallation
    
    # 检查Spark安装
    Check-SparkInstallation
    
    # 检查端口状态
    Check-PortStatus
    
    # 生成启动脚本
    Generate-StartupScripts
    
    # 生成配置建议
    Generate-ConfigRecommendations
    
    Log-Info ""
    Log-Info "检查完成！请根据上述建议配置和启动服务。"
}

# 执行主函数
Main
