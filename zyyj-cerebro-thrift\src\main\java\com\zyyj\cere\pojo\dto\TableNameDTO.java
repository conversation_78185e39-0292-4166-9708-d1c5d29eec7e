package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2020/12/2 17:10
 */
@ThriftStruct
@Setter
@NoArgsConstructor
public class TableNameDTO {
    private String name;
    private String tableName;

    @ThriftConstructor
    public TableNameDTO(String name, String tableName) {
        this.name = name;
        this.tableName = tableName;
    }

    @ThriftField(1)
    public String getName() {
        return name;
    }

    @ThriftField(2)
    public String getTableName() {
        return tableName;
    }
}
