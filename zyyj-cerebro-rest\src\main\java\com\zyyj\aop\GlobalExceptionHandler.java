package com.zyyj.aop;

import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.domain.web.pojo.vo.RespStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RestControllerAdvice(basePackages = {"com.zyyj.controller"})
@Component("cerebroGlobalExceptionHandler")  // Specify a different bean name
public class GlobalExceptionHandler {

    /**
     * 处理 ApplicationException 异常
     */
    @ExceptionHandler(ApplicationException.class)
    public ResponseEntity<RespStatus<String>> handleApplicationException(ApplicationException e) {
        log.error("业务异常: {}", e.getMessage(), e);
        
        // 检查是否是服务连接相关的异常
        if (isServiceConnectionError(e)) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(new RespStatus<>("SERVICE_UNAVAILABLE", "服务暂时不可用，请稍后重试"));
        }
        
        // 其他业务异常
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(new RespStatus<>("BUSINESS_ERROR", e.getMessage()));
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<RespStatus<String>> handleGenericException(Exception e) {
        log.error("系统异常: {}", e.getMessage(), e);
        
        // 检查是否是 Eureka 连接异常
        if (isEurekaConnectionError(e)) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(new RespStatus<>("EUREKA_UNAVAILABLE", "服务注册中心连接失败，请联系管理员"));
        }
        
        // 检查是否是 RPC 调用异常
        if (isRpcCallError(e)) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(new RespStatus<>("RPC_UNAVAILABLE", "后端服务暂时不可用，请稍后重试"));
        }
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new RespStatus<>("INTERNAL_ERROR", "系统内部错误，请联系管理员"));
    }

    /**
     * 判断是否是服务连接错误
     */
    private boolean isServiceConnectionError(ApplicationException e) {
        String message = e.getMessage();
        return message != null && (
                message.contains("Connection refused") ||
                message.contains("Connection timed out") ||
                message.contains("No available server") ||
                message.contains("服务不可用")
        );
    }

    /**
     * 判断是否是 Eureka 连接错误
     */
    private boolean isEurekaConnectionError(Exception e) {
        String message = e.getMessage();
        Throwable cause = e.getCause();
        
        return (message != null && message.contains("eureka")) ||
               (cause != null && cause.getMessage() != null && 
                (cause.getMessage().contains("Connection timed out") ||
                 cause.getMessage().contains("Cannot execute request on any known server")));
    }

    /**
     * 判断是否是 RPC 调用错误
     */
    private boolean isRpcCallError(Exception e) {
        String message = e.getMessage();
        String className = e.getClass().getSimpleName();
        
        return className.contains("Thrift") ||
               className.contains("RPC") ||
               (message != null && (
                   message.contains("thrift") ||
                   message.contains("rpc") ||
                   message.contains("TTransportException")
               ));
    }
}
