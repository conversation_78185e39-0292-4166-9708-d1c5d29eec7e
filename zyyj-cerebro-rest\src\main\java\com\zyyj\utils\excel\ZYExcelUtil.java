package com.zyyj.utils.excel;



import com.zyyj.domain.exception.ApplicationException;
import lombok.SneakyThrows;
import org.apache.http.client.utils.DateUtils;
import org.apache.poi.poifs.filesystem.FileMagic;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.StringUtils;

import java.io.*;
import java.net.URL;
import java.text.DecimalFormat;
import java.util.*;



public class ZYExcelUtil {

    private String DESC_KEY_WORDS = "#描述#"; //描述文字
    private String CSV = "CSV";

    private static FormulaEvaluator evaluator;

    public static ZYExcelUtil builder() {
        ZYExcelUtil util = new ZYExcelUtil();
        return util;
    }

    @SneakyThrows
    public List<ZYTableData> getData(String url)  {
        URL u = new URL(url);
        InputStream inputStream = u.openStream();

        //获取文件的后缀名 .jpg
        String suffix = url.substring(url.lastIndexOf("."));
        if (suffix.contains(CSV)) {
            List<ZYTableData> excelData = new ArrayList<ZYTableData>();
            ZYTableData data = ZYCSVUtil.builder().getData(inputStream);
            excelData.add(data);
            return excelData;
        } else {
            return getExcelData(inputStream);
        }
    }

    @SneakyThrows
    public List<ZYTableData> getExcelData(String url)  {
        URL u = new URL(url);
        InputStream inputStream = u.openStream();
        return getExcelData(inputStream);
    }

    @SneakyThrows
    public List<ZYTableData> getExcelData(InputStream in)  {

        List<ZYTableData> excelData = new ArrayList<ZYTableData>();
        // 创建excel工作簿
        Workbook work = getWorkbook(in);
        if (null == work) {
            throw new ApplicationException("创建Excel工作薄为空！");
        }

        evaluator=work.getCreationHelper().createFormulaEvaluator();

        for (int i = 0; i < work.getNumberOfSheets(); i++) {

            Sheet sheet = work.getSheetAt(i);
            if (sheet == null) {
                continue;
            }

            ZYTableData data = new ZYTableData();
            data.index = i;
            data.name = sheet.getSheetName();

            //判断第一行是否存在描述头
            for (int j = 0; j < sheet.getRow(sheet.getFirstRowNum()).getLastCellNum(); j++) {
                Cell cell = sheet.getRow(sheet.getFirstRowNum()).getCell(j);
                if (cell.toString().contains(DESC_KEY_WORDS)){
                    sheet.removeRow(sheet.getRow(sheet.getFirstRowNum()));
                    break;
                }
            }

            // 遍历所有行
            for (int j = sheet.getFirstRowNum(); j <= sheet.getLastRowNum(); j++) {

                List<String> rowData = new ArrayList<String>();
                Row row = sheet.getRow(j);
                row.forEach( cell -> {
                    String v = this.getCellValueByCell(cell);
                    rowData.add(v);
                } );

                data.addRowData(rowData);
            }

            // 添加数据
            excelData.add(data);
        }

        work.close();
        return excelData;
    }

    private Workbook getWorkbook(InputStream in) throws Exception {
        Workbook book = null;
        book = new XSSFWorkbook(in);
        return book;
    }

    /// 是否是excel文件
    public boolean isExcelFile(InputStream inputStream) {
        boolean result = false;
        try {
            FileMagic fileMagic = FileMagic.valueOf(inputStream);
            if (Objects.equals(fileMagic, FileMagic.OLE2)
                    || Objects.equals(fileMagic, FileMagic.OOXML)) {
                result = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }



    //获取单元格各类型值，返回字符串类型
    private String getCellValueByCell(Cell cell) {
        //判断是否为null或空串
        if (cell==null || cell.toString().trim().equals("")) {
            return "";
        }
        String cellValue = "";
        CellType cellType=cell.getCellType();
        if(cellType==CellType.FORMULA){ //表达式类型
            cellType=evaluator.evaluate(cell).getCellType();
        }
        switch (cellType) {
            case STRING: //字符串类型
                cellValue= cell.getStringCellValue().trim();
                cellValue= StringUtils.isEmpty(cellValue) ? "" : cellValue;
                break;
            case BOOLEAN:  //布尔类型
                cellValue = String.valueOf(cell.getBooleanCellValue());
                break;
            case NUMERIC: //数值类型
                if (DateUtil.isCellDateFormatted(cell)) {  //判断日期类型
                    cellValue = DateUtils.formatDate(cell.getDateCellValue(), "yyyy-MM-dd hh:mm:ss");
                } else {  //否
                    cellValue = new DecimalFormat("#.######").format(cell.getNumericCellValue());
                }
                break;
            default: //其它类型，取空串吧
                cellValue = cell.toString();
                break;
        }
        return cellValue;
    }

}
