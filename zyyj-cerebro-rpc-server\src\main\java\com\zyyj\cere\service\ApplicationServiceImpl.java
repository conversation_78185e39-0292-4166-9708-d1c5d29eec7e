package com.zyyj.cere.service;

import com.zyyj.cere.exception.CereExceptionEnum;
import com.zyyj.cere.pojo.dto.ApiApplicationListDTO;
import com.zyyj.cere.pojo.dto.PagedAppApiDTO;
import com.zyyj.cere.pojo.dto.PagedApplicationDTO;
import com.zyyj.cere.pojo.entity.ApiApplicationEntity;
import com.zyyj.cere.pojo.entity.ApplicationEntity;
import com.zyyj.cere.pojo.entity.ApplicationTypeEntity;
import com.zyyj.cere.repository.ApiApplicationRepository;
import com.zyyj.cere.repository.ApplicationRepository;
import com.zyyj.cere.repository.ApplicationTypeRepository;
import com.zyyj.cere.utils.PublicUtils;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.domain.pagination.Paging;
import com.zyyj.rpc.thrift.server.ThriftServiceHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/11/10 17:20
 */
@Slf4j
@Service
@ThriftServiceHandler
public class ApplicationServiceImpl implements ApplicationService {

    @Autowired
    ApplicationRepository applicationRepository;
    @Autowired
    ApplicationTypeRepository applicationTypeRepository;
    @Autowired
    ApiApplicationRepository apiApplicationRepository;


    @Override
    public PagedApplicationDTO getApplicationList(String key, Integer typeId, Paging paging) {
        PageRequest page = PageRequest.of(paging.getPage() - 1, paging.getSize());
        Page<ApplicationEntity> pages;
        if (typeId == 0) {//全部类型
            pages = applicationRepository.findByNameContaining(key, page);
        } else {
            pages = applicationRepository.findByTypeIdAndNameContaining(typeId, key, page);
        }
        return new PagedApplicationDTO(pages);
    }

    @Override
    public ApplicationEntity getApplicationDetail(Integer id) {

        return applicationRepository.findById(id).get();
    }

    @Override
    public String addApplication(ApplicationEntity applicationEntity) {

        //名称查重
        List<ApplicationEntity> list = applicationRepository.findByName(applicationEntity.getName());
        if (list != null && list.size() > 0) {
            return "该应用名称已存在";
        }
        //生成唯一app_id
        String appId = "";
        boolean tag = true;
        while (tag) {
            appId = PublicUtils.getRandomString(3, 17);
            ApplicationEntity res = applicationRepository.findByAppId(appId);
            if (res == null) {
                tag = false;
            }
        }

        String appSecret = "";
        //生成唯一app_secret
        tag = true;
        while (tag) {
            appSecret = PublicUtils.getRandomString(3, 34);
            ApplicationEntity res = applicationRepository.findByAppSecret(appSecret);
            if (res == null) {
                tag = false;
            }
        }
        applicationEntity.setAppId(appId);
        applicationEntity.setAppSecret(appSecret);

        ApplicationEntity res = applicationRepository.saveAndFlush(applicationEntity);
        if (res == null) {
            throw new ApplicationException(CereExceptionEnum.ERROR_ADD);
        }
        return "";
    }

    @Transactional
    @Override
    public String editApplication(ApplicationEntity applicationEntity) {
        //检验应用是否存在
        boolean exist = applicationRepository.existsById(applicationEntity.getId());
        if (!exist) {
            return "该应用已被删除,请刷新页面";
        }
        //名称查重
        List<ApplicationEntity> list = applicationRepository.existByIdAndName(applicationEntity.getId(), applicationEntity.getName());
        if (list != null && list.size() > 0) {
            return "该应用名称已存在";
        }

        applicationRepository.updateApplicationById(applicationEntity.getName(), applicationEntity.getDescribe(), applicationEntity.getTypeId(), applicationEntity.getId());

        return "";
    }

    @Override
    public String delApplication(Integer id) {
        //检验应用是否存在
        boolean exist = applicationRepository.existsById(id);
        if (!exist) {
            return "该应用已被删除,请刷新页面";
        }
        applicationRepository.deleteById(id);
        return "";
    }

    @Override
    public Map<String, List<ApplicationTypeEntity>> getApplicationTypes() {

        List<ApplicationTypeEntity> list = applicationTypeRepository.findAll();

        Map<String, List<ApplicationTypeEntity>> data = new HashMap<>();
        data.put("list", list);
        return data;
    }

    @Override
    public String addApplicationType(ApplicationTypeEntity applicationTypeEntity) {
        //名称查重
        List<ApplicationTypeEntity> list = applicationTypeRepository.findByName(applicationTypeEntity.getName());
        if (list != null && list.size() > 0) {
            return "该应用分类名称已存在";
        }
        ApplicationTypeEntity res = applicationTypeRepository.saveAndFlush(applicationTypeEntity);
        if (res == null) {
            throw new ApplicationException(CereExceptionEnum.ERROR_ADD);
        }
        return "";
    }

    @Override
    public String editApplicationType(ApplicationTypeEntity applicationTypeEntity) {

        //检验应用分类是否存在
        ApplicationTypeEntity res = applicationTypeRepository.findById(applicationTypeEntity.getId()).get();
        if (res == null) {
            return "该应用分类已被删除,请刷新页面";
        }

        //名称查重
        List<ApplicationTypeEntity> list = applicationTypeRepository.existByIdAndName(applicationTypeEntity.getId(), applicationTypeEntity.getName());
        if (list != null && list.size() > 0) {
            return "该应用分类名称已存在";
        }
        res = applicationTypeRepository.saveAndFlush(applicationTypeEntity);
        if (res == null) {
            throw new ApplicationException(CereExceptionEnum.ERROR_EDIT);
        }
        return "";
    }

    @Override
    public String delApplicationType(Integer id) {
        //检验应用是否存在
        boolean exist = applicationTypeRepository.existsById(id);
        if (!exist) {
            return "该应用已被删除,请刷新页面";
        }
        //查询分类下是否还存在应用
        List<ApplicationEntity> list = applicationRepository.findByTypeId(id);
        if (list != null && list.size() > 0) {
            return "该分类下还存在应用不可删除";
        }

        applicationTypeRepository.deleteById(id);
        return "";
    }

    @Override
    public PagedAppApiDTO getApiApplicationList(Integer applicationId, Paging paging) {
        PageRequest page = PageRequest.of(paging.getPage() - 1, paging.getSize());
        Page<ApiApplicationListDTO> pages = apiApplicationRepository.findApiByApplicationId(applicationId, page);

        return new PagedAppApiDTO(pages);
    }

    @Override
    public void setApplicationApi(Integer applicationId, String apiIds) {
        List<ApiApplicationEntity> listAll = apiApplicationRepository.findByApplicationId(applicationId);
        Map<String, Integer> map = new HashMap<>();
        if (listAll.size() > 0) {
            for (ApiApplicationEntity entity : listAll) {
                map.put(entity.getApiId() + "", 1);
            }
        }
        String[] apiIdsArr = apiIds.split(",");
        List<ApiApplicationEntity> list = new ArrayList<>();
        for (String id : apiIdsArr) {
            if (!map.containsKey(id)) {
                ApiApplicationEntity model = new ApiApplicationEntity();
                model.setApiId(Integer.parseInt(id));
                model.setApplicationId(applicationId);
                list.add(model);
            }
        }
        if (list.size() == 0) {
            return;
        }

        apiApplicationRepository.saveAll(list);
    }

    @Override
    public void delApplicationApi(Integer id) {
        apiApplicationRepository.deleteById(id);
    }


}
