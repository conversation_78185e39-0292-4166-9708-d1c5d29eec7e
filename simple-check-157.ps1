# =============================================================================
# 简化版***************服务器检查脚本
# =============================================================================

param(
    [string]$ServerIP = "***************"
)

function Log-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Log-Warn {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Log-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Log-Step {
    param([string]$Message)
    Write-Host "[STEP] $Message" -ForegroundColor Blue
}

# 检查端口连接
function Test-ServerPorts {
    Log-Step "检查服务器端口状态..."
    
    $ports = @(
        @{Port=22; Name="SSH"},
        @{Port=7077; Name="Spark Master"},
        @{Port=8080; Name="Spark Web UI"},
        @{Port=9000; Name="HDFS NameNode"},
        @{Port=9083; Name="Hive Metastore"},
        @{Port=8088; Name="YARN ResourceManager"},
        @{Port=9870; Name="HDFS Web UI"}
    )
    
    foreach ($portInfo in $ports) {
        try {
            $result = Test-NetConnection -ComputerName $ServerIP -Port $portInfo.Port -InformationLevel Quiet -WarningAction SilentlyContinue
            if ($result) {
                Log-Info "✅ $($portInfo.Name) (端口 $($portInfo.Port)) - 连接正常"
            } else {
                Log-Warn "❌ $($portInfo.Name) (端口 $($portInfo.Port)) - 连接失败"
            }
        } catch {
            Log-Error "❌ $($portInfo.Name) (端口 $($portInfo.Port)) - 测试异常"
        }
    }
}

# 检查Web界面
function Test-WebInterfaces {
    Log-Step "检查Web界面..."
    
    $webUrls = @(
        @{Url="http://$ServerIP:8080"; Name="Spark Master Web UI"},
        @{Url="http://$ServerIP:9870"; Name="HDFS Web UI"},
        @{Url="http://$ServerIP:8088"; Name="YARN Web UI"}
    )
    
    foreach ($urlInfo in $webUrls) {
        try {
            $response = Invoke-WebRequest -Uri $urlInfo.Url -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Log-Info "✅ $($urlInfo.Name) - 可访问"
            } else {
                Log-Warn "❌ $($urlInfo.Name) - 状态码: $($response.StatusCode)"
            }
        } catch {
            Log-Warn "❌ $($urlInfo.Name) - 无法访问"
        }
    }
}

# 生成SSH命令建议
function Generate-SSHCommands {
    Log-Step "生成SSH检查命令..."
    
    Write-Host ""
    Write-Host "=== 手动SSH检查命令 ==="
    Write-Host "请在命令行中执行以下命令来检查服务器状态:"
    Write-Host ""
    
    Write-Host "1. 连接到服务器:"
    Write-Host "   ssh root@$ServerIP"
    Write-Host ""
    
    Write-Host "2. 检查Java环境:"
    Write-Host "   java -version"
    Write-Host ""
    
    Write-Host "3. 检查/root目录下的安装包:"
    Write-Host "   ls -la /root/ | grep -E 'hadoop|spark'"
    Write-Host ""
    
    Write-Host "4. 检查已安装的服务:"
    Write-Host "   which hadoop"
    Write-Host "   which spark-submit"
    Write-Host "   echo `$HADOOP_HOME"
    Write-Host "   echo `$SPARK_HOME"
    Write-Host ""
    
    Write-Host "5. 检查运行中的Java进程:"
    Write-Host "   jps"
    Write-Host ""
    
    Write-Host "6. 检查端口监听:"
    Write-Host "   netstat -tlnp | grep -E '7077|8080|9000|9083|8088|9870'"
    Write-Host ""
    
    Write-Host "7. 如果需要启动Hadoop:"
    Write-Host "   `$HADOOP_HOME/sbin/start-dfs.sh"
    Write-Host "   `$HADOOP_HOME/sbin/start-yarn.sh"
    Write-Host ""
    
    Write-Host "8. 如果需要启动Spark:"
    Write-Host "   `$SPARK_HOME/sbin/start-master.sh"
    Write-Host "   `$SPARK_HOME/sbin/start-worker.sh spark://`$(hostname):7077"
    Write-Host ""
    
    Write-Host "9. 如果需要启动Hive Metastore:"
    Write-Host "   nohup hive --service metastore > /var/log/hive-metastore.log 2>&1 &"
}

# 生成配置建议
function Generate-ConfigSuggestions {
    Log-Step "生成配置建议..."
    
    Write-Host ""
    Write-Host "=== 配置建议 ==="
    Write-Host ""
    
    Write-Host "基于检查结果，建议按以下步骤配置:"
    Write-Host ""
    
    Write-Host "1. 如果Hadoop未安装:"
    Write-Host "   - 解压/root目录下的Hadoop安装包"
    Write-Host "   - 配置HADOOP_HOME环境变量"
    Write-Host "   - 配置Hadoop集群"
    Write-Host ""
    
    Write-Host "2. 如果Spark未安装:"
    Write-Host "   - 解压/root目录下的Spark安装包"
    Write-Host "   - 配置SPARK_HOME环境变量"
    Write-Host "   - 配置Spark集群"
    Write-Host ""
    
    Write-Host "3. 启动顺序:"
    Write-Host "   - 先启动Hadoop (HDFS + YARN)"
    Write-Host "   - 再启动Spark集群"
    Write-Host "   - 最后启动Hive Metastore"
    Write-Host ""
    
    Write-Host "4. 验证启动:"
    Write-Host "   - 使用jps命令查看Java进程"
    Write-Host "   - 访问Web界面确认服务状态"
    Write-Host "   - 测试端口连接"
}

# 主函数
function Main {
    Write-Host "=== *************** 服务器检查 ===" -ForegroundColor Cyan
    Write-Host "服务器IP: $ServerIP" -ForegroundColor Cyan
    Write-Host ""
    
    # 检查端口
    Test-ServerPorts
    
    Write-Host ""
    
    # 检查Web界面
    Test-WebInterfaces
    
    Write-Host ""
    
    # 生成SSH命令
    Generate-SSHCommands
    
    Write-Host ""
    
    # 生成配置建议
    Generate-ConfigSuggestions
    
    Write-Host ""
    Write-Host "=== 检查完成 ===" -ForegroundColor Cyan
}

# 执行主函数
Main
