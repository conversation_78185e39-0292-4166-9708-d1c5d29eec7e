package com.zyyj.cere.pojo.entity;

/**
 * <AUTHOR>
 * @date 2020/11/12 14:24
 */


import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.sdk.processor.annotation.ThriftPaged;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 应用分类
 */
@Setter
@ThriftStruct
@ThriftPaged
@NoArgsConstructor
@ToString
@Builder
@Entity
@Table(name = "application_type")
@EntityListeners(ApplicationTypeEntity.class)
public class ApplicationTypeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, length = 11)
    private Integer id;
    /**
     * 名称
     */
    @NotEmpty(message = "应用分类名称不能为空")
    @Column(name = "name", nullable = false, length = 10)
    @ApiModelProperty(name = "name", value = "名称")
    private String name = "";
    @ThriftConstructor
    public ApplicationTypeEntity(Integer id, @NotEmpty(message = "应用分类名称不能为空") String name) {
        this.id = id;
        this.name = name;
    }

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public String getName() {
        return name;
    }

}
