server:
  port: 8008

eureka:
  instance:
    prefer-ip-address: true
    non-secure-port: 9008
  client:
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://************:8761/eureka/

spring:
  application:
    name: ZYYJ-SPARK-THRIFT
  redis:
    database: 1
    host: *************
    port: 6379
    password: Ubq1jSDR64S8ovlYl
    timeout: 60000
zyyj:
  rpc:
    thrift:
      server:
        listen_port: 9008
spark:
  master: spark://spark02:7077  #YARN
  appName: ZYYJ-SPARK
  driver: ************
  warehouseDir: hdfs://ns/delta/
  metastoreUris: thrift://*************:9083
  hadoopConfig:
    - config/core-site.xml
    - config/hdfs-site.xml
  sparkYarnDistJars:
    - hdfs://ns/jars/delta-core_2.12-0.7.0.jar
  sparkJars:
    - hdfs://ns/jars/delta-core_2.12-0.7.0.jar
    - hdfs://ns/jars/mysql-connector-java-8.0.16.jar