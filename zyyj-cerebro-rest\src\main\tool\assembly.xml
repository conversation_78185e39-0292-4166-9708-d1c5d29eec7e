<assembly>
    <id>bin</id>
    <formats>
        <format>dir</format>
        <!--<format>zip</format>-->
    </formats>
    <dependencySets>
        <dependencySet>
            <!-- 不使用项目的artifact，第三方jar不要解压，打包进zip文件的lib目录 -->
            <useProjectArtifact>false</useProjectArtifact>
            <outputDirectory>lib</outputDirectory>
            <!-- 依赖包按规则命名,防止snapshot命名混乱 -->
            <outputFileNameMapping>
                ${artifact.artifactId}-${artifact.baseVersion}.${artifact.extension}
            </outputFileNameMapping>
        </dependencySet>
    </dependencySets>
    <fileSets>
        <fileSet>
            <directory>src/main/bin</directory>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>restart.sh</include>
                <include>start.sh</include>
                <include>stop.sh</include>
            </includes>
        </fileSet>

        <!-- 把配置文件放进config目录 -->
        <fileSet>
            <directory>src/main/resources/${profiles.active}</directory>
            <outputDirectory>config</outputDirectory>
        </fileSet>

        <!-- 把项目自己编译出来的jar文件，打包进zip文件的根目录 -->
        <fileSet>
            <directory>${project.build.directory}</directory>
            <outputDirectory></outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>