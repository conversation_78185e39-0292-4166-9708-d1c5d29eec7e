{"commitInfo":{"timestamp":1753691811547,"operation":"CREATE TABLE","operationParameters":{"isManaged":"true","description":null,"partitionBy":"[]","properties":"{}"},"isBlindAppend":true,"operationMetrics":{}}}
{"protocol":{"minReaderVersion":1,"minWriterVersion":2}}
{"metaData":{"id":"5f58ce42-e9de-43b7-a22f-93247c540a6e","format":{"provider":"parquet","options":{}},"schemaString":"{\"type\":\"struct\",\"fields\":[{\"name\":\"table_name\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"sq_id\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"config\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}}]}","partitionColumns":[],"configuration":{},"createdTime":1753691811543}}
