# =============================================================================
# Windows端配置更新脚本 - 配置调用Ubuntu上的Spark服务
# =============================================================================

param(
    [string]$UbuntuServerIP = "***************",
    [switch]$Help
)

if ($Help) {
    Write-Host "用法: .\update-windows-config.ps1 [-UbuntuServerIP <IP地址>]"
    Write-Host ""
    Write-Host "参数:"
    Write-Host "  -UbuntuServerIP  Ubuntu服务器IP地址 (默认: ***************)"
    Write-Host "  -Help           显示帮助信息"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  .\update-windows-config.ps1"
    Write-Host "  .\update-windows-config.ps1 -UbuntuServerIP ***************"
    exit 0
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colors = @{
        "Red" = "Red"
        "Green" = "Green" 
        "Yellow" = "Yellow"
        "Blue" = "Blue"
        "White" = "White"
    }
    
    Write-Host $Message -ForegroundColor $colors[$Color]
}

function Log-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Green"
}

function Log-Warn {
    param([string]$Message)
    Write-ColorOutput "[WARN] $Message" "Yellow"
}

function Log-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

function Log-Step {
    param([string]$Message)
    Write-ColorOutput "[STEP] $Message" "Blue"
}

# 停止Windows上的Spark RPC服务
function Stop-WindowsSparkService {
    Log-Step "停止Windows上的Spark RPC服务..."
    
    try {
        # 查找并杀掉Spark RPC进程
        $sparkProcesses = Get-Process -Name "java" -ErrorAction SilentlyContinue | Where-Object {
            $_.CommandLine -like "*zyyj-spark-rpc-server*"
        }
        
        if ($sparkProcesses) {
            foreach ($process in $sparkProcesses) {
                Log-Info "停止进程 PID: $($process.Id)"
                Stop-Process -Id $process.Id -Force
            }
            Start-Sleep -Seconds 5
        } else {
            Log-Info "未找到运行中的Spark RPC服务"
        }
    } catch {
        Log-Warn "停止服务时出现错误: $($_.Exception.Message)"
    }
}

# 更新Cerebro RPC配置
function Update-CerebroConfig {
    Log-Step "更新Cerebro RPC配置..."
    
    $configPath = "zyyj-cerebro-rpc-server\src\main\resources\dev\application.yml"
    
    if (-not (Test-Path $configPath)) {
        Log-Error "配置文件不存在: $configPath"
        return $false
    }
    
    try {
        # 读取配置文件
        $content = Get-Content $configPath -Raw
        
        # 确保service.spark配置正确
        if ($content -match "service:\s*\n\s*#thrift 客户端配置\s*\n\s*spark:\s*(.+)") {
            if ($matches[1].Trim() -ne "ZYYJ-SPARK-THRIFT") {
                $content = $content -replace "(service:\s*\n\s*#thrift 客户端配置\s*\n\s*spark:\s*)(.+)", "`$1ZYYJ-SPARK-THRIFT"
                Log-Info "更新service.spark配置为: ZYYJ-SPARK-THRIFT"
            } else {
                Log-Info "service.spark配置已正确: ZYYJ-SPARK-THRIFT"
            }
        }
        
        # 保存配置文件
        Set-Content -Path $configPath -Value $content -Encoding UTF8
        Log-Info "Cerebro RPC配置更新完成"
        return $true
    } catch {
        Log-Error "更新配置文件失败: $($_.Exception.Message)"
        return $false
    }
}

# 测试Ubuntu服务器连接
function Test-UbuntuConnection {
    Log-Step "测试Ubuntu服务器连接..."
    
    try {
        # 测试SSH连接
        $sshTest = Test-NetConnection -ComputerName $UbuntuServerIP -Port 22 -InformationLevel Quiet
        if ($sshTest) {
            Log-Info "SSH连接正常 (端口22)"
        } else {
            Log-Warn "SSH连接失败 (端口22)"
        }
        
        # 测试Eureka连接
        $eurekaTest = Test-NetConnection -ComputerName $UbuntuServerIP -Port 8761 -InformationLevel Quiet
        if ($eurekaTest) {
            Log-Info "Eureka连接正常 (端口8761)"
        } else {
            Log-Warn "Eureka连接失败 (端口8761)"
        }
        
        return $sshTest
    } catch {
        Log-Error "连接测试失败: $($_.Exception.Message)"
        return $false
    }
}

# 重新构建Cerebro RPC服务
function Rebuild-CerebroRPC {
    Log-Step "重新构建Cerebro RPC服务..."
    
    try {
        # 设置Java环境
        $env:JAVA_HOME = "C:\Program Files\Java\jdk1.8.0_141"
        
        # 构建zyyj-cerebro-thrift模块
        Log-Info "构建zyyj-cerebro-thrift模块..."
        Set-Location "zyyj-cerebro-thrift"
        $buildResult = & mvn clean package -DskipTests
        Set-Location ".."
        
        if ($LASTEXITCODE -eq 0) {
            Log-Info "zyyj-cerebro-thrift构建成功"
        } else {
            Log-Error "zyyj-cerebro-thrift构建失败"
            return $false
        }
        
        # 替换JAR包
        $sourceJar = "zyyj-cerebro-thrift\target\zyyj-cerebro-thrift-1.0.0-SNAPSHOT.jar"
        $targetJar = "bak1.0\zyyj-cerebro-rpc-server\zyyj-cerebro-rpc-server-bin\zyyj-cerebro-rpc-server\lib\zyyj-cerebro-thrift-1.0.0-SNAPSHOT.jar"
        
        if (Test-Path $sourceJar) {
            Copy-Item $sourceJar $targetJar -Force
            Log-Info "JAR包替换完成"
            return $true
        } else {
            Log-Error "源JAR包不存在: $sourceJar"
            return $false
        }
    } catch {
        Log-Error "构建失败: $($_.Exception.Message)"
        return $false
    }
}

# 启动Windows服务
function Start-WindowsServices {
    Log-Step "启动Windows端服务..."
    
    # 启动Cerebro RPC服务
    Log-Info "启动Cerebro RPC服务..."
    $cerebroProcess = Start-Process -FilePath "C:\Program Files\Java\jdk1.8.0_141\bin\java" -ArgumentList @(
        "-Dspring.profiles.active=dev",
        "-Dspring.main.allow-bean-definition-overriding=true",
        "-Dserver.port=9007",
        "-Dzyyj.rpc.thrift.server.listen_port=9006",
        "-Deureka.client.serviceUrl.defaultZone=http://admin:admin123@${UbuntuServerIP}:8761/eureka/",
        "-Dspring.config.location=file:E:\dev\project\zyyj-cerebro\zyyj-cerebro-rpc-server\src\main\resources\dev\application.yml",
        "-jar",
        "E:\dev\project\zyyj-cerebro\bak1.0\zyyj-cerebro-rpc-server\zyyj-cerebro-rpc-server-bin\zyyj-cerebro-rpc-server\zyyj-cerebro-rpc-server.jar"
    ) -PassThru -RedirectStandardOutput "logs\cerebro-rpc-new.log" -RedirectStandardError "logs\cerebro-rpc-new-error.log"
    
    Log-Info "Cerebro RPC进程ID: $($cerebroProcess.Id)"
    
    # 等待服务启动
    Start-Sleep -Seconds 30
    
    # 启动REST API服务
    Log-Info "启动REST API服务..."
    $restProcess = Start-Process -FilePath "C:\Program Files\Java\jdk1.8.0_141\bin\java" -ArgumentList @(
        "-Dserver.port=8005",
        "-Dspring.profiles.active=dev",
        "-Dspring.application.name=ZYYJ-CEREBRO-REST",
        "-Deureka.instance.appname=ZYYJ-CEREBRO-REST",
        "-Deureka.instance.non-secure-port=8005",
        "-Dspring.main.allow-bean-definition-overriding=true",
        "-Deureka.client.serviceUrl.defaultZone=http://admin:admin123@${UbuntuServerIP}:8761/eureka/",
        "-Dservice.cerebro=ZYYJ-CEREBRO-THRIFT",
        "-Dscan.package=com.zyyj.controller",
        "-jar",
        "E:\dev\project\zyyj-cerebro\zyyj-cerebro-rest\target\zyyj-cerebro-rest.jar"
    ) -PassThru -RedirectStandardOutput "logs\rest-api-new.log" -RedirectStandardError "logs\rest-api-new-error.log"
    
    Log-Info "REST API进程ID: $($restProcess.Id)"
    
    # 等待服务启动
    Start-Sleep -Seconds 30
}

# 检查服务状态
function Check-ServicesStatus {
    Log-Step "检查服务状态..."
    
    # 检查Cerebro RPC
    $cerebroPort = netstat -ano | Select-String ":9006.*LISTENING"
    if ($cerebroPort) {
        Log-Info "✅ Cerebro RPC服务正常 (9006端口)"
    } else {
        Log-Warn "❌ Cerebro RPC服务异常 (9006端口)"
    }
    
    # 检查REST API
    $restPort = netstat -ano | Select-String ":8005.*LISTENING"
    if ($restPort) {
        Log-Info "✅ REST API服务正常 (8005端口)"
    } else {
        Log-Warn "❌ REST API服务异常 (8005端口)"
    }
}

# 主函数
function Main {
    Log-Info "开始配置Windows端服务调用Ubuntu Spark服务"
    Log-Info "Ubuntu服务器IP: $UbuntuServerIP"
    
    # 创建日志目录
    if (-not (Test-Path "logs")) {
        New-Item -ItemType Directory -Name "logs" | Out-Null
    }
    
    # 停止现有服务
    Stop-WindowsSparkService
    
    # 测试连接
    if (-not (Test-UbuntuConnection)) {
        Log-Error "无法连接到Ubuntu服务器，请检查网络和服务器状态"
        return
    }
    
    # 更新配置
    if (-not (Update-CerebroConfig)) {
        Log-Error "配置更新失败"
        return
    }
    
    # 重新构建
    if (-not (Rebuild-CerebroRPC)) {
        Log-Error "服务构建失败"
        return
    }
    
    # 启动服务
    Start-WindowsServices
    
    # 检查状态
    Check-ServicesStatus
    
    Log-Info "配置完成！"
    Log-Info ""
    Log-Info "下一步："
    Log-Info "1. 确保Ubuntu服务器上的Spark RPC服务正在运行"
    Log-Info "2. 测试接口: POST http://localhost:8005/api/v1/table/add"
    Log-Info "3. 查看日志文件了解详细状态"
}

# 执行主函数
Main
