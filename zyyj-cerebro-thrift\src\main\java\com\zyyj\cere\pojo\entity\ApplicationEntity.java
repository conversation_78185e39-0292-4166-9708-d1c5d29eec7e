package com.zyyj.cere.pojo.entity;

/**
 * <AUTHOR>
 * @date 2020/11/10 16:10
 */


import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.sdk.processor.annotation.ThriftPaged;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 应用
 */
@Setter
@ThriftStruct
@ThriftPaged
@NoArgsConstructor
@ToString
@Builder
@ApiModel(value = "ApplicationEntity", description = "应用")
@Entity
@Table(name = "application")
@EntityListeners(ApplicationEntity.class)
public class ApplicationEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, length = 11)
    @ApiModelProperty(name = "id", value = "ID")
    private Integer id;
    /**
     * 名称
     */
    @NotEmpty(message = "应用名称不能为空")
    @Column(name = "name", nullable = false, length = 10)
    @ApiModelProperty(name = "name", value = "名称")
    private String name = "";


    @Column(name = "type_id", length = 11)
    @ApiModelProperty(name = "type_id", value = "应用分类id")
    private Integer typeId;
    /**
     * 描述
     */
    @Column(name = "`describe`", length = 200)
    @ApiModelProperty(name = "describe", value = "描述")
    private String describe = "";
    /**
     * app_id
     */
    @Column(name = "app_id", nullable = false, length = 17)
    @ApiModelProperty(name = "app_id", value = "APP_ID")
    private String appId = "";
    /**
     * 密钥
     */
    @Column(name = "app_secret", nullable = false, length = 34)
    @ApiModelProperty(name = "app_secret", value = "密钥")
    private String appSecret = "";

    @ThriftConstructor
    public ApplicationEntity(Integer id, @NotEmpty(message = "应用名称不能为空") String name, Integer typeId, String describe, String appId, String appSecret) {
        this.id = id;
        this.name = name;
        this.typeId = typeId;
        this.describe = describe;
        this.appId = appId;
        this.appSecret = appSecret;
    }

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public String getName() {
        return name;
    }

    @ThriftField(3)
    public Integer getTypeId() {
        return typeId;
    }

    @ThriftField(4)
    public String getDescribe() {
        return describe;
    }

    @ThriftField(5)
    public String getAppId() {
        return appId;
    }

    @ThriftField(6)
    public String getAppSecret() {
        return appSecret;
    }
}
