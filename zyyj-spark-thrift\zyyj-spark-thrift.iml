<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="Spring" name="Spring">
      <configuration />
    </facet>
    <facet type="web" name="Web">
      <configuration>
        <webroots />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Maven: com.zyyj.sdk:zyyj-domain-common:1.0.2-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.gson:gson:2.8.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-collections4:4.2" level="project" />
    <orderEntry type="library" name="Maven: apache-beanutils:commons-beanutils:1.7.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-commons:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.25" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-web:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-json:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.9.7" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.9.7" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.7" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-parameter-names:2.9.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-tomcat:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-core:9.0.12" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-el:9.0.12" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-websocket:9.0.12" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.validator:hibernate-validator:6.0.13.Final" level="project" />
    <orderEntry type="library" name="Maven: org.jboss.logging:jboss-logging:3.3.2.Final" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml:classmate:1.4.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-web:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webmvc:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.belerweb:pinyin4j:2.5.0" level="project" />
    <orderEntry type="library" name="Maven: com.zyyj.sdk:zyyj-rpc-thrift-common:1.0.2-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.zyyj.sdk:zyyj-processor:1.0.0-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.google.auto.service:auto-service:1.0-rc4" level="project" />
    <orderEntry type="library" name="Maven: com.google.auto:auto-common:0.8" level="project" />
    <orderEntry type="library" name="Maven: com.squareup:javapoet:1.10.0" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.javax.persistence:hibernate-jpa-2.1-api:1.0.0.Final" level="project" />
    <orderEntry type="library" name="Maven: com.zyyj.sdk:zyyj-processor-annotation:1.0.0-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-lang3:3.8.1" level="project" />
    <orderEntry type="library" name="Maven: com.facebook.swift:swift-annotations:0.23.1" level="project" />
    <orderEntry type="library" name="Maven: javax.validation:validation-api:2.0.1.Final" level="project" />
    <orderEntry type="library" name="Maven: io.swagger:swagger-annotations:1.5.13" level="project" />
    <orderEntry type="library" name="Maven: javax.persistence:persistence-api:1.0" level="project" />
    <orderEntry type="library" name="Maven: com.zyyj.sdk:zyyj-rpc-thrift:1.0.2-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.facebook.swift:swift-service:0.23.1-zyyj" level="project" />
    <orderEntry type="library" name="Maven: com.facebook.swift:swift-codec:0.23.1" level="project" />
    <orderEntry type="library" name="Maven: com.thoughtworks.paranamer:paranamer:2.8" level="project" />
    <orderEntry type="library" name="Maven: jp.skypencil.guava:helper:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.thrift:libthrift:0.9.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpclient:4.5.6" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.11" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpcore:4.4.10" level="project" />
    <orderEntry type="library" name="Maven: com.facebook.nifty:nifty-client:0.23.0" level="project" />
    <orderEntry type="library" name="Maven: com.google.inject:guice:4.0" level="project" />
    <orderEntry type="library" name="Maven: aopalliance:aopalliance:1.0" level="project" />
    <orderEntry type="library" name="Maven: com.facebook.nifty:nifty-core:0.23.0" level="project" />
    <orderEntry type="library" name="Maven: javax.inject:javax.inject:1" level="project" />
    <orderEntry type="library" name="Maven: com.google.inject.extensions:guice-multibindings:4.0" level="project" />
    <orderEntry type="library" name="Maven: com.facebook.nifty:nifty-ssl:0.23.0" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-tcnative-boringssl-static:2.0.17.Final" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.9.7" level="project" />
    <orderEntry type="library" name="Maven: io.airlift:configuration:0.119" level="project" />
    <orderEntry type="library" name="Maven: cglib:cglib-nodep:2.2.2" level="project" />
    <orderEntry type="library" name="Maven: io.airlift:stats:0.119" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.9.0" level="project" />
    <orderEntry type="library" name="Maven: io.airlift:slice:0.10" level="project" />
    <orderEntry type="library" name="Maven: org.openjdk.jol:jol-core:0.1" level="project" />
    <orderEntry type="library" name="Maven: io.airlift:units:0.119" level="project" />
    <orderEntry type="library" name="Maven: io.airlift:log:0.119" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty:3.10.5.Final" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.findbugs:annotations:2.0.3" level="project" />
    <orderEntry type="library" name="Maven: org.weakref:jmxutils:1.18" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-pool2:2.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-commons:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-crypto:5.1.1.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.google.guava:guava:24.0-jre" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.findbugs:jsr305:1.3.9" level="project" />
    <orderEntry type="library" name="Maven: org.checkerframework:checker-compat-qual:2.0.0" level="project" />
    <orderEntry type="library" name="Maven: com.google.errorprone:error_prone_annotations:2.1.3" level="project" />
    <orderEntry type="library" name="Maven: com.google.j2objc:j2objc-annotations:1.1" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.mojo:animal-sniffer-annotations:1.14" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.11.1" level="project" />
    <orderEntry type="library" name="Maven: com.zyyj:zyyj-acc-thrift:1.0.0-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-data-redis:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-redis:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-keyvalue:2.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-tx:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-oxm:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context-support:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: io.lettuce:lettuce-core:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: io.projectreactor:reactor-core:3.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.reactivestreams:reactive-streams:1.0.2" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-common:4.1.29.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-transport:4.1.29.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-buffer:4.1.29.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-resolver:4.1.29.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-handler:4.1.29.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-codec:4.1.29.Final" level="project" />
    <orderEntry type="library" name="Maven: tk.mybatis:mapper-core:1.1.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-autoconfigure:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: javax.annotation:javax.annotation-api:1.3.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jcl:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.yaml:snakeyaml:1.23" level="project" />
    <orderEntry type="library" name="Maven: org.projectlombok:lombok:1.18.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-starter-test:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test-autoconfigure:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.jayway.jsonpath:json-path:2.4.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:json-smart:2.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:accessors-smart:1.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.ow2.asm:asm:5.0.4" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: junit:junit:4.12" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.assertj:assertj-core:3.11.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.mockito:mockito-core:2.23.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.bytebuddy:byte-buddy:1.9.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.bytebuddy:byte-buddy-agent:1.9.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.objenesis:objenesis:2.6" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest-core:1.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest-library:1.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.skyscreamer:jsonassert:1.5.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.vaadin.external.google:android-json:0.0.20131108.vaadin1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework:spring-test:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.xmlunit:xmlunit-core:2.6.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: javax.xml.bind:jaxb-api:2.3.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: javax.activation:javax.activation-api:1.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-aop:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.aspectj:aspectjweaver:1.9.2" level="project" />
    <orderEntry type="library" name="Maven: com.github.pagehelper:pagehelper-spring-boot-starter:1.2.10" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis.spring.boot:mybatis-spring-boot-starter:1.3.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-jdbc:2.1.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.zaxxer:HikariCP:3.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jdbc:5.1.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis.spring.boot:mybatis-spring-boot-autoconfigure:1.3.2" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis:3.4.6" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis-spring:1.3.2" level="project" />
    <orderEntry type="library" name="Maven: com.github.pagehelper:pagehelper-spring-boot-autoconfigure:1.2.10" level="project" />
    <orderEntry type="library" name="Maven: com.github.pagehelper:pagehelper:5.1.8" level="project" />
    <orderEntry type="library" name="Maven: com.github.jsqlparser:jsqlparser:1.2" level="project" />
  </component>
</module>