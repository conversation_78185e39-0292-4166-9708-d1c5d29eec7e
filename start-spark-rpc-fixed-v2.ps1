# 停止现有进程
Write-Host "正在停止现有 Spark RPC 服务..."
Get-Process | Where-Object {$_.ProcessName -eq "java" -and $_.CommandLine -like "*zyyj-spark-rpc-server*"} | ForEach-Object {
    Stop-Process -Id $_.Id -Force
    Write-Host "已停止进程 ID: $($_.Id)"
}

# 等待进程完全停止
Start-Sleep -Seconds 3

# 设置环境变量
$env:JAVA_HOME = "C:\Program Files\Java\jdk1.8.0_141"
$env:HADOOP_USER_NAME = "root"
$env:SPARK_LOCAL_HOSTNAME = "localhost"

# 检查并添加主机名映射
$hostsPath = "C:\Windows\System32\drivers\etc\hosts"
$hostsContent = Get-Content $hostsPath
if (-not ($hostsContent -match "kai12")) {
    Write-Host "添加kai12主机名映射到hosts文件..."
    Add-Content -Path $hostsPath -Value "`n*************** kai12" -Force
}

# 测试连接
Write-Host "测试连接到 Spark Master..."
$testResult = Test-NetConnection -ComputerName *************** -Port 7077 -InformationLevel Quiet
if ($testResult) {
    Write-Host "连接测试成功！"
} else {
    Write-Host "警告: 无法连接到 Spark Master (***************:7077)"
    Write-Host "继续启动服务，但可能会连接失败..."
}

Write-Host "正在启动 Spark RPC 服务 (集群模式)..."

# 创建临时配置文件
$tempConfigDir = ".\temp-config"
if (-not (Test-Path $tempConfigDir)) {
    New-Item -Path $tempConfigDir -ItemType Directory -Force | Out-Null
}

# 创建 spark-defaults.conf
$sparkDefaultsPath = "$tempConfigDir\spark-defaults.conf"
@"
# Spark 集群配置
spark.master                     spark://***************:7077
spark.app.name                   ZYYJ-SPARK-RPC-SERVER
spark.driver.host                localhost
spark.driver.bindAddress         0.0.0.0
spark.network.timeout            300s
spark.rpc.askTimeout             300s
spark.rpc.lookupTimeout          300s
spark.serializer.objectStreamReset 100
spark.sql.execution.arrow.pyspark.enabled false

# Hadoop 安全配置
spark.hadoop.hadoop.security.authentication simple
spark.hadoop.hadoop.security.authorization false
spark.hadoop.hadoop.proxyuser.root.hosts *
spark.hadoop.hadoop.proxyuser.root.groups *

# 仓库配置
spark.sql.warehouse.dir          hdfs://***************:9000/spark-warehouse
"@ | Out-File -FilePath $sparkDefaultsPath -Encoding utf8

# 设置 Spark 配置目录
$env:SPARK_CONF_DIR = (Resolve-Path $tempConfigDir).Path

$process = Start-Process -FilePath "$env:JAVA_HOME\bin\java" -ArgumentList `
    "-Xms512m",
    "-Xmx1g",
    "-Dserver.port=8081",
    "-Dspring.profiles.active=dev",
    "-Dspring.application.name=ZYYJ-SPARK-THRIFT",
    "-Deureka.instance.appname=ZYYJ-SPARK-THRIFT",
    "-Deureka.instance.non-secure-port=8081",
    "-Deureka.client.serviceUrl.defaultZone=******************************************/eureka/",
    "-Dspring.main.allow-bean-definition-overriding=true",
    "-Dspark.master=spark://***************:7077",
    "-Dspark.appName=ZYYJ-SPARK-RPC-SERVER",
    "-Dspark.warehouseDir=hdfs://***************:9000/spark-warehouse",
    "-Dspark.metastoreUris=thrift://***************:9083",
    "-Dspark.driver=localhost",
    "-Dspark.driver.bindAddress=0.0.0.0",
    "-Dzyyj.rpc.thrift.server.listen_port=9009",
    # 网络超时设置
    "-Dspark.network.timeout=300s",
    "-Dspark.sql.execution.arrow.pyspark.enabled=false",
    "-Dspark.serializer.objectStreamReset=100",
    "-Dspark.rpc.askTimeout=300s",
    "-Dspark.rpc.lookupTimeout=300s",
    # 用户映射和安全配置
    "-DHADOOP_USER_NAME=root",
    "-Duser.name=root",
    "-Dhadoop.security.authentication=simple",
    "-Dspark.hadoop.hadoop.security.authentication=simple",
    "-Dspark.hadoop.hadoop.security.authorization=false",
    # 显式指定 Spark 配置目录
    "-Dspark.conf.dir=$env:SPARK_CONF_DIR",
    "-jar","E:\dev\project\zyyj-cerebro\zyyj-spark-rpc-server\target\zyyj-spark-rpc-server.jar" `
    -RedirectStandardOutput "logs\spark-rpc.log" `
    -RedirectStandardError "logs\spark-rpc-error.log" `
    -PassThru

Write-Host "Spark RPC 服务已启动，进程 ID: $($process.Id)"
Write-Host "日志文件: logs\spark-rpc.log"
Write-Host "错误日志: logs\spark-rpc-error.log"