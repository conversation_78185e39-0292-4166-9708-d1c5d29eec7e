package com.zyyj.cere.pojo.entity;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.cere.pojo.body.TableAddBody;
import io.swagger.annotations.ApiModel;
import lombok.*;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 业务表信息
 */
@ThriftStruct
@Setter
@NoArgsConstructor
@ToString
@Builder
@ApiModel(value = "TableEntity", description = "物理表")
@Entity
@Table(name = "`table`")
@EntityListeners(TableEntity.class)
public class TableEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    /**
     * 业务域id
     */
    @Column(name = "business_id", nullable = false)
    private Long businessId;

    /**
     * 表中文名
     */
    @Column(name = "name", nullable = false)
    private String name = "";

    /**
     * 对应表名
     */
    @Column(name = "table_name", nullable = false)
    private String tableName = "";

    /**
     * 1 手动添加  2 导入  3 管道  4 组件建模  5 敏捷建模
     */
    @Column(name = "`source`", nullable = false)
    @Builder.Default()
    private Byte source = 1;

    /**
     * 1 物理表  2 主题表
     */
    @Column(name = "`type_id`", nullable = false)
    private Byte typeId = 1;

    /**
     * 1 未发布 2 待审核 3 已发布(主题表  物理表默认0)
     */
    @Column(name = "`status`", nullable = false)
    @Builder.Default()
    private Byte status = 0;

    /**
     * 同步类型  1 管道  2 同步任务
     */
    @Column(name = "sync_type", nullable = false)
    @Builder.Default()
    private Byte syncType = 0;

    /**
     * 管道id
     */
    @Column(name = "tunnel_id")
    @Builder.Default()
    private Integer tunnelId = 0;

    /**
     * 建模id
     */
    @Column(name = "modeling_id")
    @Builder.Default()
    private Integer modelingId = 0;

    @ThriftConstructor
    public TableEntity(Long id, Long businessId, String name, String tableName, Byte source, Byte typeId, Byte status, Byte syncType, Integer tunnelId, Integer modelingId) {
        this.id = id;
        this.businessId = businessId;
        this.name = name;
        this.tableName = tableName;
        this.source = source;
        this.typeId = typeId;
        this.status = status;
        this.syncType = syncType;
        this.tunnelId = tunnelId;
        this.modelingId = modelingId;
    }

    @ThriftField(1)
    public Long getId() {
        return id;
    }

    @ThriftField(2)
    public Long getBusinessId() {
        return businessId;
    }

    @ThriftField(3)
    public String getName() {
        return name;
    }

    @ThriftField(4)
    public String getTableName() {
        return tableName;
    }

    @ThriftField(5)
    public Byte getSource() {
        return source;
    }

    @ThriftField(6)
    public Byte getTypeId() {
        return typeId;
    }

    @ThriftField(7)
    public Byte getStatus() {
        return status;
    }

    @ThriftField(8)
    public Byte getSyncType() {
        return syncType;
    }

    @ThriftField(9)
    public Integer getTunnelId() {
        return tunnelId;
    }

    @ThriftField(10)
    public Integer getModelingId() {
        return modelingId;
    }

    public String getSyncTypeName(){
        switch (syncType){
            case 1:
                return "TUN";
            case 2:
                return "API";
            default:
                return "";
        }
    }

    /**
     * 添加表接口参数body转表映射对象
     *
     * @param t 参数对象
     * @param 1 手动添加  2 导入  3 管道  4 组件建模  5 敏捷建模
     * @return 本类
     */
    public static TableEntity of(TableAddBody t) {
        return TableEntity
                .builder()
                .name(t.getName())
                .tableName(t.getTableName())
                .businessId(t.getBusinessId())
                .source(t.getSource().byteValue())
                .typeId((byte) 1)
//                .status((byte)1)
//                .syncType((byte)0)
//                .tunnelId(0)
//                .modelingId(0)
                .build();
    }

    ;
}
