package com.zyyj.cere.repository;

import com.zyyj.cere.CerebroRPCServerApplication;
import com.zyyj.cere.pojo.dto.PagedDatasourceDTO;
import com.zyyj.cere.pojo.entity.DatasourceEntity;
import com.zyyj.domain.pagination.OrderBy;
import com.zyyj.domain.pagination.Paging;
import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CerebroRPCServerApplication.class)
public class DatasourceRepositoryTest extends TestCase {

    @Autowired
    DatasourceRepository datasourceRepository;

    @Test
    public void page(){
//        Sort sort = Sort.by(Sort.Direction.DESC, "id");
//        Pageable pageable = PageRequest.of(1,3,sort);

        Paging pageable = new Paging(1,5,new OrderBy(Sort.Direction.DESC, "id"));

        Page<DatasourceEntity> page =  datasourceRepository.findAll(pageable);
        System.out.println(page);

        PagedDatasourceDTO pd = new PagedDatasourceDTO(page);
        System.out.println(pd);

        System.out.println(pd.getTotal());

        pd.getData().forEach(System.out::println);
    }
}