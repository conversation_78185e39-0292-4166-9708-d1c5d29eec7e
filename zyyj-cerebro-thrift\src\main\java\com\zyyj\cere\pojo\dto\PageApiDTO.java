package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.cere.pojo.entity.ApiEntity;
import com.zyyj.domain.pagination.Paged;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/17 14:45
 */
@ThriftStruct
public class PageApiDTO extends Paged<ApiResponseDTO> {


    public PageApiDTO(Page<ApiResponseDTO> page) {
        super(page);
    }

    @ThriftConstructor
    public PageApiDTO(long total, List<ApiResponseDTO> data) {
        super(total, data);
    }
}
