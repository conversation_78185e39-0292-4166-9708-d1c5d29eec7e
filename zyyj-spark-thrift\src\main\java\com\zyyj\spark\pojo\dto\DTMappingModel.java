package com.zyyj.spark.pojo.dto;



import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.NoArgsConstructor;
import lombok.Setter;


// Delta tunnel Mapping Config

@Setter
@ThriftStruct
@NoArgsConstructor
public class DTMappingModel {
    //来源字段
    private String sourceField;

    //目标字段
    private String field;

    //字段标题
    private String comment;

    //varchar
    private String type;

    //是否主键
    private int priKey;

    // 如果是更新字段， 表示该字段是原始库中的字段名, 需要更新成字段名 {name}
    private String dbField="";
    // 是否是更新数据
    private Byte updateField =0;
    // 是否更新 comment
    private Byte updateComment=0;

    @ThriftConstructor
    public DTMappingModel(String sourceField, String field, String comment, String type, int priKey, Byte updateField, String dbField, Byte updateComment) {
        this.sourceField = sourceField;
        this.field = field;
        this.comment = comment;
        this.type = type;
        this.priKey = priKey;
        this.updateField = updateField;
        this.dbField = dbField;
        this.updateComment = updateComment;
    }


    @ThriftField(1)
    public String getSourceField() {
        return sourceField;
    }

    @ThriftField(2)
    public String getField() {
        return field;
    }

    @ThriftField(3)
    public String getComment() {
        return comment;
    }

    @ThriftField(4)
    public String getType() {
        return type;
    }

    @ThriftField(5)
    public int getPriKey() {
        return priKey;
    }


    @ThriftField(6)
    public Byte getUpdateField() {
        return updateField;
    }

    @ThriftField(7)
    public String getDbField() {
        return dbField;
    }

    @ThriftField(8)
    public Byte getUpdateComment() {
        return updateComment;
    }

    /* 是否是新的field，新的需要添加 */
    public boolean isAddField(){ return this.dbField==null || this.dbField.isEmpty();  }
    /* 是否更新 comment */
    public boolean isUpdateComment() { return this.updateComment==1 && !dbField.isEmpty(); };
    /* 是否字段名更新 */
    public boolean isRenameField(){
        return !dbField.isEmpty() && !this.field.equals(dbField);
    }


    // 获取创建表时的sql 片段
    public String getCreateTableFiled(){
        return getTableFiledName() + " " + type + " '" +  comment + "'";
    }

    // 获取库中字段名
    public String getTableFiledName(){
        return "`" + field + "`";
    }

}
