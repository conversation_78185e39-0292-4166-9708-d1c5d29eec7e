package com.zyyj.cere.pojo.dto;


import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.domain.pagination.Paged;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * describe 通用分页返回
 *
 * <AUTHOR>
 * @date 2020/11/11 10:53
 */

@ThriftStruct
public class PagedAppApiDTO extends Paged<ApiApplicationListDTO> {

    public PagedAppApiDTO(Page<ApiApplicationListDTO> page) {
        super(page);
    }

    @ThriftConstructor
    public PagedAppApiDTO(long total, List<ApiApplicationListDTO> data) {
        super(total, data);
    }
}