#!/bin/bash

# =============================================================================
# 192.168.121.157 服务器 Hadoop/Spark 安装脚本
# =============================================================================

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 配置变量
INSTALL_DIR="/opt"
JAVA_HOME="/usr/lib/jvm/java-8-openjdk-amd64"
HADOOP_HOME="$INSTALL_DIR/hadoop"
SPARK_HOME="$INSTALL_DIR/spark"
HIVE_HOME="$INSTALL_DIR/hive"

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
}

# 安装Java 8
install_java8() {
    log_step "安装Java 8..."
    
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1)
        log_info "Java已安装: $JAVA_VERSION"
    else
        log_info "安装OpenJDK 8..."
        apt update
        apt install -y openjdk-8-jdk
        log_info "✅ Java 8安装完成"
    fi
    
    # 设置JAVA_HOME
    if [ ! -d "$JAVA_HOME" ]; then
        JAVA_HOME=$(find /usr/lib/jvm -name "java-8-openjdk*" | head -n 1)
    fi
    
    log_info "JAVA_HOME: $JAVA_HOME"
}

# 安装Hadoop
install_hadoop() {
    log_step "安装Hadoop..."
    
    # 查找Hadoop安装包
    HADOOP_PACKAGE=$(ls /root/ | grep -i hadoop | grep -E "\.(tar\.gz|tgz)$" | head -n 1 || true)
    
    if [ -z "$HADOOP_PACKAGE" ]; then
        log_warn "未找到Hadoop安装包，跳过Hadoop安装"
        return 0
    fi
    
    log_info "发现Hadoop安装包: $HADOOP_PACKAGE"
    
    # 解压安装包
    cd /root
    tar -xzf "$HADOOP_PACKAGE"
    
    # 找到解压后的目录
    HADOOP_DIR=$(ls -d hadoop-* | head -n 1)
    
    if [ -z "$HADOOP_DIR" ]; then
        log_error "解压后未找到Hadoop目录"
        return 1
    fi
    
    # 移动到安装目录
    if [ -d "$HADOOP_HOME" ]; then
        log_warn "删除现有Hadoop安装: $HADOOP_HOME"
        rm -rf "$HADOOP_HOME"
    fi
    
    mv "$HADOOP_DIR" "$HADOOP_HOME"
    chown -R root:root "$HADOOP_HOME"
    
    log_info "✅ Hadoop安装完成: $HADOOP_HOME"
    
    # 创建必要的目录
    mkdir -p /var/log/hadoop
    mkdir -p /tmp/hadoop
    chmod 755 /tmp/hadoop
    
    # 基本配置
    configure_hadoop
}

# 配置Hadoop
configure_hadoop() {
    log_step "配置Hadoop..."
    
    HADOOP_CONF_DIR="$HADOOP_HOME/etc/hadoop"
    
    # 配置hadoop-env.sh
    cat > "$HADOOP_CONF_DIR/hadoop-env.sh" << EOF
export JAVA_HOME=$JAVA_HOME
export HADOOP_HOME=$HADOOP_HOME
export HADOOP_CONF_DIR=$HADOOP_CONF_DIR
export HADOOP_LOG_DIR=/var/log/hadoop
export HADOOP_PID_DIR=/tmp/hadoop
EOF
    
    # 配置core-site.xml
    cat > "$HADOOP_CONF_DIR/core-site.xml" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>fs.defaultFS</name>
        <value>hdfs://$(hostname):9000</value>
    </property>
    <property>
        <name>hadoop.tmp.dir</name>
        <value>/tmp/hadoop</value>
    </property>
</configuration>
EOF
    
    # 配置hdfs-site.xml
    cat > "$HADOOP_CONF_DIR/hdfs-site.xml" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>dfs.replication</name>
        <value>1</value>
    </property>
    <property>
        <name>dfs.namenode.name.dir</name>
        <value>/tmp/hadoop/namenode</value>
    </property>
    <property>
        <name>dfs.datanode.data.dir</name>
        <value>/tmp/hadoop/datanode</value>
    </property>
    <property>
        <name>dfs.namenode.http-address</name>
        <value>0.0.0.0:9870</value>
    </property>
</configuration>
EOF
    
    # 配置yarn-site.xml
    cat > "$HADOOP_CONF_DIR/yarn-site.xml" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>yarn.nodemanager.aux-services</name>
        <value>mapreduce_shuffle</value>
    </property>
    <property>
        <name>yarn.resourcemanager.hostname</name>
        <value>$(hostname)</value>
    </property>
    <property>
        <name>yarn.resourcemanager.webapp.address</name>
        <value>0.0.0.0:8088</value>
    </property>
</configuration>
EOF
    
    # 配置mapred-site.xml
    cat > "$HADOOP_CONF_DIR/mapred-site.xml" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>mapreduce.framework.name</name>
        <value>yarn</value>
    </property>
</configuration>
EOF
    
    log_info "✅ Hadoop配置完成"
}

# 安装Spark
install_spark() {
    log_step "安装Spark..."
    
    # 查找Spark安装包
    SPARK_PACKAGE=$(ls /root/ | grep -i spark | grep -E "\.(tar\.gz|tgz)$" | head -n 1 || true)
    
    if [ -z "$SPARK_PACKAGE" ]; then
        log_warn "未找到Spark安装包，跳过Spark安装"
        return 0
    fi
    
    log_info "发现Spark安装包: $SPARK_PACKAGE"
    
    # 解压安装包
    cd /root
    tar -xzf "$SPARK_PACKAGE"
    
    # 找到解压后的目录
    SPARK_DIR=$(ls -d spark-* | head -n 1)
    
    if [ -z "$SPARK_DIR" ]; then
        log_error "解压后未找到Spark目录"
        return 1
    fi
    
    # 移动到安装目录
    if [ -d "$SPARK_HOME" ]; then
        log_warn "删除现有Spark安装: $SPARK_HOME"
        rm -rf "$SPARK_HOME"
    fi
    
    mv "$SPARK_DIR" "$SPARK_HOME"
    chown -R root:root "$SPARK_HOME"
    
    log_info "✅ Spark安装完成: $SPARK_HOME"
    
    # 创建必要的目录
    mkdir -p /var/log/spark
    
    # 基本配置
    configure_spark
}

# 配置Spark
configure_spark() {
    log_step "配置Spark..."
    
    SPARK_CONF_DIR="$SPARK_HOME/conf"
    
    # 配置spark-env.sh
    cat > "$SPARK_CONF_DIR/spark-env.sh" << EOF
export JAVA_HOME=$JAVA_HOME
export SPARK_HOME=$SPARK_HOME
export HADOOP_HOME=$HADOOP_HOME
export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop
export SPARK_LOG_DIR=/var/log/spark
export SPARK_PID_DIR=/tmp/spark
export SPARK_MASTER_HOST=$(hostname)
export SPARK_MASTER_PORT=7077
export SPARK_MASTER_WEBUI_PORT=8080
EOF
    
    # 配置spark-defaults.conf
    cat > "$SPARK_CONF_DIR/spark-defaults.conf" << EOF
spark.master                     spark://$(hostname):7077
spark.eventLog.enabled           true
spark.eventLog.dir               hdfs://$(hostname):9000/spark-logs
spark.sql.warehouse.dir          hdfs://$(hostname):9000/spark-warehouse
spark.serializer                 org.apache.spark.serializer.KryoSerializer
spark.sql.adaptive.enabled       true
spark.sql.adaptive.coalescePartitions.enabled true
EOF
    
    # 配置workers文件
    echo "$(hostname)" > "$SPARK_CONF_DIR/workers"
    
    log_info "✅ Spark配置完成"
}

# 安装Hive
install_hive() {
    log_step "安装Hive..."
    
    # 查找Hive安装包
    HIVE_PACKAGE=$(ls /root/ | grep -i hive | grep -E "\.(tar\.gz|tgz)$" | head -n 1 || true)
    
    if [ -z "$HIVE_PACKAGE" ]; then
        log_warn "未找到Hive安装包，跳过Hive安装"
        return 0
    fi
    
    log_info "发现Hive安装包: $HIVE_PACKAGE"
    
    # 解压安装包
    cd /root
    tar -xzf "$HIVE_PACKAGE"
    
    # 找到解压后的目录
    HIVE_DIR=$(ls -d *hive* | head -n 1)
    
    if [ -z "$HIVE_DIR" ]; then
        log_error "解压后未找到Hive目录"
        return 1
    fi
    
    # 移动到安装目录
    if [ -d "$HIVE_HOME" ]; then
        log_warn "删除现有Hive安装: $HIVE_HOME"
        rm -rf "$HIVE_HOME"
    fi
    
    mv "$HIVE_DIR" "$HIVE_HOME"
    chown -R root:root "$HIVE_HOME"
    
    log_info "✅ Hive安装完成: $HIVE_HOME"
    
    # 基本配置
    configure_hive
}

# 配置Hive
configure_hive() {
    log_step "配置Hive..."
    
    HIVE_CONF_DIR="$HIVE_HOME/conf"
    
    # 配置hive-site.xml
    cat > "$HIVE_CONF_DIR/hive-site.xml" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>javax.jdo.option.ConnectionURL</name>
        <value>***********************************************************</value>
    </property>
    <property>
        <name>javax.jdo.option.ConnectionDriverName</name>
        <value>org.apache.derby.jdbc.EmbeddedDriver</value>
    </property>
    <property>
        <name>hive.metastore.uris</name>
        <value>thrift://$(hostname):9083</value>
    </property>
    <property>
        <name>hive.metastore.warehouse.dir</name>
        <value>hdfs://$(hostname):9000/hive/warehouse</value>
    </property>
</configuration>
EOF
    
    log_info "✅ Hive配置完成"
}

# 设置环境变量
setup_environment() {
    log_step "设置环境变量..."
    
    # 创建环境变量文件
    cat > /etc/environment << EOF
JAVA_HOME=$JAVA_HOME
HADOOP_HOME=$HADOOP_HOME
SPARK_HOME=$SPARK_HOME
HIVE_HOME=$HIVE_HOME
PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:$JAVA_HOME/bin:$HADOOP_HOME/bin:$HADOOP_HOME/sbin:$SPARK_HOME/bin:$SPARK_HOME/sbin:$HIVE_HOME/bin"
EOF
    
    # 添加到.bashrc
    cat >> /root/.bashrc << EOF

# Hadoop/Spark Environment
export JAVA_HOME=$JAVA_HOME
export HADOOP_HOME=$HADOOP_HOME
export SPARK_HOME=$SPARK_HOME
export HIVE_HOME=$HIVE_HOME
export PATH=\$JAVA_HOME/bin:\$HADOOP_HOME/bin:\$HADOOP_HOME/sbin:\$SPARK_HOME/bin:\$SPARK_HOME/sbin:\$HIVE_HOME/bin:\$PATH
EOF
    
    # 使环境变量生效
    source /root/.bashrc
    
    log_info "✅ 环境变量设置完成"
}

# 初始化Hadoop
initialize_hadoop() {
    log_step "初始化Hadoop..."
    
    if [ -d "$HADOOP_HOME" ]; then
        # 格式化NameNode
        log_info "格式化HDFS NameNode..."
        export JAVA_HOME=$JAVA_HOME
        export HADOOP_HOME=$HADOOP_HOME
        export PATH=$HADOOP_HOME/bin:$PATH
        
        $HADOOP_HOME/bin/hdfs namenode -format -force
        
        log_info "✅ Hadoop初始化完成"
    else
        log_warn "Hadoop未安装，跳过初始化"
    fi
}

# 主函数
main() {
    echo "============================================================================="
    echo "192.168.121.157 服务器 Hadoop/Spark 安装脚本"
    echo "============================================================================="
    echo "安装时间: $(date)"
    echo ""
    
    check_root
    install_java8
    install_hadoop
    install_spark
    install_hive
    setup_environment
    initialize_hadoop
    
    echo ""
    echo "============================================================================="
    echo "安装完成！"
    echo "============================================================================="
    echo "安装路径:"
    echo "  JAVA_HOME: $JAVA_HOME"
    echo "  HADOOP_HOME: $HADOOP_HOME"
    echo "  SPARK_HOME: $SPARK_HOME"
    echo "  HIVE_HOME: $HIVE_HOME"
    echo ""
    echo "下一步："
    echo "1. 重新登录或执行: source /root/.bashrc"
    echo "2. 运行启动脚本: ./start-hadoop-spark-157.sh"
    echo "3. 检查服务状态: ./check-server-157.sh"
    echo ""
}

# 执行主函数
main "$@"
