package com.zyyj.cere.service;

import com.zyyj.cere.pojo.kafka.KafkaConnectorResp;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.rpc.thrift.server.ThriftServiceHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;


/**
 * demo实现类
 */
@Slf4j
@Service
@ThriftServiceHandler
public class DemoServiceImpl implements DemoService{
    @Autowired
    private RestTemplate rest;

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public Long getId(Long id) {
        return id+1;
    }

    @Override
    public String get(){
        ResponseEntity<String> responseEntity = restTemplate.getForEntity("http://**************:8005", String.class);
        String str = responseEntity.getBody();
        System.out.println(str);
        return str;
    }

    public void deleteKafkaConnector() throws ApplicationException {
        // url
        String url = "http://localhost:8083/connectors";
        // request
        HttpEntity request = new HttpEntity("");
        // do request
        try {
            System.out.println("================");
            String[] resps = rest.getForObject(url, String[].class);
            System.out.println(resps);


            List<Thread> threadList = new ArrayList<>();
            for (String resp: resps) {
                Thread thread = new Thread(new Runnable() {
                    @Override
                    public void run() {
                        String delURL = "http://localhost:8083/connectors/" + resp;
                        rest.delete(delURL);
                    }
                });
                thread.start();
                threadList.add(thread);
            }

        }catch (RestClientException e){
            e.printStackTrace();
            throw new ApplicationException(e.getMessage());
        }
    }



}
