package com.zyyj.rpc;

import com.zyyj.cere.service.*;
import com.zyyj.rpc.thrift.client.AbstractThriftClientConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CereRpcConfig extends AbstractThriftClientConfiguration {

    @Value("${service.cerebro}")
    private String serverName;

    @Override
    protected String getDiscoveryName() {
        return serverName;
    }

    @Bean
    public DemoService demoService() {
        return getClient(DemoService.class);
    }

    @Bean
    public DatasourceService datasourceService() {
        return getClient(DatasourceService.class);
    }

    @Bean
    public SubjectService subjectService() {
        return getClient(SubjectService.class);
    }

    @Bean
    public BusinessService businessService() {
        return getClient(BusinessService.class);
    }

    @Bean
    public ApplicationService applicationService() {
        return getClient(ApplicationService.class);
    }

    @Bean
    public ApiService apiService() {
        return getClient(ApiService.class);
    }

    @Bean
    public ApiTestService apiTestService() {
        return getClient(ApiTestService.class);
    }
    @Bean
    public TableModelingService tableModelingService() {
        return getClient(TableModelingService.class);
    }

    @Bean
    public TableService tableService() {
        return getClient(TableService.class);
    }

    @Bean
    public TunnelService tunnelService() {
        return getClient(TunnelService.class);
    }

    @Bean
    public DatasetService datasetService() {
        return getClient(DatasetService.class);
    }
}
