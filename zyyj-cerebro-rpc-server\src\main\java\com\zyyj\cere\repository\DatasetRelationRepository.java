package com.zyyj.cere.repository;

import com.zyyj.cere.pojo.entity.DatasetRelationEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Component;


@Component
public interface DatasetRelationRepository extends JpaRepository<DatasetRelationEntity, Integer>, JpaSpecificationExecutor<DatasetRelationEntity> {
    DatasetRelationEntity findByDatasetId(Integer datasetId);

    @Modifying
    @Query(value = "update dataset_relation set inner_attribute = ?1,customize_attribute = ?2 where dataset_id = ?3",nativeQuery = true)
    int updateByDatasetId(String innerAttribute,String customizeAttribute,Integer datasetId);

    @Modifying
    @Query(value = "update dataset_relation set inner_attribute = ?1 where dataset_id = ?2",nativeQuery = true)
    void updateInnerAttributeByDatasetId(String innerAttribute, Integer datasetId);
}