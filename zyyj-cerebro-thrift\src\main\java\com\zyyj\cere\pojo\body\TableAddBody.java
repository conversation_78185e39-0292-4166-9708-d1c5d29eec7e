package com.zyyj.cere.pojo.body;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Pattern;

@ThriftStruct
@Setter
@NoArgsConstructor
@ToString
@Builder
public class TableAddBody {
    // 中文名
    private String name;

    @Pattern(regexp="^[a-zA-Z]\\w{1,50}$", message = "表名不合法")
    private String tableName;
    private String fieldJson;
    private Long businessId;
    private String fileUrl;
    private Integer source;

    @ThriftConstructor
    public TableAddBody(String name, String tableName, String fieldJson, Long businessId, String fileUrl, Integer source) {
        this.name = name;
        this.tableName = tableName;
        this.fieldJson = fieldJson;
        this.businessId = businessId;
        this.fileUrl = fileUrl;
        this.source = source;
    }


    @ThriftField(1)
    public String getName() {
        return name;
    }

    @ThriftField(2)
    public String getTableName() {
        return tableName;
    }

    @ThriftField(3)
    public String getFieldJson() {
        return fieldJson;
    }

    @ThriftField(4)
    public Long getBusinessId() {
        return businessId;
    }

    @ThriftField(5)
    public String getFileUrl() {
        return fileUrl;
    }

    @ThriftField(6)
    public Integer getSource() {
        return source;
    }


}
