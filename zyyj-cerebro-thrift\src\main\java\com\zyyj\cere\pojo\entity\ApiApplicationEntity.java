package com.zyyj.cere.pojo.entity;

/**
 * <AUTHOR>
 * @date 2020/11/13 15:33
 */

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.sdk.processor.annotation.ThriftPaged;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 应用API关系表
 */
@Setter
@ThriftStruct
@ThriftPaged
@NoArgsConstructor
@ToString
@Builder
@Entity
@Table(name = "api_application")
@EntityListeners(ApiApplicationEntity.class)
public class ApiApplicationEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, length = 11)
    private Integer id;

    @Column(name = "application_id", nullable = false, length = 11)
    private Integer applicationId;

    @Column(name = "api_id", nullable = false, length = 11)
    private Integer apiId;

    @ThriftConstructor
    public ApiApplicationEntity(Integer id, Integer applicationId, Integer apiId) {
        this.id = id;
        this.applicationId = applicationId;
        this.apiId = apiId;
    }

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public Integer getApplicationId() {
        return applicationId;
    }

    @ThriftField(3)
    public Integer getApiId() {
        return apiId;
    }
}
