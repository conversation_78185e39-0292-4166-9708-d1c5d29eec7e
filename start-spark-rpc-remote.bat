@echo off
echo 启动Spark RPC服务 (使用***************远程集群)...

set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_141

"%JAVA_HOME%\bin\java" ^
    -Xms512m ^
    -Xmx1g ^
    -Dserver.port=8081 ^
    -Dspring.profiles.active=dev ^
    -Dspring.application.name=ZYYJ-SPARK-THRIFT ^
    -Deureka.instance.appname=ZYYJ-SPARK-THRIFT ^
    -Deureka.instance.non-secure-port=8081 ^
    -Deureka.client.serviceUrl.defaultZone=******************************************/eureka/ ^
    -Dspring.main.allow-bean-definition-overriding=true ^
    -Dspark.master=spark://***************:7077 ^
    -Dspark.appName=ZYYJ-SPARK-RPC-SERVER ^
    -Dspark.warehouseDir=hdfs://***************:9000/spark-warehouse ^
    -Dspark.metastoreUris=thrift://***************:9083 ^
    -Dspark.driver=localhost ^
    -Dspark.driver.bindAddress=0.0.0.0 ^
    -Dzyyj.rpc.thrift.server.listen_port=9009 ^
    -jar zyyj-spark-rpc-server\target\zyyj-spark-rpc-server.jar
