package com.zyyj.spark.pojo.vo;


import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.HashMap;

@AllArgsConstructor
@NoArgsConstructor
public
class KafkaOffset {
    // topic name;
    String name;
    // key 是 partition, value 是 offset;
    HashMap<String, Long> offsets;
    // 是否在库中存在
    boolean existInTable = false;

    public String getStartingOffsetJSON(){
        Gson gson = new Gson();
        HashMap<String, Object> startingOffsetsMap = new HashMap();
        startingOffsetsMap.put(name, offsets);
        return gson.toJson(startingOffsetsMap);
    }

    public String getName() {
        return name;
    }

    public KafkaOffset setName(String name) {
        this.name = name;
        return this;
    }

    public HashMap<String, Long> getOffsets() {
        return offsets;
    }

    public KafkaOffset setOffsets(HashMap<String, Long> offsets) {
        this.offsets = offsets;
        return this;
    }

    public boolean isExistInTable() {
        return existInTable;
    }

    public KafkaOffset setExistInTable(boolean existInTable) {
        this.existInTable = existInTable;
        return this;
    }


    public void setOffset(String partition, Long offset){
        this.offsets.put(partition, offset);
    }

    public String getOffsetString(){
        Gson gson = new Gson();
        return gson.toJson(offsets);
    }

    // 获取存储在数据库中的数据
    public String getTableValue(){
        String[] values = new String[]{ "'" + getName() + "'" , "'" + getOffsetString() + "'"};
        return String.join("," , values);
    }
}