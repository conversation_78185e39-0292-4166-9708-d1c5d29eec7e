package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2020/11/17 14:59
 */
@ThriftStruct
@Setter
@Builder
@ToString
@NoArgsConstructor
public class ApiNameDTO {
    private Integer id;
    private String name;

    @ThriftConstructor
    public ApiNameDTO(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public String getName() {
        return name;
    }
}
