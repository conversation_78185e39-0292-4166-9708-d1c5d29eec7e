package com.zyyj.cere.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.zyyj.cere.pojo.entity.BusinessEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/9/24 15:32
 */
@ThriftService
public interface BusinessService {

    /**
     * @Description: 主题域列表
     * @Param:
     * @return: Map<String, List < BusinessEntity>>
     * @Author: bravelee
     * @Date: 2020/9/23
     */
    @ThriftMethod
    Map<String, List<BusinessEntity>> getBusinessList(Integer subjectId);

    /**
     * @Description: 添加数据集
     * @Param: [businessEntity]
     * @return:
     * @Author: bravelee
     * @Date: 2020/9/24
     */
    @ThriftMethod
    String addBusiness(BusinessEntity businessEntity);

    /**
     * @Description:编辑业务集
     * @Param: [businessEntity]
     * @return:
     * @Author: bravelee
     * @Date: 2020/9/22
     */
    @ThriftMethod
    String editBusiness(BusinessEntity businessEntity);

    /**
     * @Description: 删除业务集
     * @Param: [businessEntity]
     * @return:
     * @Author: bravelee
     * @Date: 2020/9/23
     */
    @ThriftMethod
    String delBusiness(Integer id);
}
