# 测试创建表接口并查看调试日志
param(
    [string]$TableName = "test_debug_table_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
)

Write-Host "🧪 测试创建表接口 - 调试模式" -ForegroundColor Green
Write-Host "表名: $TableName" -ForegroundColor Yellow

# 准备请求体
$body = @{
    name = "调试测试表"
    tableName = $TableName
    fieldJson = '[{"field":"id","comment":"ID","type":"long","priKey":1},{"field":"name","comment":"姓名","type":"STRING","priKey":0},{"field":"age","comment":"年龄","type":"int","priKey":0}]'
    businessId = 1
    source = 1
} | ConvertTo-Json -Depth 3

Write-Host "请求体:" -ForegroundColor Cyan
Write-Host $body -ForegroundColor White

Write-Host "`n🚀 发送创建表请求..." -ForegroundColor Green

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8005/api/v1/table/add" -Method POST -Body $body -ContentType "application/json"
    Write-Host "✅ 请求成功!" -ForegroundColor Green
    Write-Host "响应: $($response | ConvertTo-Json)" -ForegroundColor White
} catch {
    Write-Host "❌ 请求失败!" -ForegroundColor Red
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "状态码: $statusCode" -ForegroundColor Red
    }
}

Write-Host "`n📋 现在请查看以下日志文件中的调试信息:" -ForegroundColor Yellow
Write-Host "  本地 Cerebro RPC: logs\cerebro-rpc.log" -ForegroundColor White
Write-Host "  远程 Spark RPC: /opt/logs/spark-rpc.log (在158服务器上)" -ForegroundColor White

Write-Host "`n💡 建议的查看命令:" -ForegroundColor Yellow
Write-Host "  本地: Get-Content logs\cerebro-rpc.log -Tail 20" -ForegroundColor White
Write-Host "  远程: ssh root@*************** 'tail -20 /opt/logs/spark-rpc.log'" -ForegroundColor White

Write-Host "`n🔍 查找关键调试信息:" -ForegroundColor Yellow
Write-Host "  - '=== 准备调用Spark RPC创建表 ==='" -ForegroundColor White
Write-Host "  - '=== createTable方法开始执行 ==='" -ForegroundColor White
Write-Host "  - 'Client was disconnected'" -ForegroundColor White
Write-Host "  - 任何异常堆栈信息" -ForegroundColor White
