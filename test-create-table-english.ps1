# 测试创建表接口 - 使用英文字段名
param(
    [string]$TableName = "test_english_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
)

Write-Host "🧪 测试创建表接口 - 英文字段名" -ForegroundColor Green
Write-Host "表名: $TableName" -ForegroundColor Yellow

# 准备请求体 - 使用英文字段名和注释
$body = @{
    name = "English Test Table"
    tableName = $TableName
    fieldJson = '[{"field":"id","comment":"Primary ID","type":"long","priKey":1},{"field":"name","comment":"User Name","type":"STRING","priKey":0},{"field":"age","comment":"User Age","type":"int","priKey":0}]'
    businessId = 1
    source = 1
} | ConvertTo-Json -Depth 3

Write-Host "请求体:" -ForegroundColor Cyan
Write-Host $body -ForegroundColor White

Write-Host "`n🚀 发送创建表请求..." -ForegroundColor Green

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8005/api/v1/table/add" -Method POST -Body $body -ContentType "application/json"
    Write-Host "✅ 请求成功!" -ForegroundColor Green
    Write-Host "响应: $($response | ConvertTo-Json)" -ForegroundColor White
} catch {
    Write-Host "❌ 请求失败!" -ForegroundColor Red
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "状态码: $statusCode" -ForegroundColor Red
    }
}

Write-Host "`n📋 查看调试日志:" -ForegroundColor Yellow
Write-Host "  Get-Content logs\cerebro-rpc-debug.log | Select-String -Pattern 'English Test Table|$TableName' -Context 3" -ForegroundColor White

Write-Host "`n🔍 预期的SQL语句应该是:" -ForegroundColor Yellow
$expectedSQL = "CREATE OR REPLACE TABLE ``$TableName`` (``id`` long COMMENT 'Primary ID',``name`` STRING COMMENT 'User Name',``age`` int COMMENT 'User Age') USING DELTA PARTITIONED BY (``id``) COMMENT 'English Test Table'"
Write-Host $expectedSQL -ForegroundColor White
