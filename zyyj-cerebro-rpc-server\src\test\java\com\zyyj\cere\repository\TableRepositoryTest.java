package com.zyyj.cere.repository;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.zyyj.cere.CerebroRPCServerApplication;
import com.zyyj.cere.config.JpaQueryConfig;
import com.zyyj.cere.pojo.dto.TableListDTO;

import com.zyyj.cere.pojo.entity.TableEntity;
import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CerebroRPCServerApplication.class)
public class TableRepositoryTest extends TestCase {

    @Autowired
    TableRepository tableRepository;

    @Autowired
    JPAQueryFactory queryFactory;

    @Test
    public void testList(){
        Long bus = 1L;
        String s = "1,2,3";
        String name = "试1";

        tableRepository.findAll(new Specification<TableEntity>() {
            @Override
            public Predicate toPredicate(Root<TableEntity> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                List<Predicate> predicateList = new ArrayList<>();
                if (!StringUtils.isEmpty(bus)){
                    predicateList.add(criteriaBuilder.equal(root.get("businessId"), bus));
                }

                if (!StringUtils.isEmpty(s)){
                    CriteriaBuilder.In<Byte> in = criteriaBuilder.in(root.get("status"));
                    String[] split = s.split(",");
                    for (String string : split) {
                        in.value(Byte.valueOf(string));
                    }
                    predicateList.add(in);
                }

                if (!StringUtils.isEmpty(name)){
                    predicateList.add(criteriaBuilder.like(root.get("name"), "%" + name +"%"));
                }

                Predicate[] predicateArr = new Predicate[predicateList.size()];
                return  criteriaBuilder.and(predicateList.toArray(predicateArr));
            }
        }).forEach(System.out::println);
    }

    @Test
    public void testFindByStatusAndBusinessIdAndNameLike() {
        TableEntity tableEntity = TableEntity.builder()
                .status((byte)1)
                .businessId(1L)
                .name(null)
                .build();
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("name" ,ExampleMatcher.GenericPropertyMatchers.contains());//全部模糊查询，即%{address}%

        Example<TableEntity> e = Example.of(tableEntity,matcher);

        tableRepository.findAll(e).forEach(System.out::println);
    }

    @Test
    public void testJpaDsl(){
//        QTableEntity tableEntity = QTableEntity.tableEntity;
//
//        Long bus = 1L;
//        String s = "1,2,3";
//        String name = "";
//
//        // 动态查询示例
//        BooleanBuilder builder = new BooleanBuilder();
//
//        List<Predicate> predicateList = new ArrayList<>();
//        if (!StringUtils.isEmpty(bus)){
//            builder.and(tableEntity.businessId.eq(bus));
//        }
//
//        if (!StringUtils.isEmpty(s)){
//            List<Byte> l = new ArrayList<Byte>();
//            String[] split = s.split(",");
//            for (String string : split) {
//                l.add(Byte.valueOf(string));
//            }
//            builder.and(tableEntity.status.in(l));
//        }
//
//        if (!StringUtils.isEmpty(name)){
//            builder.and(tableEntity.name.like("%"+name+"%"));
//        }
//
//        queryFactory.select(
//                Projections.bean(TableListDTO.class,tableEntity.id,tableEntity.name,tableEntity.typeId,tableEntity.status,tableEntity.source)
//        )
//                .from(tableEntity)
//                .where(builder)
//                .fetch().forEach(System.out::println);

    }
}