package com.zyyj.cere.pojo.kafka;

import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class KafkaConnectorResp extends KafkaConnectorReq {

    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    @Setter
    public class Task {
        String connector;
        String task;
    }

    String type;
    Task[] tasks;


    public String getConfigString(){
        if (this.getConfig() == null){
            return "";
        }
        return new Gson().toJson(this.getConfig());
    }
}