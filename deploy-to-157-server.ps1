# =============================================================================
# 一键部署脚本 - 部署到***************服务器
# =============================================================================

param(
    [string]$ServerIP = "***************",
    [string]$Username = "root",
    [switch]$CheckOnly,
    [switch]$Help
)

if ($Help) {
    Write-Host "用法: .\deploy-to-157-server.ps1 [-ServerIP <IP>] [-Username <用户名>] [-CheckOnly]"
    Write-Host ""
    Write-Host "参数:"
    Write-Host "  -ServerIP   服务器IP地址 (默认: ***************)"
    Write-Host "  -Username   SSH用户名 (默认: root)"
    Write-Host "  -CheckOnly  仅检查环境，不执行部署"
    Write-Host "  -Help       显示帮助信息"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  .\deploy-to-157-server.ps1                    # 完整部署"
    Write-Host "  .\deploy-to-157-server.ps1 -CheckOnly         # 仅检查环境"
    Write-Host "  .\deploy-to-157-server.ps1 -ServerIP ***************  # 指定服务器"
    exit 0
}

function Log-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Log-Warn {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Log-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Log-Step {
    param([string]$Message)
    Write-Host "[STEP] $Message" -ForegroundColor Blue
}

# 检查本地文件
function Check-LocalFiles {
    Log-Step "检查本地脚本文件..."
    
    $requiredFiles = @(
        "check-server-157.sh",
        "install-hadoop-spark-157.sh", 
        "start-hadoop-spark-157.sh",
        "stop-hadoop-spark-157.sh"
    )
    
    $missingFiles = @()
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            $missingFiles += $file
        }
    }
    
    if ($missingFiles.Count -gt 0) {
        Log-Error "缺少以下脚本文件:"
        $missingFiles | ForEach-Object { Write-Host "  - $_" }
        return $false
    }
    
    Log-Info "✅ 所有脚本文件已准备就绪"
    return $true
}

# 测试SSH连接
function Test-SSHConnection {
    Log-Step "测试SSH连接..."
    
    try {
        $result = ssh -o ConnectTimeout=5 "${Username}@${ServerIP}" "echo 'SSH连接测试成功'" 2>$null
        if ($result -like "*SSH连接测试成功*") {
            Log-Info "✅ SSH连接正常"
            return $true
        } else {
            Log-Error "❌ SSH连接失败"
            return $false
        }
    } catch {
        Log-Error "❌ SSH连接异常: $($_.Exception.Message)"
        return $false
    }
}

# 上传脚本文件
function Upload-Scripts {
    Log-Step "上传脚本文件到服务器..."
    
    $scripts = @(
        "check-server-157.sh",
        "install-hadoop-spark-157.sh",
        "start-hadoop-spark-157.sh", 
        "stop-hadoop-spark-157.sh"
    )
    
    foreach ($script in $scripts) {
        try {
            Log-Info "上传 $script..."
            scp $script "${Username}@${ServerIP}:/root/"
            if ($LASTEXITCODE -ne 0) {
                Log-Error "上传 $script 失败"
                return $false
            }
        } catch {
            Log-Error "上传 $script 异常: $($_.Exception.Message)"
            return $false
        }
    }
    
    # 设置执行权限
    Log-Info "设置脚本执行权限..."
    ssh "${Username}@${ServerIP}" "chmod +x /root/*.sh"
    
    Log-Info "✅ 脚本文件上传完成"
    return $true
}

# 执行环境检查
function Execute-EnvironmentCheck {
    Log-Step "执行服务器环境检查..."
    
    Log-Info "运行环境检查脚本..."
    ssh "${Username}@${ServerIP}" "/root/check-server-157.sh"
    
    if ($LASTEXITCODE -eq 0) {
        Log-Info "✅ 环境检查完成"
        return $true
    } else {
        Log-Error "❌ 环境检查失败"
        return $false
    }
}

# 执行安装部署
function Execute-Installation {
    Log-Step "执行Hadoop/Spark安装..."
    
    Log-Info "运行安装脚本..."
    ssh "${Username}@${ServerIP}" "/root/install-hadoop-spark-157.sh"
    
    if ($LASTEXITCODE -eq 0) {
        Log-Info "✅ 安装完成"
        return $true
    } else {
        Log-Error "❌ 安装失败"
        return $false
    }
}

# 启动服务
function Start-Services {
    Log-Step "启动Hadoop/Spark服务..."
    
    Log-Info "运行启动脚本..."
    ssh "${Username}@${ServerIP}" "/root/start-hadoop-spark-157.sh"
    
    if ($LASTEXITCODE -eq 0) {
        Log-Info "✅ 服务启动完成"
        return $true
    } else {
        Log-Error "❌ 服务启动失败"
        return $false
    }
}

# 验证部署结果
function Verify-Deployment {
    Log-Step "验证部署结果..."
    
    # 检查端口连接
    $ports = @(7077, 8080, 9000, 9083, 8088, 9870)
    $portNames = @("Spark Master", "Spark Web UI", "HDFS NameNode", "Hive Metastore", "YARN ResourceManager", "HDFS Web UI")
    
    Write-Host ""
    Write-Host "=== 端口连接测试 ==="
    for ($i = 0; $i -lt $ports.Count; $i++) {
        $port = $ports[$i]
        $name = $portNames[$i]
        
        try {
            $result = Test-NetConnection -ComputerName $ServerIP -Port $port -InformationLevel Quiet -WarningAction SilentlyContinue
            if ($result) {
                Log-Info "✅ $name (端口 $port) - 连接正常"
            } else {
                Log-Warn "❌ $name (端口 $port) - 连接失败"
            }
        } catch {
            Log-Warn "❌ $name (端口 $port) - 测试异常"
        }
    }
    
    # 检查Web界面
    Write-Host ""
    Write-Host "=== Web界面测试 ==="
    $webUrls = @(
        @{Url="http://$ServerIP:8080"; Name="Spark Master Web UI"},
        @{Url="http://$ServerIP:9870"; Name="HDFS Web UI"},
        @{Url="http://$ServerIP:8088"; Name="YARN Web UI"}
    )
    
    foreach ($urlInfo in $webUrls) {
        try {
            $response = Invoke-WebRequest -Uri $urlInfo.Url -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Log-Info "✅ $($urlInfo.Name) - 可访问"
            } else {
                Log-Warn "❌ $($urlInfo.Name) - 状态码: $($response.StatusCode)"
            }
        } catch {
            Log-Warn "❌ $($urlInfo.Name) - 无法访问"
        }
    }
    
    Write-Host ""
    Write-Host "=== 访问地址 ==="
    Write-Host "Spark Master Web UI: http://$ServerIP:8080"
    Write-Host "HDFS Web UI: http://$ServerIP:9870"
    Write-Host "YARN ResourceManager: http://$ServerIP:8088"
}

# 生成后续操作指南
function Generate-NextSteps {
    Log-Step "生成后续操作指南..."
    
    Write-Host ""
    Write-Host "=== 部署完成！后续操作指南 ===" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "1. 服务管理命令:"
    Write-Host "   启动服务: ssh $Username@$ServerIP '/root/start-hadoop-spark-157.sh'"
    Write-Host "   停止服务: ssh $Username@$ServerIP '/root/stop-hadoop-spark-157.sh'"
    Write-Host "   检查状态: ssh $Username@$ServerIP '/root/check-status.sh'"
    Write-Host ""
    
    Write-Host "2. Web界面访问:"
    Write-Host "   Spark Master: http://$ServerIP:8080"
    Write-Host "   HDFS: http://$ServerIP:9870"
    Write-Host "   YARN: http://$ServerIP:8088"
    Write-Host ""
    
    Write-Host "3. 现在可以配置Windows端使用远程集群:"
    Write-Host "   .\start-with-remote-cluster.ps1"
    Write-Host ""
    
    Write-Host "4. 测试HDFS操作:"
    Write-Host "   ssh $Username@$ServerIP 'hdfs dfs -ls /'"
    Write-Host "   ssh $Username@$ServerIP 'hdfs dfs -mkdir /test'"
    Write-Host ""
    
    Write-Host "5. 测试Spark作业:"
    Write-Host "   ssh $Username@$ServerIP 'spark-submit --class org.apache.spark.examples.SparkPi --master spark://\$(hostname):7077 \$SPARK_HOME/examples/jars/spark-examples*.jar 10'"
    Write-Host ""
    
    Write-Host "6. 查看日志:"
    Write-Host "   ssh $Username@$ServerIP 'tail -f /var/log/hadoop/*.log'"
    Write-Host "   ssh $Username@$ServerIP 'tail -f /var/log/spark/*.out'"
    Write-Host ""
}

# 主函数
function Main {
    Write-Host "=== *************** 服务器一键部署脚本 ===" -ForegroundColor Cyan
    Write-Host "服务器: $ServerIP"
    Write-Host "用户: $Username"
    Write-Host "模式: $(if ($CheckOnly) { '仅检查' } else { '完整部署' })"
    Write-Host ""
    
    # 检查本地文件
    if (-not (Check-LocalFiles)) {
        return
    }
    
    # 测试SSH连接
    if (-not (Test-SSHConnection)) {
        Log-Error "无法连接到服务器，请检查:"
        Log-Error "1. 服务器IP地址是否正确"
        Log-Error "2. SSH密钥是否配置"
        Log-Error "3. 用户名是否正确"
        Log-Error "4. 服务器是否在线"
        return
    }
    
    # 上传脚本
    if (-not (Upload-Scripts)) {
        return
    }
    
    # 执行环境检查
    if (-not (Execute-EnvironmentCheck)) {
        return
    }
    
    # 如果只是检查模式，到此结束
    if ($CheckOnly) {
        Log-Info "检查模式完成，未执行安装部署"
        return
    }
    
    # 执行安装
    Log-Info "开始安装部署..."
    if (-not (Execute-Installation)) {
        Log-Error "安装失败，请检查服务器日志"
        return
    }
    
    # 启动服务
    if (-not (Start-Services)) {
        Log-Error "服务启动失败，请检查配置"
        return
    }
    
    # 验证部署
    Verify-Deployment
    
    # 生成后续操作指南
    Generate-NextSteps
    
    Write-Host ""
    Write-Host "=== 部署完成！ ===" -ForegroundColor Green
}

# 执行主函数
Main
