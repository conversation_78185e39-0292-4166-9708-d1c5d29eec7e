package com.zyyj.cere.pojo.entity;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.sf.jsqlparser.expression.DateTimeLiteralExpression;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 数据标准
 */

@ThriftStruct
@Data
@NoArgsConstructor
@Entity
@Table(name = "dataset_standard")
@ApiModel(value = "DatasetStandardEntity", description = "数据集标准")
public class DatasetStandardEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false,length = 11)
    @ApiModelProperty(name = "id", value = "ID")
    private Integer id;


    /**
     * 数据集名
     */
    @NotNull(message = "数据集ID不可为空")
    @ApiModelProperty(name = "datasetId", value = "数据集id")
    @Column(name = "dataset_id", nullable = false,length = 11)
    private Integer datasetId;

    /**
     * content
     */
    @ApiModelProperty(name = "content", value = "内容")
    @Column(name = "content" ,length = 11)
    private String content;

    /**
     * 0 正常 2 删除
     */
    @ApiModelProperty(name = "status", value = "状态")
    @Column(name = "status")
    private Integer status = 0;

    /**
     * 编号
     */
    @NotNull(message = "编号")
    @ApiModelProperty(name = "code", value = "编号")
    @Column(name = "code", length = 100)
    private String code;

    /**
     * 中文名称
     */
    @NotNull(message = "中文名称")
    @ApiModelProperty(name = "cname", value = "中文名称")
    @Column(name = "cname", length = 100)
    private String cname;

    /**
     * 英文名称
     */
    @ApiModelProperty(name = "ename", value = "英文名称")
    @Column(name = "ename", length = 100)
    private String ename;

    /**
     * 描述
     */
    @ApiModelProperty(name = "comment", value = "描述")
    @Column(name = "comment", length = 255)
    private String comment;

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public Integer getDatasetId() {
        return datasetId;
    }

    @ThriftField(3)
    public String getContent() {
        return content;
    }

    @ThriftField(4)
    public Integer getStatus() {
        return status;
    }

    @ThriftField(5)
    public String getCode() {
        return code;
    }

    @ThriftField(6)
    public String getCname() {
        return cname;
    }

    @ThriftField(7)
    public String getEname() {
        return ename;
    }

    @ThriftField(8)
    public String getComment() {
        return comment;
    }

    @ThriftConstructor
    public DatasetStandardEntity(Integer id, @NotNull(message = "数据集ID不可为空") Integer datasetId, String content, Integer status, @NotNull(message = "编号") String code, @NotNull(message = "中文名称") String cname, String ename, String comment) {
        this.id = id;
        this.datasetId = datasetId;
        this.content = content;
        this.status = status;
        this.code = code;
        this.cname = cname;
        this.ename = ename;
        this.comment = comment;
    }
}
