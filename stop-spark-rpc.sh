#!/bin/bash
# 停止 Spark RPC 服务脚本

echo "Stopping Spark RPC Server..."

# 查找并停止 Java 进程
PIDS=$(ps aux | grep "zyyj-spark-rpc-server.jar" | grep -v grep | awk '{print $2}')

if [ -z "$PIDS" ]; then
    echo "No Spark RPC Server process found."
else
    for PID in $PIDS; do
        echo "Killing process $PID"
        kill -9 $PID
    done
    echo "Spark RPC Server stopped."
fi

# 检查端口是否释放
echo "Checking ports..."
netstat -tlnp | grep -E '(8081|9009)' || echo "Ports 8081 and 9009 are free."