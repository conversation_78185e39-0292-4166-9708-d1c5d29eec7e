package com.zyyj.cere.service;


import com.zyyj.cere.pojo.entity.KccEntity;
import com.zyyj.cere.pojo.kafka.KafkaConnectorReq;
import com.zyyj.cere.pojo.kafka.KafkaConnectorResp;
import com.zyyj.cere.pojo.kafka.KCMySqlSourceConfig;
import com.zyyj.cere.property.KafkaProperties;
import com.zyyj.cere.repository.KccRepository;
import com.zyyj.domain.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.KafkaAdminClient;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@ConfigurationProperties(prefix = "kafka")
public class KafkaServiceImpl implements KafkaService {

    private static String CONNECTORS="/connectors";

    // 数据库操作
    @Autowired
    KccRepository kccRepository;

    @Autowired
    KafkaProperties kafkaProperty;

    @Autowired
    private RestTemplate rest;

    private String getConnectorURL(){
        return kafkaProperty.getConnectorHost().concat(CONNECTORS);
    }

    KafkaConnectorResp CreateMySqlSourceConnector(KCMySqlSourceConfig config) throws ApplicationException {
        HashMap<String, String> conf = config.getKCConfig();
        log.warn(conf.toString());
        KafkaConnectorReq req = new KafkaConnectorReq(config.getName() ,conf);
        return requestKafkaConnector(req);
    }

    KafkaConnectorResp requestKafkaConnector(KafkaConnectorReq req) throws ApplicationException{
        // url
        String url = getConnectorURL();
        // header
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        // request
        HttpEntity request = new HttpEntity<>(req, headers);
        // do request
        try {
            KafkaConnectorResp resp = rest.postForObject(url,request, KafkaConnectorResp.class);
            return resp;
        }catch (RestClientException e){
            e.printStackTrace();
            throw new ApplicationException(e.getMessage());
        }
    }

    /* 删除kafka connector */
    void delConnector(String kafkaName)throws ApplicationException{
        // url
        String url = getConnectorURL().concat("/").concat(kafkaName);

        // do request
        try {
            rest.delete(url);
        }catch (RestClientException e){
            e.printStackTrace();
        }
    }

    /* 停止 kafka connector */
    void pauseConnector(String kafkaName)throws ApplicationException{
        // url
        String url = getConnectorURL().concat("/").concat(kafkaName).concat("/").concat("pause");

        // do request
        try {
            rest.put(url, null);
        }catch (RestClientException e){
            throw new ApplicationException(e.getMessage());
        }
    }


    /* 停止 kafka connector */
    void resumeConnector(String kafkaName)throws ApplicationException {
        // url
        String url = getConnectorURL().concat("/").concat(kafkaName).concat("/").concat("resume");

        // do request
        try {
            rest.put(url, null);
        }catch (RestClientException e){
            throw new ApplicationException(e.getMessage());
        }
    }


    // 删除kafka topic 和 connector
    @Override
    public void deleteConnector(){
        List<KccEntity> list = kccRepository.findAll();
        List<String> activeConnectors = list.stream().map(kccEntity -> {
           return kccEntity.getName();
        }).collect(Collectors.toList());

        String url = getConnectorURL();
        try {
            String[] runningConnectors = rest.getForObject(url, String[].class);
            for (String connector: runningConnectors) {
                // 运行的connector 不处于 激活状态，直接删除
                if (!activeConnectors.contains(connector)){
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                delConnector(connector);
                            }catch (Exception e){
                                e.printStackTrace();
                            }
                        }
                    }).start();
                }
            }
        } catch (Exception e){
            e.printStackTrace();
        }
    }

    private Properties getKafkaProperties(){
        Properties properties = new Properties();
        properties.put("bootstrap.servers", kafkaProperty.getBootstrapServers());
        return properties;
    }

    private AdminClient getKafkaAdminClient(){
        Properties properties = this.getKafkaProperties();
        AdminClient adminClient =  KafkaAdminClient.create(properties);
        return adminClient;
    }


    // 获取所有 topic 名称
    public Set<String> getTopicList() throws Exception {
        AdminClient adminClient = getKafkaAdminClient();
        ListTopicsResult listTopicsResult = adminClient.listTopics();
        Set<String> topics = listTopicsResult.names().get(10, TimeUnit.SECONDS);
        return topics;
    }

    //删除topic
    public void deleteTopics(String serverName){
        try {
            Set<String> topics = this.getTopicList();
            Set<String> list = new HashSet();

            for (String topic : topics) {
                if (topic.contains(serverName)){
                    list.add(topic);
                }
            }
            // 删除
            deleteTopics(list);
        } catch (Exception e){
            e.printStackTrace();
        }
    }

    //删除topic
    public void deleteTopics(Collection<String> topics){
        AdminClient adminClient = getKafkaAdminClient();
        try {
            adminClient.deleteTopics(topics).all().get();
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
    }
}
