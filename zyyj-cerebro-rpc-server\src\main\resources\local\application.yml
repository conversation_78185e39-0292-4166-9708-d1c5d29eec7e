server:
  port: 8006

eureka:
  instance:
    prefer-ip-address: true
    non-secure-port: 9006
  client:
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: ******************************************/eureka/

spring:
  application:
    name: ZYYJ-CEREBRO-THRIFT-LOCAL-HH
  datasource:
    url: ***************************************************************************************************
    username: root
    password: m17JJ36azzNNBsd8UR
    driver-class-name: com.mysql.cj.jdbc.Driver
#    test-on-borrow: true
#    validation-query: SELECT 1
  jpa:
    hibernate:
      jdbc:
        batch_size: 500
        batch_versioned_data: true
        order_inserts: true
        order_updates: true
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
  redis:
    database: 1
    host: *************
    port: 6379
    password: Ckjl12@#jk11
    timeout: 60000
service:
  #thrift 客户端配置
  spark: ZYYJ-SPARK-THRIFT-LOCAL-HH
zyyj:
  rpc:
    thrift:
      server:
        listen_port: 9006
# 打印sql
#logging:
#  config: config/log4j2.xml
