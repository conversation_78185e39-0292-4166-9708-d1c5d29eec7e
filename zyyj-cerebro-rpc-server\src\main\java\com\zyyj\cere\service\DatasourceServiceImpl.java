package com.zyyj.cere.service;


import com.zyyj.cere.pojo.dto.*;
import com.zyyj.cere.pojo.entity.DatasourceEntity;
import com.zyyj.cere.pojo.entity.DatasourceTypeEntity;
import com.zyyj.cere.repository.DatasourceRepository;
import com.zyyj.cere.repository.DatasourceTypeRepository;
import com.zyyj.cere.utils.PublicUtils;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.domain.pagination.Paging;
import com.zyyj.rpc.thrift.server.ThriftServiceHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;

import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据源实现类
 */
@Slf4j
@Service
@ThriftServiceHandler
public class DatasourceServiceImpl implements DatasourceService {

    @Autowired
    DatasourceRepository datasourceRepository;
    @Autowired
    DatasourceTypeRepository datasourceTypeRepository;

    @Override
    public PagedDatasourceDTO getListPage(Paging paging) {
        return new PagedDatasourceDTO(datasourceRepository.findAll(PageRequest.of(paging.getPage(), paging.getSize(), Sort.Direction.DESC, "id")));
    }

    @Override
    public List<DatasourceEntity> getList() throws ApplicationException {
        return datasourceRepository.findAll();
    }

    @Override
    public DatasourceDTO get(Long id) {
        //参数非空校验
        PublicUtils.checkNotNullArr(id);

        return datasourceRepository.queryInfoById(id);
    }

    @Override
    public void set(DatasourceEntity p) {
        //参数非空校验
        PublicUtils.checkNotNullArr(p, p.getTypeId());
        PublicUtils.checkNotEmptyArr(p.getName(), p.getDatabase(), p.getHost(), p.getUsername());

        datasourceRepository.save(p);
    }

    @Override
    public void update(DatasourceEntity p) {
        //参数非空校验
        PublicUtils.checkNotNullArr(p, p.getId(), p.getTypeId());
        PublicUtils.checkNotEmptyArr(p.getName(), p.getDatabase(), p.getHost(), p.getUsername());

        datasourceRepository.save(p);
    }

    @Override
    public void delete(Long id) {
        //参数校验
        PublicUtils.checkNotNullArr(id);

        datasourceRepository.deleteById(id);
    }


    @Override
    public int testConnect(DatasourceEntity data) {
        return PublicUtils.testConnect(data);
    }

    @Override
    public Map<String, List<TableNameDTO>> getDataSourceTables(Integer dataSourceId) {
        boolean exist = datasourceRepository.existsById((long) dataSourceId);
        if (!exist) {
            throw new ApplicationException("该数据源已被删除,请刷新页面");
        }
        List<TableNameDTO> list = new ArrayList<>();
        DatasourceEntity datasourceEntity = datasourceRepository.findById((long) dataSourceId).get();
        Connection connection = PublicUtils.getConnection(datasourceEntity);
        try {
            Statement st = connection.createStatement();
            String sql = "SELECT TABLE_NAME,TABLE_COMMENT FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '" + datasourceEntity.getDatabase() + "'";
            ResultSet rs = st.executeQuery(sql);
            while (rs.next()) {
                TableNameDTO tableNameDTO = new TableNameDTO(rs.getString(2), rs.getString(1));
                list.add(tableNameDTO);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ApplicationException(e.getMessage());
        }
        Map<String, List<TableNameDTO>> map = new HashMap<>();
        map.put("list", list);
        return map;
    }

    @Override
    public Map<String, List<TableFieldDTO>> getDataSourceTableFields(Integer dataSourceId, String tableName) {
        boolean exist = datasourceRepository.existsById((long) dataSourceId);
        if (!exist) {
            throw new ApplicationException("该数据源已被删除,请刷新页面");
        }
        List<TableFieldDTO> list = new ArrayList<>();
        DatasourceEntity datasourceEntity = datasourceRepository.findById((long) dataSourceId).get();
        Connection connection = PublicUtils.getConnection(datasourceEntity);
        try {
            Statement st = connection.createStatement();
            String sql = "SELECT\n" +
                    "   t.COLUMN_NAME AS name,\n" +
                    "   t.COLUMN_COMMENT AS comment,\n" +
                    "   t.COLUMN_TYPE AS type,\n" +
                    "   t.COLUMN_KEY AS prikey\n" +
                    " FROM information_schema.`COLUMNS` t\n" +
                    " WHERE t.TABLE_SCHEMA = '" + datasourceEntity.getDatabase() + "' AND t.TABLE_NAME = upper('" + tableName + "')";
            ResultSet rs = st.executeQuery(sql);
            while (rs.next()) {
                TableFieldDTO tableFieldDTO = new TableFieldDTO();
                tableFieldDTO.setField(rs.getString(1));
                tableFieldDTO.setComment(rs.getString(2));
                tableFieldDTO.setType(rs.getString(3));
                if (rs.getString(4).equals("PRI")) {
                    tableFieldDTO.setPriKey((byte) 1);
                }
                list.add(tableFieldDTO);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ApplicationException(e.getMessage());
        }
        Map<String, List<TableFieldDTO>> map = new HashMap<>();
        map.put("list", list);
        return map;
    }

    @Override
    public List<DatasourceTypeEntity> getDatasourceType() {
        return datasourceTypeRepository.findAll();
    }
}
