package com.zyyj.cere.pojo.dto;


import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.cere.pojo.entity.ApplicationEntity;
import com.zyyj.domain.pagination.Paged;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * describe 通用分页返回
 *
 * <AUTHOR>
 * @date 2020/11/11 10:53
 */

@ThriftStruct
public class PagedApplicationDTO extends Paged<ApplicationEntity> {

    public PagedApplicationDTO(Page<ApplicationEntity> page) {
        super(page);
    }

    @ThriftConstructor
    public PagedApplicationDTO(long total, List<ApplicationEntity> data) {
        super(total, data);
    }
}