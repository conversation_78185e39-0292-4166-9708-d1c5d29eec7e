package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/17 18:29
 */
@ThriftStruct
@Setter
@NoArgsConstructor
@ToString
public class ParamDataDTO {
    private String tableName;
    private Integer method;
    private List<ParamInfoDTO> params;
    private List<ParamInfoDTO> returns;

    @ThriftConstructor
    public ParamDataDTO(String tableName, Integer method, List<ParamInfoDTO> params, List<ParamInfoDTO> returns) {
        this.tableName = tableName;
        this.method = method;
        this.params = params;
        this.returns = returns;
    }

    @ThriftField(1)
    public String getTableName() {
        return tableName;
    }

    @ThriftField(2)
    public Integer getMethod() {
        return method;
    }

    @ThriftField(3)
    public List<ParamInfoDTO> getParams() {
        return params;
    }

    @ThriftField(4)
    public List<ParamInfoDTO> getReturns() {
        return returns;
    }
}
