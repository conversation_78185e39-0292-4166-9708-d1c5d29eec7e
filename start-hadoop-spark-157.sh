#!/bin/bash

# =============================================================================
# *************** 服务器 Hadoop/Spark 启动脚本
# =============================================================================

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 设置环境变量
setup_environment() {
    log_step "设置环境变量..."
    
    # 自动检测安装路径
    if [ -z "$JAVA_HOME" ]; then
        if [ -d "/usr/lib/jvm/java-8-openjdk-amd64" ]; then
            export JAVA_HOME="/usr/lib/jvm/java-8-openjdk-amd64"
        elif [ -d "/usr/lib/jvm/java-1.8.0-openjdk" ]; then
            export JAVA_HOME="/usr/lib/jvm/java-1.8.0-openjdk"
        else
            JAVA_HOME=$(find /usr/lib/jvm -name "java-8-openjdk*" | head -n 1)
            export JAVA_HOME
        fi
    fi
    
    if [ -z "$HADOOP_HOME" ]; then
        if [ -d "/opt/hadoop" ]; then
            export HADOOP_HOME="/opt/hadoop"
        elif [ -d "/usr/local/hadoop" ]; then
            export HADOOP_HOME="/usr/local/hadoop"
        fi
    fi
    
    if [ -z "$SPARK_HOME" ]; then
        if [ -d "/opt/spark" ]; then
            export SPARK_HOME="/opt/spark"
        elif [ -d "/usr/local/spark" ]; then
            export SPARK_HOME="/usr/local/spark"
        fi
    fi
    
    if [ -z "$HIVE_HOME" ]; then
        if [ -d "/opt/hive" ]; then
            export HIVE_HOME="/opt/hive"
        elif [ -d "/usr/local/hive" ]; then
            export HIVE_HOME="/usr/local/hive"
        fi
    fi
    
    # 设置PATH
    export PATH=$JAVA_HOME/bin:$HADOOP_HOME/bin:$HADOOP_HOME/sbin:$SPARK_HOME/bin:$SPARK_HOME/sbin:$HIVE_HOME/bin:$PATH
    
    log_info "环境变量设置完成:"
    log_info "  JAVA_HOME: $JAVA_HOME"
    log_info "  HADOOP_HOME: $HADOOP_HOME"
    log_info "  SPARK_HOME: $SPARK_HOME"
    log_info "  HIVE_HOME: $HIVE_HOME"
}

# 检查环境
check_environment() {
    log_step "检查环境..."
    
    # 检查Java
    if [ -z "$JAVA_HOME" ] || [ ! -d "$JAVA_HOME" ]; then
        log_error "JAVA_HOME未设置或目录不存在: $JAVA_HOME"
        return 1
    fi
    
    if ! command -v java &> /dev/null; then
        log_error "Java命令不可用"
        return 1
    fi
    
    log_info "✅ Java环境正常: $(java -version 2>&1 | head -n 1)"
    
    # 检查Hadoop
    if [ -n "$HADOOP_HOME" ] && [ -d "$HADOOP_HOME" ]; then
        log_info "✅ Hadoop环境正常: $HADOOP_HOME"
    else
        log_warn "❌ Hadoop环境不可用"
    fi
    
    # 检查Spark
    if [ -n "$SPARK_HOME" ] && [ -d "$SPARK_HOME" ]; then
        log_info "✅ Spark环境正常: $SPARK_HOME"
    else
        log_warn "❌ Spark环境不可用"
    fi
}

# 停止现有服务
stop_services() {
    log_step "停止现有服务..."
    
    # 停止Spark
    if [ -n "$SPARK_HOME" ] && [ -f "$SPARK_HOME/sbin/stop-all.sh" ]; then
        log_info "停止Spark服务..."
        $SPARK_HOME/sbin/stop-all.sh || true
    fi
    
    # 停止Hadoop
    if [ -n "$HADOOP_HOME" ] && [ -f "$HADOOP_HOME/sbin/stop-yarn.sh" ]; then
        log_info "停止YARN..."
        $HADOOP_HOME/sbin/stop-yarn.sh || true
    fi
    
    if [ -n "$HADOOP_HOME" ] && [ -f "$HADOOP_HOME/sbin/stop-dfs.sh" ]; then
        log_info "停止HDFS..."
        $HADOOP_HOME/sbin/stop-dfs.sh || true
    fi
    
    # 停止Hive Metastore
    log_info "停止Hive Metastore..."
    pkill -f "hive.*metastore" || true
    
    # 等待进程停止
    sleep 5
    
    log_info "✅ 现有服务已停止"
}

# 启动Hadoop服务
start_hadoop() {
    if [ -z "$HADOOP_HOME" ] || [ ! -d "$HADOOP_HOME" ]; then
        log_warn "Hadoop未安装，跳过启动"
        return 0
    fi
    
    log_step "启动Hadoop服务..."
    
    # 检查NameNode是否已格式化
    if [ ! -d "/tmp/hadoop/namenode" ]; then
        log_info "首次启动，格式化NameNode..."
        $HADOOP_HOME/bin/hdfs namenode -format -force
    fi
    
    # 启动HDFS
    log_info "启动HDFS..."
    $HADOOP_HOME/sbin/start-dfs.sh
    
    # 等待HDFS启动
    sleep 10
    
    # 启动YARN
    log_info "启动YARN..."
    $HADOOP_HOME/sbin/start-yarn.sh
    
    # 等待YARN启动
    sleep 10
    
    # 创建必要的HDFS目录
    log_info "创建HDFS目录..."
    $HADOOP_HOME/bin/hdfs dfs -mkdir -p /spark-logs || true
    $HADOOP_HOME/bin/hdfs dfs -mkdir -p /spark-warehouse || true
    $HADOOP_HOME/bin/hdfs dfs -mkdir -p /hive/warehouse || true
    $HADOOP_HOME/bin/hdfs dfs -chmod 777 /spark-logs || true
    $HADOOP_HOME/bin/hdfs dfs -chmod 777 /spark-warehouse || true
    $HADOOP_HOME/bin/hdfs dfs -chmod 777 /hive/warehouse || true
    
    log_info "✅ Hadoop服务启动完成"
}

# 启动Spark服务
start_spark() {
    if [ -z "$SPARK_HOME" ] || [ ! -d "$SPARK_HOME" ]; then
        log_warn "Spark未安装，跳过启动"
        return 0
    fi
    
    log_step "启动Spark服务..."
    
    # 启动Spark Master
    log_info "启动Spark Master..."
    $SPARK_HOME/sbin/start-master.sh
    
    # 等待Master启动
    sleep 5
    
    # 启动Spark Worker
    log_info "启动Spark Worker..."
    $SPARK_HOME/sbin/start-worker.sh spark://$(hostname):7077
    
    # 等待Worker启动
    sleep 5
    
    log_info "✅ Spark服务启动完成"
}

# 启动Hive Metastore
start_hive_metastore() {
    if [ -z "$HIVE_HOME" ] || [ ! -d "$HIVE_HOME" ]; then
        log_warn "Hive未安装，跳过启动"
        return 0
    fi
    
    log_step "启动Hive Metastore..."
    
    # 初始化Schema（如果需要）
    if [ ! -f "/tmp/hive_schema_initialized" ]; then
        log_info "初始化Hive Schema..."
        $HIVE_HOME/bin/schematool -dbType derby -initSchema || true
        touch /tmp/hive_schema_initialized
    fi
    
    # 启动Metastore服务
    log_info "启动Hive Metastore服务..."
    nohup $HIVE_HOME/bin/hive --service metastore > /var/log/hive-metastore.log 2>&1 &
    
    # 等待服务启动
    sleep 10
    
    log_info "✅ Hive Metastore启动完成"
}

# 验证服务状态
verify_services() {
    log_step "验证服务状态..."
    
    echo ""
    echo "=== Java进程状态 ==="
    if command -v jps &> /dev/null; then
        jps
    else
        ps aux | grep java | grep -v grep
    fi
    
    echo ""
    echo "=== 端口监听状态 ==="
    PORTS=(7077 8080 9000 9083 8088 9870)
    PORT_NAMES=("Spark Master" "Spark Web UI" "HDFS NameNode" "Hive Metastore" "YARN ResourceManager" "HDFS Web UI")
    
    for i in "${!PORTS[@]}"; do
        PORT=${PORTS[$i]}
        NAME=${PORT_NAMES[$i]}
        
        if netstat -tlnp 2>/dev/null | grep -q ":$PORT "; then
            log_info "✅ $NAME (端口 $PORT) - 正在监听"
        else
            log_warn "❌ $NAME (端口 $PORT) - 未监听"
        fi
    done
    
    echo ""
    echo "=== Web界面访问地址 ==="
    HOST_IP=$(hostname -I | awk '{print $1}')
    echo "Spark Master Web UI: http://$HOST_IP:8080"
    echo "HDFS Web UI: http://$HOST_IP:9870"
    echo "YARN ResourceManager: http://$HOST_IP:8088"
    echo ""
}

# 生成状态检查命令
generate_status_commands() {
    log_step "生成状态检查命令..."
    
    cat > /root/check-status.sh << 'EOF'
#!/bin/bash
echo "=== 服务状态检查 ==="
echo "时间: $(date)"
echo ""

echo "=== Java进程 ==="
jps

echo ""
echo "=== 端口监听 ==="
netstat -tlnp | grep -E ':7077|:8080|:9000|:9083|:8088|:9870'

echo ""
echo "=== HDFS状态 ==="
if command -v hdfs &> /dev/null; then
    hdfs dfsadmin -report
fi

echo ""
echo "=== Web界面 ==="
HOST_IP=$(hostname -I | awk '{print $1}')
echo "Spark Master: http://$HOST_IP:8080"
echo "HDFS: http://$HOST_IP:9870"
echo "YARN: http://$HOST_IP:8088"
EOF
    
    chmod +x /root/check-status.sh
    log_info "✅ 状态检查脚本已创建: /root/check-status.sh"
}

# 主函数
main() {
    echo "============================================================================="
    echo "*************** 服务器 Hadoop/Spark 启动脚本"
    echo "============================================================================="
    echo "启动时间: $(date)"
    echo ""
    
    setup_environment
    check_environment
    stop_services
    start_hadoop
    start_spark
    start_hive_metastore
    verify_services
    generate_status_commands
    
    echo ""
    echo "============================================================================="
    echo "启动完成！"
    echo "============================================================================="
    echo ""
    echo "服务状态检查命令: /root/check-status.sh"
    echo ""
    echo "如果需要重启服务，请运行:"
    echo "  ./start-hadoop-spark-157.sh"
    echo ""
    echo "如果需要停止服务，请运行:"
    echo "  ./stop-hadoop-spark-157.sh"
    echo ""
}

# 执行主函数
main "$@"
