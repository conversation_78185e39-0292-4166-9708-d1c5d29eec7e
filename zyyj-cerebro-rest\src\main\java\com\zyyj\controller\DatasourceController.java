package com.zyyj.controller;


import com.zyyj.cere.pojo.dto.*;
import com.zyyj.cere.pojo.entity.DatasourceEntity;
import com.zyyj.cere.pojo.entity.DatasourceTypeEntity;
import com.zyyj.cere.service.DatasourceService;
import com.zyyj.domain.pagination.Paging;
import com.zyyj.utils.PublicUtils;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/v1/datasource")
@CrossOrigin(origins = "*", maxAge = 3600)
public class DatasourceController {

    @Autowired
    private DatasourceService datasourceService;

    /**
     * 数据源列表--分页
     *
     * @return
     */
    @ApiOperation("数据源列表--分页")
    @GetMapping("/list_page")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "当前页面", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "size", value = "每页数量", required = true, paramType = "query", dataType = "int")
    })
    public PagedDatasourceDTO getDatasourcePage(Paging paging) {
        return datasourceService.getListPage(paging);
    }

    /**
     * 数据源列表
     *
     * @return
     */
    @ApiOperation("数据源列表")
    @GetMapping("/list")
    public Map<String, List<DatasourceEntity>> getDatasource() {
        return new HashMap<String, List<DatasourceEntity>>() {{
            put("list", datasourceService.getList());
        }};
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @ApiOperation("数据源详情")
    @GetMapping("/info/{id}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据源id", required = true, paramType = "path", dataType = "long")
    })
    public DatasourceDTO info(@PathVariable Long id) {
        //参数非空校验
        PublicUtils.checkNotNullArr(id);
        return datasourceService.get(id);
    }

    /**
     * 添加
     *
     * @param p
     * @return
     */
    @ApiOperation("添加数据源")
    @PostMapping("/add")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "DatasourceEntity", value = "数据源发布对象", required = true, paramType = "body", dataType = "DatasourceEntity")
    })
    public void set(@RequestBody @Valid DatasourceEntity p) {
        //参数非空校验
        PublicUtils.checkNotNullArr(p, p.getTypeId());
        PublicUtils.checkNotEmptyArr(p.getName(), p.getDatabase(), p.getHost(), p.getUsername());

        datasourceService.set(p);
    }

    /**
     * 编辑
     *
     * @param p
     * @return
     */
    @ApiOperation("编辑数据源")
    @PostMapping("/edit")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "DatasourceEntity", value = "数据源发布对象", required = true, paramType = "body", dataType = "DatasourceEntity")
    })
    public void edit(@RequestBody @Valid DatasourceEntity p) {
        //参数非空校验
        PublicUtils.checkNotNullArr(p, p.getId(), p.getTypeId());
        PublicUtils.checkNotEmptyArr(p.getName(), p.getDatabase(), p.getHost(), p.getUsername());

        datasourceService.update(p);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @ApiOperation("删除数据源")
    @PostMapping("/del/{id}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据源id", required = true, paramType = "path", dataType = "long")
    })
    public void del(@PathVariable Long id) {
        //参数校验
        PublicUtils.checkNotNullArr(id);

        datasourceService.delete(id);
    }

    /**
     * @Description: 测试连接
     * @Param: [p]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/9
     */
    @ApiOperation("测试连接")
    @PostMapping("/test_connect")
    public Map<String, Integer> testConnect(@RequestBody DatasourceEntity p) {
        PublicUtils.checkNotEmptyArr(p.getHost());
        PublicUtils.checkNotEmptyArr(p.getUsername());
        PublicUtils.checkIntNotNull(p.getTypeId().intValue());
        System.out.println(p);
        Map<String, Integer> data = new HashMap<>();
        data.put("status", datasourceService.testConnect(p));
        return data;
    }

    /**
     * @Description: 数据源表列表
     * @Param: [dataSourceId]
     * @return: java.util.Map<java.lang.String, java.util.List < com.zyyj.cere.pojo.dto.TableNameDTO>>
     * @Author: bravelee
     * @Date: 2020/12/7
     */
    @GetMapping("/table_list/{dataSourceId}")
    public Map<String, List<TableNameDTO>> getDataSourceTables(@PathVariable Integer dataSourceId) {
        PublicUtils.checkIntNotNull(dataSourceId);

        return datasourceService.getDataSourceTables(dataSourceId);
    }

    /**
     * @Description: 数据源表表结构
     * @Param: [dataSourceId, tableName]
     * @return: java.util.Map<java.lang.String, java.util.List < com.zyyj.cere.pojo.dto.TableFieldDTO>>
     * @Author: bravelee
     * @Date: 2020/12/7
     */
    @GetMapping("/table_field")
    public Map<String, List<TableFieldDTO>> getDataSourceTableFields(Integer dataSourceId, String tableName) {
        PublicUtils.checkNotEmptyArr(tableName);

        return datasourceService.getDataSourceTableFields(dataSourceId, tableName);
    }

    /**
     * @Description: 数据源列表
     * @Param: []
     * @return: java.util.List<com.zyyj.cere.pojo.dto.ApiNameDTO>
     * @Author: bravelee
     * @Date: 2020/12/7
     */
    @GetMapping("/types")
    public List<DatasourceTypeEntity> getDatasourceType() {
        return datasourceService.getDatasourceType();
    }

}
