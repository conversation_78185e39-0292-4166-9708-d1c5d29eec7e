package com.zyyj.cere.repository;

import com.zyyj.cere.pojo.entity.TableEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface TableRepository extends JpaRepository<TableEntity, Long>, JpaSpecificationExecutor<TableEntity> {

    boolean existsByTableName(String tableName);

    List<TableEntity> findByBusinessId(Long businessId);

    // 通过table_name 查找table。 table_name 唯一的
    TableEntity findOneByTableName(String tableName);

    @Modifying
    @Query(value = "UPDATE TableEntity t SET t.businessId = ?1 WHERE t.id = ?2")
    void updateBusinessIdById(Long businessId, Long Id);

    @Modifying
    @Query(value = "UPDATE TableEntity t SET t.status = ?1 WHERE t.id = ?2")
    void updateStatusById(Byte status, Long Id);

    @Modifying
    @Query(value = "UPDATE TableEntity t SET t.name = ?1 WHERE t.id = ?2")
    void updateNameById(String name, Long Id);

    @Modifying
    @Query(value = "UPDATE TableEntity t SET t.name = ?1,t.tableName = ?2 WHERE t.modelingId = ?3")
    void updateNameAndTableNameByModelId(String name, String tableName, Integer modelingId);

    @Modifying
    @Query(value = "UPDATE TableEntity t SET t.tunnelId = 0 WHERE t.tunnelId = ?1")
    void deleteTunnelIdBy(Integer tunnelId);

    List<TableEntity> findByNameContaining(String name);

    List<TableEntity> findByStatusInAndNameContaining(Byte[] status, String name);

    //通过建模id查询表id
    TableEntity findByModelingId(Integer modelingId);

    //通过通道id查询
    Optional<TableEntity> findByTunnelId(Long tunnelId);
}
