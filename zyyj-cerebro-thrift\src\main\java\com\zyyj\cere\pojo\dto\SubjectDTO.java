package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2020/9/18 15:49
 */
@ThriftStruct
@Setter
@NoArgsConstructor
public class SubjectDTO {

    private Integer id;
    private String name;

    @ThriftConstructor
    public SubjectDTO(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public String getName() {
        return name;
    }
}
