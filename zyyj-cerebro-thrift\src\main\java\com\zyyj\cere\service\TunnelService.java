package com.zyyj.cere.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.zyyj.cere.pojo.body.TunnelAddFormModel;
import com.zyyj.domain.exception.ApplicationException;


/*管道服务*/
@ThriftService
public interface TunnelService {

    // 初始化管道
    @ThriftMethod
    Long initTunnel(TunnelAddFormModel model) throws ApplicationException;

    @ThriftMethod
    String getTunnelInfo(Long id) throws ApplicationException;

    @ThriftMethod
    String deleteTunnel(Long id) throws ApplicationException;

    @ThriftMethod
    String stopTunnel(Long id) throws ApplicationException;

    @ThriftMethod
    String resumeTunnel(Long id) throws ApplicationException;

}
