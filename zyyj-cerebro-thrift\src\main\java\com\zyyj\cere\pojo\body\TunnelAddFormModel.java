package com.zyyj.cere.pojo.body;

/*
*   {
            "datasource_id":1,  //数据源
            "source_table":"",   //来源表
            "table_name":"",    //表名
            "table_comment":"",  //中文名
            "mapping_type":1,    // 1 同行 2 同名
            "mapping_conf":"",
            "business_id: 0 // 业务域id
    }
    *
* */


import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.google.gson.Gson;
import lombok.Setter;
import lombok.NoArgsConstructor;

import java.util.List;

@Setter
@NoArgsConstructor
@ThriftStruct
public class TunnelAddFormModel {

    private Long datasourceId;

    private Long businessId;
    // 原始表名
    private String sourceTable;
    // 不能中文
    private String tableName;

    private String tableComment;

    private Integer mappingType;

    private List<TunnelAddFromMappingConfModel> mappingConf;

    @ThriftConstructor
    public TunnelAddFormModel(Long datasourceId, Long businessId, Integer mappingType, String sourceTable, String tableName, String tableComment, List<TunnelAddFromMappingConfModel> mappingConf) {
        this.datasourceId = datasourceId;
        this.businessId = businessId;
        this.mappingType = mappingType;
        this.sourceTable = sourceTable;
        this.tableName = tableName;
        this.tableComment = tableComment;
        this.mappingConf = mappingConf;
    }

    @ThriftField(1)
    public Long getDatasourceId() {
        return datasourceId;
    }

    @ThriftField(2)
    public Long getBusinessId() {
        return businessId;
    }

    @ThriftField(3)
    public Integer getMappingType() {
        return mappingType;
    }

    @ThriftField(4)
    public String getSourceTable() {
        return sourceTable;
    }

    @ThriftField(5)
    public String getTableName() {
        return tableName;
    }

    @ThriftField(6)
    public String getTableComment() {
        return tableComment;
    }

    @ThriftField(7)
    public List<TunnelAddFromMappingConfModel> getMappingConf() {
        return mappingConf;
    }

//    @ThriftField(8)
//    public String getName() {
//        return name;
//    }

    public String getMappingConfig(){
        Gson gson = new Gson();
        return gson.toJson(this.getMappingConf());
    }
}
