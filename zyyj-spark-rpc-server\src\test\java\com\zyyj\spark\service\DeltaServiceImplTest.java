package com.zyyj.spark.service;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.zyyj.spark.SparkRPCServerApplication;
import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = SparkRPCServerApplication.class)
public class DeltaServiceImplTest extends TestCase {
    @Autowired
    DeltaServiceImpl deltaService;

    @Test
    public void testCreateDatabase() {
        deltaService.createDatabase("testbase5");
    }

    @Test
    public void testCreateTableByJson(){
        deltaService.createTableByJson("test1","[{'id':1,'name':'新增1','age':1},{'id':2,'name':'新增2','age':2},{'id':3,'name':'新增2','age':2},{'id':4,'name':'新增2','age':2},{'id':5,'name':'新增2','age':2},{'id':6,'name':'新增2','age':2},{'id':7,'name':'新增2','age':2}]");
//        deltaService.createTableByJson("testbase.test3","[{'id':1,'name':'新增1','age':1},{'id':2,'name':'新增2','age':2}]");
    }

    @Test
    public void testTableList(){
//        List<String> l =  deltaService.getTableList("userbase.usersmap");
        List<String> l =  deltaService.getTableList("test40");
        l.forEach(System.out::println);
    }

    @Test
    public void testReTable(){
        deltaService.renameTable("test7","test777");
    }

    @Test
    public void testStruct(){
        System.out.println(deltaService.getTableStruct("test8"));
    }

    @Test
    public void testSql(){
        String sql;

        sql = "select count(*) as num from test1";
        List<String> l1 = deltaService.getTableListBySql(sql);
//        List<String> l =  deltaService.getTableList("test34")

//        sql = "INSERT INTO test34(\"id\",\"name\",\"age\") VALUES(\"7\",\"测试4\",\"14\"),(\"8\",\"测试5\",\"15\")";
        sql = "select * from test1 limit 1, 5";
        List<String> l = deltaService.getTableListBySql(sql);
//        List<String> l =  deltaService.getTableList("test34");

        Type type = new TypeToken<Map<String, Long>>(){}.getType();
        Map<String, Long> myMap = new Gson().fromJson(l1.get(0),type);
        System.out.println(myMap.get("num"));
        l.forEach(System.out::println);
    }

    @Test
    public void testPrivate(){
        System.out.println(deltaService.preview("test1","id", 2, 2));
    }
}