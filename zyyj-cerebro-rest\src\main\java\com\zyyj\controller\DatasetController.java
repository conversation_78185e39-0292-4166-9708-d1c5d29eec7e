package com.zyyj.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zyyj.cere.pojo.dto.DatasetAttributeDto;
import com.zyyj.cere.pojo.dto.DatasetDto;
import com.zyyj.cere.pojo.entity.*;
import com.zyyj.cere.service.DatasetService;
import com.zyyj.domain.pagination.OrderBy;
import com.zyyj.domain.pagination.Paging;
import com.zyyj.utils.PublicUtils;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/v1/dataset")
public class DatasetController {

    @Resource
    private DatasetService datasetService;

    /**
     * 添加或修改数据集目录
     *
     * @Param datasetEntity
     * @return
     */
    @ApiOperation("添加数据集目录")
    @PostMapping(value = "/saveDataset")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "数据集名称", required = true, dataType = "string"),
            @ApiImplicitParam(name = "parentId", value = "父节点Id", required = true, dataType = "int"),
            @ApiImplicitParam(name = "level", value = "层级", required = true, dataType = "int"),
            @ApiImplicitParam(name = "type", value = "类型", required = true, dataType = "int")
    })
    public void saveDataset(@RequestBody DatasetEntity datasetEntity) {
        datasetService.saveDataset(datasetEntity);
    }

    /**
     * 数据集列表
     *
     * @return
     */
    @ApiOperation("数据集列表")
    @PostMapping(value = "/list")
    public List<DatasetEntity> getDatasetList(@RequestBody JSONObject obj) {
        PublicUtils.checkNotNullArr(obj.getInteger("id"));
        return datasetService.getDatasetList(obj.getInteger("id"),obj.getString("name"));
    }

    /**
     * 删除数据集
     *
     * @param
     * @return
     */
    @ApiOperation("删除数据集")
    @PostMapping(value = "/delete")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据源id", required = true,  dataType = "int")
    })
    public void datasetDelete(@RequestBody JSONObject obj) {
        PublicUtils.checkNotNullArr(obj.getInteger("id"));
        datasetService.datasetDelete(obj.getInteger("id"));
    }

    /**
     * 数据标准列表
     *
     * @param
     * @return
     */
    @ApiOperation("数据标准列表")
    @PostMapping(value = "/standard/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "编号/英文名/中文名",  dataType = "string"),
            @ApiImplicitParam(name = "datasetId", value = "数据集Id", dataType = "string"),
            @ApiImplicitParam(name = "paging", value = "分页信息",  dataType = "string")
    })
    public Map standardList(@RequestBody JSONObject obj) {
        PublicUtils.checkNotNullArr(obj.getInteger("datasetId"));
        PublicUtils.checkNotNullArr(obj.getInteger("page"));
        PublicUtils.checkNotNullArr(obj.getInteger("size"));
        PublicUtils.checkNotNullArr(obj.getInteger("type"));
        Map map =new HashMap();
        if(obj.getInteger("type")==2){
            List<DatasetAttributeEntity> titleList =datasetService.getStandardTitle(obj.getInteger("datasetId"));
            if(obj.getInteger("type")==3||titleList.size()>0){
                String content = datasetService.getStandardList(obj.getInteger("datasetId"),
                        obj.getString("name"), new Paging(obj.getInteger("page"),obj.getInteger("size"),new OrderBy(Sort.Direction.DESC,"id")));
                map.put("title",titleList);
                map.put("content", JSON.parseObject(content,Map.class));
            }
            return map;
        }else{
            String content = datasetService.getStandardList(obj.getInteger("datasetId"),
                    obj.getString("name"), new Paging(obj.getInteger("page"),obj.getInteger("size"),new OrderBy(Sort.Direction.DESC,"id")));
            return JSON.parseObject(content,Map.class);
        }
    }

    /**
     * 添加或修改数据标准
     *
     * @param
     * @return
     */
    @ApiOperation("修改数据标准列表")
    @PostMapping(value = "/standard/saveOrUpdate")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "datasetId", value = "数据集Id", required = true,  dataType = "int"),
            @ApiImplicitParam(name = "code", value = "code", required = true,  dataType = "string"),
            @ApiImplicitParam(name = "cname", value = "cname", required = true,  dataType = "string"),
            @ApiImplicitParam(name = "ename", value = "ename", required = true,  dataType = "string"),
            @ApiImplicitParam(name = "content", value = "content",  dataType = "string")
    })
    public void standardSave(@RequestBody DatasetStandardEntity datasetStandardEntity) {
        datasetService.saveDatasetStandard(datasetStandardEntity);
    }

    /**
     * 删除数据标准
     *
     * @param
     * @return
     */
    @ApiOperation("修改数据标准列表")
    @PostMapping(value = "/standard/delete")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据标准Id", required = true,  dataType = "int"),
    })
    public void standardDelete(@RequestBody JSONObject obj) {
        PublicUtils.checkNotNullArr(obj.getInteger("id"));
        datasetService.standardDelete(obj.getInteger("id"));
    }

    /**
     * 添加或修改数据属性
     *
     * @param
     * @return
     */
    @ApiOperation("添加或修改属性")
    @PostMapping(value = "/attribute/saveOrUpdate")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "属性代码", required = true,  dataType = "string"),
            @ApiImplicitParam(name = "name", value = "属性名称", required = true,  dataType = "string"),
            @ApiImplicitParam(name = "field_length", value = "字段长度", dataType = "int"),
            @ApiImplicitParam(name = "field_precision", value = "字段精度", dataType = "int"),
            @ApiImplicitParam(name = "is_null", value = "是否必填", dataType = "int"),
            @ApiImplicitParam(name = "describe", value = "描述", dataType = "String"),

    })
    public void saveAttribute(@RequestBody DatasetAttributeDto datasetAttributeDto) {
        PublicUtils.checkNotNullArr(datasetAttributeDto.getDatasetId());
        datasetService.saveDatasetAttribute(datasetAttributeDto);
    }

    /**
     * 删除属性
     *
     * @param
     * @return
     */
    @ApiOperation("删除属性")
    @PostMapping(value = "/attribute/delete")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据标准属性Id", dataType = "int")
    })
    public void attributeDelete(@RequestBody JSONObject obj) {
        PublicUtils.checkNotNullArr(obj.getInteger("id"));
        PublicUtils.checkNotNullArr(obj.getInteger("datasetId"));
        PublicUtils.checkNotNullArr(obj.getInteger("propertyTypeId"));
        datasetService.attributeDelete(obj.getInteger("id"),obj.getInteger("datasetId"),obj.getInteger("propertyTypeId"));
    }

    /**
     * 内置属性列表
     *
     * @param
     * @return
     */
    @ApiOperation("内置属性列表")
    @PostMapping(value = "/attribute/innerList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据集Id", dataType = "int")
    })
    public Map<String, List<DatasetAttributeEntity>> getInnerAttributeList(@RequestBody JSONObject obj) {
        PublicUtils.checkNotNullArr(obj.getInteger("id"));
        return datasetService.getInnerAttributeList(obj.getInteger("id"));
    }

    /**
     * 属性列表
     *
     * @param
     * @return
     */
    @ApiOperation("属性列表")
    @PostMapping(value = "/attribute/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据集Id", dataType = "int")
    })
    public Map getAttributeList(@RequestBody JSONObject obj) {
        PublicUtils.checkNotNullArr(obj.getInteger("id"));
        PublicUtils.checkNotNullArr(obj.getInteger("page"));
        PublicUtils.checkNotNullArr(obj.getInteger("size"));
        return JSON.parseObject(datasetService.getAllAttributeList(obj.getInteger("id"),
                obj.getString("name"),obj.getString("propertyType"),
                new Paging(obj.getInteger("page"),obj.getInteger("size"))),Map.class);
    }

    /**
     * 移动数据集列表
     *
     * @param
     * @return
     */
    @ApiOperation("移动数据集列表")
    @PostMapping(value = "/moveList")
    @ApiImplicitParams({})
    public List<DatasetDto> getAllAttributeMoveList() {
        String result = datasetService.getAllAttributeMoveList();
        return JSON.parseArray(result,DatasetDto.class);
    }

    /**
     * 移动数据集列表
     *
     * @param
     * @return
     */
    @ApiOperation("复制数据集列表")
    @PostMapping(value = "/copyList")
    @ApiImplicitParams({})
    public  List<DatasetDto>  getCopyList() {
        return JSON.parseArray(datasetService.getAllAttributeCopyList(),DatasetDto.class);
    }

    /**
     * 复制数据集属性保存
     *
     * @param
     * @return
     */
    @ApiOperation("复制数据集属性保存")
    @PostMapping(value = "/saveCopyAttribute")
    @ApiImplicitParams({})
    public void saveCopyAttribute(@RequestBody JSONObject obj) {
        PublicUtils.checkNotNullArr(obj.getInteger("copyId"));
        PublicUtils.checkNotNullArr(obj.getInteger("id"));
        datasetService.saveCopyAttribute(obj.getInteger("id"),obj.getInteger("copyId"));
    }

    /**
     * 内置属性保存
     *
     * @param
     * @return
     */
    @ApiOperation("复制数据集属性保存")
    @PostMapping(value = "/saveInnerAttribute")
    @ApiImplicitParams({})
    public void saveInnerAttribute(@RequestBody DatasetRelationEntity datasetRelationEntity) {
        PublicUtils.checkNotNullArr(datasetRelationEntity.getDatasetId());
        PublicUtils.checkNotNullArr(datasetRelationEntity.getInnerAttribute());
        datasetService.saveInnerAttribute(datasetRelationEntity);
    }
};
