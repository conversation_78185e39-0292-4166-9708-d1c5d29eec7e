package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据标准属性
 */

@ThriftStruct
@Data
@NoArgsConstructor
public class DatasetAttributeDto {

    private Integer id;
    private String code;
    private String name;
    private Integer propertyTypeId = 6;
    private Integer fieldLength;
    private Integer fieldPrecision;
    private Integer status = 0;
    private Byte isNull = 0;
    private String comment;
    private String propertyTypeName = "自定义属性";
    private String type;
    private String options;
    private Integer datasetId;

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public String getCode() {
        return code;
    }

    @ThriftField(3)
    public String getName() {
        return name;
    }

    @ThriftField(4)
    public Integer getPropertyTypeId() {
        return propertyTypeId;
    }

    @ThriftField(5)
    public Integer getFieldLength() {
        return fieldLength;
    }

    @ThriftField(6)
    public Integer getFieldPrecision() {
        return fieldPrecision;
    }

    @ThriftField(7)
    public Integer getStatus() {
        return status;
    }

    @ThriftField(8)
    public Byte getIsNull() {
        return isNull;
    }

    @ThriftField(9)
    public String getComment() {
        return comment;
    }

    @ThriftField(10)
    public String getPropertyTypeName() {
        return propertyTypeName;
    }

    @ThriftField(11)
    public String getType() {
        return type;
    }

    @ThriftField(12)
    public String getOptions() {
        return options;
    }

    @ThriftField(13)
    public Integer getDatasetId() {
        return datasetId;
    }

    @ThriftConstructor
    public DatasetAttributeDto(Integer id, String code, String name, Integer propertyTypeId, Integer fieldLength, Integer fieldPrecision, Integer status, Byte isNull, String comment, String propertyTypeName, String type, String options, Integer datasetId) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.propertyTypeId = propertyTypeId;
        this.fieldLength = fieldLength;
        this.fieldPrecision = fieldPrecision;
        this.status = status;
        this.isNull = isNull;
        this.comment = comment;
        this.propertyTypeName = propertyTypeName;
        this.type = type;
        this.options = options;
        this.datasetId = datasetId;
    }
}
