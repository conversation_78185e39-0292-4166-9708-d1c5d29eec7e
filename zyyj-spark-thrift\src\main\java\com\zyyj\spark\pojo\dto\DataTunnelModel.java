package com.zyyj.spark.pojo.dto;


import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.google.gson.Gson;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@ThriftStruct
@NoArgsConstructor
@Setter
public class DataTunnelModel implements Serializable {

    private static final long serialVersionUID = 1L;

    // delta 中对应表名
    private String tableName;
    // kafka 对应的topic名
    private String topicName;
    // kafka server
    private List<String> bootstrapServer;
    // 映射关系
    private List<DTMappingModel> mappingConf;

    @ThriftConstructor
    public DataTunnelModel(String tableName, String topicName, List<String> bootstrapServer, List<DTMappingModel> mappingConf) {
        this.tableName = tableName;
        this.topicName = topicName;
        this.bootstrapServer = bootstrapServer;
        this.mappingConf = mappingConf;
    }

    @ThriftField(1)
    public String getTableName() {
        return tableName;
    }

    @ThriftField(2)
    public String getTopicName() {
        return topicName;
    }

    @ThriftField(3)
    public List<String> getBootstrapServer() {
        return bootstrapServer;
    }

    @ThriftField(4)
    public List<DTMappingModel> getMappingConf() {
        return mappingConf;
    }

    public String getBootstrapServers() {
        return String.join(",", bootstrapServer);
    }


    // 通过字段名来获取映射配置
    public DTMappingModel getMappingConfByField(String field){
        for (DTMappingModel m:mappingConf) {
            if (m.getField().equals(field)){
                return m;
            }
        }
        return null;
    }


    public String toJSON(){
        Gson gson = new Gson();
        return gson.toJson(this);
    }

}
