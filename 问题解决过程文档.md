# Spark RPC服务问题解决过程文档

## 📋 问题概述

**时间**: 2025年8月2日  
**问题**: Cerebro RPC调用Spark RPC创建表接口返回500错误  
**环境**: 
- 本地Cerebro RPC服务 (Windows)
- 158服务器 Spark RPC服务 (Linux)
- 157服务器 Spark集群 + HDFS + Eureka (Linux)

## 🔍 问题排查过程

### 阶段1: 初步问题定位
**现象**: 创建表接口返回500内部服务器错误
**排查方向**: 
1. 检查服务连通性
2. 查看错误日志
3. 分析调用链路

**发现**: 
- REST API服务正常
- Cerebro RPC服务正常
- 问题出现在调用Spark RPC时

### 阶段2: 网络连接问题
**现象**: Thrift客户端连接超时
**错误信息**: `connection timed out: /***************:8081`

**问题分析**: 
- 客户端尝试连接8081端口（HTTP端口）
- 实际Thrift服务运行在9009端口

**解决方案**: 修复Eureka服务注册配置
```bash
# 修改前
-Deureka.instance.non-secure-port=8081

# 修改后  
-Deureka.instance.non-secure-port=9009
```

### 阶段3: 配置文件问题
**现象**: Spark配置参数全部为null
**错误信息**: `null value for spark.sql.warehouse.dir`

**问题分析**:
- Spring Boot `@ConfigurationProperties` 没有正确读取配置
- 启动参数与配置属性名不匹配

**解决方案**: 使用直接JVM参数而不是依赖配置文件
```bash
# 关键参数
-Dspark.master=local[*]
-Dspark.appName=ZYYJ-SPARK-RPC-SERVER  
-Dspark.warehouseDir=hdfs://***************:9000/spark-warehouse
-Dspark.metastoreUris=thrift://***************:9083
```

### 阶段4: Scala版本兼容性问题
**现象**: 序列化/反序列化异常
**错误信息**: 
```
ClassCastException: cannot assign instance of scala.collection.immutable.List$SerializationProxy 
to field org.apache.spark.sql.catalyst.expressions.objects.NewInstance.arguments
```

**问题分析**:
- 应用使用Scala 2.12.10
- Spark集群也使用Scala 2.12.10
- 但跨节点序列化时出现兼容性问题

**尝试的解决方案**:
1. 统一Scala版本 ❌
2. 配置Kryo序列化器 ❌
3. 使用本地Spark模式 ✅

### 阶段5: 最终解决方案
**解决方案**: 使用本地Spark模式
```bash
-Dspark.master=local[*]  # 关键配置
```

**效果**:
- 避免跨节点序列化问题
- 消除集群版本兼容性问题
- 保持功能完整性
- 创建表接口正常工作

## ✅ 成功配置总结

### 关键配置参数
```bash
-Dspark.master=local[*]
-Dspark.appName=ZYYJ-SPARK-RPC-SERVER
-Dspark.warehouseDir=hdfs://***************:9000/spark-warehouse
-Dspark.metastoreUris=thrift://***************:9083
-Dspark.driver.host=***************
-Dspark.driver.bindAddress=***************
-Dspark.serializer=org.apache.spark.serializer.KryoSerializer
-Deureka.instance.non-secure-port=9009
-Dzyyj.rpc.thrift.server.listen_port=9009
```

### 成功测试结果
```json
{
    "code": "SUCCESS",
    "data": {
        "id": 68,
        "businessId": 1,
        "name": "Local Mode Test",
        "tableName": "test_local_mode_20250802081824",
        "source": 1,
        "status": 0
    }
}
```

## 🎯 经验总结

### 问题根因
1. **端口配置错误**: Eureka注册了HTTP端口而不是Thrift端口
2. **配置读取失败**: Spring Boot配置属性映射问题
3. **集群兼容性**: 跨节点序列化版本冲突

### 解决思路
1. **逐层排查**: 从网络→配置→版本→架构
2. **日志分析**: 详细的错误日志是关键
3. **配置验证**: 确保配置真正生效
4. **架构调整**: 本地模式避免复杂性

### 最佳实践
1. **使用直接JVM参数**而不是复杂的配置文件映射
2. **本地模式优先**用于开发和测试环境
3. **详细的调试日志**便于问题定位
4. **版本统一管理**避免兼容性问题

## 📁 相关文件
- `start-spark-rpc-158-fixed-v2.0.sh` - 正常工作的启动脚本
- `zyyj-spark-rpc-server.jar` - 修复Scala版本的应用包
- 本文档 - 完整的问题解决过程

---
**文档创建时间**: 2025年8月2日  
**解决状态**: ✅ 已完全解决  
**测试状态**: ✅ 创建表接口正常工作
