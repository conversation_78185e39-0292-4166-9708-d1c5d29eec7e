package com.zyyj.cere.service;

import com.zyyj.cere.pojo.dto.DataMsgDTO;
import com.zyyj.cere.pojo.dto.PageListStringDTO;
import com.zyyj.cere.pojo.entity.TableEntity;
import com.zyyj.cere.pojo.entity.TableModelingEntity;
import com.zyyj.cere.repository.TableModelingRepository;
import com.zyyj.cere.repository.TableRepository;
import com.zyyj.domain.pagination.Paging;
import com.zyyj.rpc.thrift.server.ThriftServiceHandler;
import com.zyyj.spark.service.DeltaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/19 18:09
 */

@Service
@ThriftServiceHandler

public class TableModelingServiceImpl implements TableModelingService {

    @Autowired
    TableModelingRepository tableModelingRepository;
    @Autowired
    TableRepository tableRepository;
    @Autowired
    DeltaService deltaService;

    @Transactional
    @Override
    public DataMsgDTO<Map<String, Integer>> addTableModelingComponent(Integer businessId, TableModelingEntity tableModelingEntity) {
        boolean isInsert = tableModelingEntity.getId() == null;
        Map<String, Integer> map = new HashMap<>();
        if (isInsert) {
            //新增
            List<TableModelingEntity> list1 = tableModelingRepository.findByName(tableModelingEntity.getName());
            if (list1 != null && list1.size() > 0) {
                return new DataMsgDTO<>(map, "中文名重复");
            }
            List<TableModelingEntity> list2 = tableModelingRepository.findByTableName(tableModelingEntity.getTableName());
            if (list2 != null && list2.size() > 0) {
                return new DataMsgDTO<>(map, "英文名重复");
            }
        } else {
            List<TableModelingEntity> list1 = tableModelingRepository.queryByIdAndName(tableModelingEntity.getId(), tableModelingEntity.getName());
            if (list1 != null && list1.size() > 0) {
                return new DataMsgDTO<>(map, "中文名重复");
            }
            List<TableModelingEntity> list2 = tableModelingRepository.queryByIdAndTableName(tableModelingEntity.getId(), tableModelingEntity.getTableName());
            if (list2 != null && list2.size() > 0) {
                return new DataMsgDTO<>(map, "英文名重复");
            }
        }


        TableModelingEntity res = tableModelingRepository.saveAndFlush(tableModelingEntity);
        int tableId = 0;
        if (isInsert) {//新增
            TableEntity tableEntity = new TableEntity();
            tableEntity.setBusinessId((long) businessId);
            tableEntity.setName(res.getName());
            tableEntity.setTableName(res.getTableName());
            tableEntity.setModelingId(res.getId().intValue());
            if (res.getType() == 1) {
                tableEntity.setSource((byte) 4);//组建建模
            } else {
                tableEntity.setSource((byte) 5);//敏捷建模
            }
            tableEntity.setStatus((byte) 1);//建模成功状态为未发布
            tableEntity.setTypeId((byte) 2);//建模的为主题表
            tableRepository.saveAndFlush(tableEntity);
            tableId = tableEntity.getId().intValue();
        } else {//更新
            tableRepository.updateNameAndTableNameByModelId(res.getName(), res.getTableName(), res.getId().intValue());
            tableId = tableRepository.findByModelingId(res.getId().intValue()).getId().intValue();
        }
        map.put("table_id", tableId);
        map.put("modeling_id", res.getId().intValue());
        return new DataMsgDTO<>(map, "");
    }

    @Override
    public PageListStringDTO previewData(String dataSql, Paging paging) {

        List<String> structList = deltaService.getTableStructBySql(dataSql);
        List<String> dataList = deltaService.getTableListBySql(dataSql + " LIMIT 200");

        System.out.println("==structList==" + structList);
        System.out.println("==dataList==" + dataList);

        //分页
        int total = dataList.size();
        int startIndex = (paging.getPage() - 1) * paging.getSize();
        int endIndex = (paging.getPage() - 1) * paging.getSize() + paging.getSize();
        if (endIndex + 1 > total) {
            endIndex = total;
        }

        return new PageListStringDTO(total, dataList.subList(startIndex, endIndex), structList);
    }

    @Override
    public TableModelingEntity getTableModelingComponent(Integer id) {

        return tableModelingRepository.findById((long) id).get();
    }
}
