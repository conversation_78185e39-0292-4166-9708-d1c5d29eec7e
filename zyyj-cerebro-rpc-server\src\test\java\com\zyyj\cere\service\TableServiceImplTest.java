package com.zyyj.cere.service;

import com.zyyj.cere.CerebroRPCServerApplication;
import com.zyyj.cere.pojo.body.TableAddBody;
import com.zyyj.cere.pojo.entity.TableEntity;
import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CerebroRPCServerApplication.class)
public class TableServiceImplTest extends TestCase {

    @Autowired
    TableServiceImpl tableService;

    @Test
    public void testSetTableByManual() {
//        String data;
//        data = "[{\"id\":1,\"name\":\"黄河\",\"sex\":2},{\"id\":2,\"name\":\"黄河2\",\"sex\":3},{\"id\":3,\"name\":\"黄河3\",\"sex\":4}]";

        List<List<String>> data= new ArrayList<>();
        data.add(Arrays.asList("id", "name", "sex"));
        data.add(Arrays.asList("1", "name2", "12"));
        data.add(Arrays.asList("2", "name3", "13"));
        data.add(Arrays.asList("3", "name4", "14"));
        data.add(Arrays.asList("4", "name5", "15"));

        String tableName = "test22";

        TableAddBody t = TableAddBody
                .builder()
                .businessId(1L)
                .name("test表")
                .tableName(tableName)
                .fieldJson("[{\"field\":\"id\",\"comment\":\"\",\"type\":\"long\",\"priKey\":1},{\"field\":\"name\",\"comment\":\"名称\",\"type\":\"STRING\",\"priKey\":0},{\"field\":\"sex\",\"comment\":\"性别\",\"type\":\"long\",\"priKey\":0}]")
                .source(1)
                .build();
        System.out.println(data);
        System.out.println(tableService.setTableByManual(t,data));
        System.out.println(tableService.getTableData(tableName));
    }

    @Test
    public void testTableINfo(){
        String tableName = "test20";
        System.out.println("----------------结构--------------------");
        System.out.println(tableService.getTableStruct(41L).toString());
        System.out.println("------------------数据-----------------------");
        System.out.println(tableService.getTableData(tableName));
    }
}