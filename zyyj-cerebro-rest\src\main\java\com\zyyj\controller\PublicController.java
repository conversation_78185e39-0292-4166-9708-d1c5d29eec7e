package com.zyyj.controller;


import com.qiniu.util.Auth;
import com.qiniu.util.StringMap;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.properties.QiniuProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;


@RestController
@RequestMapping("/api/v1/public")
public class PublicController {

    @Autowired
    QiniuProperty qiniuProperty;

    @GetMapping("/qiniu/token")
    public Map<String, String> getQiniuToken() {
        Auth auth = Auth.create(qiniuProperty.getAccessKey(), qiniuProperty.getSecretKey());
        String upToken = auth.uploadToken(qiniuProperty.getBucket(), (String)null, 3600L * 24, (StringMap)null, true);
        Map<String, String> data = new HashMap<String,String >();
        data.put("up_token", upToken);
        return data;
    }

}
