server:
  port: 8006

eureka:
  instance:
    prefer-ip-address: true
    non-secure-port: 9006
  client:
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: ******************************************/eureka/

spring:
  application:
    name: ZYYJ-CEREBRO-THRIFT
  datasource:
    url: **********************************************************************************************************************************************
    username: root
    password: 85169336
    driver-class-name: com.mysql.cj.jdbc.Driver
#    test-on-borrow: true
#    validation-query: SELECT 1
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
  redis:
    database: 1
    host: **************
    port: 6379
    password: Vhzu4U5MZJw2lmc
    timeout: 60000
service:
  #thrift 客户端配置
  spark: ZYYJ-SPARK-THRIFT
zyyj:
  rpc:
    thrift:
      server:
        listen_port: 9006
        request_timeout: 300000  # 5 minutes in milliseconds
        max_frame_size: 16777216  # 16MB
        client_idle_timeout: 300000  # 5 minutes
      client:
        connection_timeout: 60000  # 60 seconds
        receive_timeout: 300000    # 5 minutes
        max_connections_per_host: 50
        max_frame_size: 16777216   # 16MB
# 详细的调试日志配置
logging:
  level:
    root: INFO
    com.facebook.swift: DEBUG
    com.facebook.nifty: DEBUG
    org.apache.thrift: DEBUG
    com.zyyj.rpc: DEBUG
    com.zyyj.spark: DEBUG
    com.zyyj.cere: DEBUG
    io.netty: DEBUG
    org.jboss.netty: DEBUG

kafka:
  bootstrap-server:
    - ***************:9092
  #connector
  connector-host: http://***************:9093
