server:
  port: 8006

eureka:
  instance:
    prefer-ip-address: true
    non-secure-port: 9006
  client:
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: ******************************************/eureka/

spring:
  application:
    name: ZYYJ-CEREBRO-THRIFT
  datasource:
    url: **********************************************************************************************************************************************
    username: root
    password: 85169336
    driver-class-name: com.mysql.cj.jdbc.Driver
#    test-on-borrow: true
#    validation-query: SELECT 1
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
  redis:
    database: 1
    host: **************
    port: 6379
    password: Vhzu4U5MZJw2lmc
    timeout: 60000
service:
  #thrift 客户端配置
  spark: ZYYJ-SPARK-THRIFT
zyyj:
  rpc:
    thrift:
      server:
        listen_port: 9006
# 打印sql
#logging:
#  config: config/log4j2.xml

kafka:
  bootstrap-server:
    - ***************:9092
  #connector
  connector-host: http://***************:9093
