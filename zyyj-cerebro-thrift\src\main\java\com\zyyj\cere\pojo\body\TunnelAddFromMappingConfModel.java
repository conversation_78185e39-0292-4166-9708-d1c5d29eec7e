package com.zyyj.cere.pojo.body;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.annotation.Nullable;

@Setter
@NoArgsConstructor
@ThriftStruct
public class TunnelAddFromMappingConfModel {
    // 是否是主键
    private int priKey;
    private String sourceField;
    private String field;
    private String comment;
    private String type;

    // 如果是更新字段， 表示该字段是原始库中的字段名, 需要更新成字段名 {name}
    @Nullable
    private String dbField="";
    // 是否是更新数据
    @Nullable
    private Byte updateField =0;
    // 是否更新 comment
    @Nullable
    private Byte updateComment=0;


    @ThriftConstructor
    public TunnelAddFromMappingConfModel(int priKey, String sourceField, String field, String comment, String type, String dbField, Byte updateField, Byte updateComment) {
        this.priKey = priKey;
        this.sourceField = sourceField;
        this.field = field;
        this.comment = comment;
        this.type = type;
        this.dbField = dbField;
        this.updateField = updateField;
        this.updateComment = updateComment;
    }



    @ThriftField(1)
    public int getPriKey() {
        return priKey;
    }
    @ThriftField(2)
    public String getSourceField() {
        return sourceField;
    }
    @ThriftField(3)
    public String getField() {
        return field;
    }
    @ThriftField(4)
    public String getComment() {
        return comment;
    }
    @ThriftField(5)
    public String getType() {
        return type;
    }

    @ThriftField(6)
    public Byte getUpdateField() {
        return updateField;
    }

    @ThriftField(7)
    public String getDbField() {
        return dbField;
    }

    @ThriftField(8)
    public Byte getUpdateComment() {
        return updateComment;
    }
}
