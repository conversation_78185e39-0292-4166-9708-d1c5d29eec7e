# 启动本地服务（Spark RPC在158服务器运行）
Write-Host "=== 启动本地 Cerebro 服务 ===" -ForegroundColor Green
Write-Host "时间: $(Get-Date)"
Write-Host ""

# 创建日志目录
if (-not (Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs" -Force
    Write-Host "创建日志目录: logs"
}

# 设置Java环境
$env:JAVA_HOME = "C:\Program Files\Java\jdk1.8.0_141"
Write-Host "Java环境: $env:JAVA_HOME"
Write-Host ""

Write-Host "📝 注意: Spark RPC服务运行在158服务器上 (/opt/start-spr.sh)" -ForegroundColor Yellow
Write-Host ""

# 启动 Cerebro RPC 服务
Write-Host "🚀 启动 Cerebro RPC 服务..."
$cerebroProcess = Start-Process -FilePath "$env:JAVA_HOME\bin\java" -ArgumentList `
    "-Dspring.profiles.active=dev",
    "-Dspring.main.allow-bean-definition-overriding=true",
    "-Dserver.port=9007",
    "-Dzyyj.rpc.thrift.server.listen_port=9006",
    "-Deureka.client.serviceUrl.defaultZone=http://admin:admin123@***************:8761/eureka/",
    "-Dspring.config.location=file:E:\dev\project\zyyj-cerebro\zyyj-cerebro-rpc-server\src\main\resources\dev\application.yml",
    "-jar","E:\dev\project\zyyj-cerebro\bak1.0\zyyj-cerebro-rpc-server\zyyj-cerebro-rpc-server-bin\zyyj-cerebro-rpc-server\zyyj-cerebro-rpc-server.jar" `
    -RedirectStandardOutput "logs\cerebro-rpc.log" `
    -RedirectStandardError "logs\cerebro-rpc-error.log" `
    -PassThru

Write-Host "Cerebro RPC 进程ID: $($cerebroProcess.Id)" -ForegroundColor Green
Write-Host ""

# 等待20秒
Write-Host "等待Cerebro RPC服务启动 (20秒)..."
Start-Sleep -Seconds 20

# 启动 REST API 服务
Write-Host "🚀 启动 REST API 服务..."
$restProcess = Start-Process -FilePath "$env:JAVA_HOME\bin\java" -ArgumentList `
    "-Dserver.port=8005",
    "-Dspring.profiles.active=dev",
    "-Dspring.application.name=ZYYJ-CEREBRO-REST",
    "-Deureka.instance.appname=ZYYJ-CEREBRO-REST",
    "-Deureka.instance.non-secure-port=8005",
    "-Dspring.main.allow-bean-definition-overriding=true",
    "-Deureka.client.serviceUrl.defaultZone=http://admin:admin123@***************:8761/eureka/",
    "-Dservice.cerebro=ZYYJ-CEREBRO-THRIFT",
    "-Dscan.package=com.zyyj.controller",
    "-jar","E:\dev\project\zyyj-cerebro\zyyj-cerebro-rest\target\zyyj-cerebro-rest.jar" `
    -RedirectStandardOutput "logs\rest-api.log" `
    -RedirectStandardError "logs\rest-api-error.log" `
    -PassThru

Write-Host "REST API 进程ID: $($restProcess.Id)" -ForegroundColor Green
Write-Host ""

# 等待10秒
Write-Host "等待REST API服务启动 (10秒)..."
Start-Sleep -Seconds 10

Write-Host ""
Write-Host "=== 启动完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "服务访问地址:" -ForegroundColor Yellow
Write-Host "  REST API: http://localhost:8005" -ForegroundColor White
Write-Host "  Cerebro RPC HTTP: http://localhost:9007" -ForegroundColor White
Write-Host "  Spark RPC: http://***************:8081 (远程)" -ForegroundColor White
Write-Host ""
Write-Host "远程集群Web界面:" -ForegroundColor Yellow
Write-Host "  Spark Master: http://***************:8080" -ForegroundColor White
Write-Host "  HDFS: http://***************:9870" -ForegroundColor White
Write-Host "  YARN: http://***************:8088" -ForegroundColor White
Write-Host ""
Write-Host "进程ID:" -ForegroundColor Yellow
Write-Host "  Cerebro RPC: $($cerebroProcess.Id)" -ForegroundColor White
Write-Host "  REST API: $($restProcess.Id)" -ForegroundColor White
Write-Host ""
Write-Host "日志查看:" -ForegroundColor Yellow
Write-Host "  Get-Content logs\cerebro-rpc.log -Tail 50 -Wait" -ForegroundColor White
Write-Host "  Get-Content logs\rest-api.log -Tail 50 -Wait" -ForegroundColor White
Write-Host ""
Write-Host "测试创建表接口:" -ForegroundColor Yellow
Write-Host "  .\test-create-table-debug.ps1" -ForegroundColor White
Write-Host ""
Write-Host "停止服务:" -ForegroundColor Yellow
Write-Host "  Stop-Process -Id $($cerebroProcess.Id)" -ForegroundColor White
Write-Host "  Stop-Process -Id $($restProcess.Id)" -ForegroundColor White
