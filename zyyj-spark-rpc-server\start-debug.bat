@echo off
cd target
set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_141
set HADOOP_HOME=E:\dev\project\zyyj-cerebro\hadoop

echo Starting Spark RPC Server with debug logging...
echo Output will be saved to debug.log

java -Xms512m -Xmx1g ^
  -Dspring.profiles.active=dev ^
  -Dlogging.level.root=INFO ^
  -Dlogging.level.com.zyyj=DEBUG ^
  -Dlogging.level.org.springframework=INFO ^
  -Dlogging.level.org.glassfish.jersey.server.internal.scanning=ERROR ^
  -Dzyyj.rpc.thrift.server.listen_port=9009 ^
  -Deureka.client.serviceUrl.defaultZone=******************************************/eureka/ ^
  -Dspring.main.allow-bean-definition-overriding=true ^
  -Dspark.metastoreUris=thrift://***************:9083 ^
  -Dspark.warehouseDir=file:///E:/dev/project/zyyj-cerebro/warehouse/ ^
  -Dspark.master=local[*] ^
  -Dspark.appName=ZYYJ-SPARK-RPC-SERVER ^
  -Dspark.driver=localhost ^
  -Dspark.driver.bindAddress=0.0.0.0 ^
  -Dspark.ui.showConsoleProgress=false ^
  -Dspark.sql.adaptive.enabled=false ^
  -Dhadoop.home.dir=E:\dev\project\zyyj-cerebro\hadoop ^
  -jar zyyj-spark-rpc-server.jar > debug.log 2>&1

echo Application finished. Check debug.log for details.
pause
