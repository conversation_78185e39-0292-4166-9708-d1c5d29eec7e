# zyyj-cerebro 项目启动指南

## 项目概述

zyyj-cerebro 是一个基于 Spring Boot 和 Apache Thrift 构建的分布式服务系统，包含以下核心服务：

- **zyyj-cerebro-rpc-server**: 核心 RPC 服务
- **zyyj-cerebro-rest**: REST API 服务  
- **zyyj-spark-rpc-server**: Spark 大数据处理服务

## 环境要求

- **JDK**: 1.8+
- **Maven**: 3.6+
- **MySQL**: 8.0
- **Redis**: 6.0+
- **Apache Spark**: 3.0.0 (可选)

## 快速启动

### 1. 编译打包

```bash
# 进入项目根目录
cd zyyj-cerebro

# 编译打包 (本地环境)
mvn clean package -P local -DskipTests

# 其他环境选项
# mvn clean package -P dev -DskipTests    # 开发环境
# mvn clean package -P pro -DskipTests    # 生产环境
```

### 2. 启动服务

**重要**: 请按照以下顺序启动服务，确保每个服务完全启动后再启动下一个。

#### 2.1 启动 RPC 服务器 (必须)

```bash
cd zyyj-cerebro-rpc-server/target
java -Xms1024M -Xmx1024M -jar zyyj-cerebro-rpc-server.jar
```

**服务信息**:
- HTTP 端口: 8006
- Thrift 端口: 9006
- 主类: `com.zyyj.cere.CerebroRPCServerApplication`

#### 2.2 启动 REST 服务器 (必须)

```bash
cd zyyj-cerebro-rest/target
java -Xms1024M -Xmx1024M -jar zyyj-cerebro-rest.jar
```

**服务信息**:
- HTTP 端口: 8005
- 主类: `com.zyyj.CerebroRestApplication`
- 提供 RESTful API 接口
- 集成 Swagger 文档
- 已配置 CORS 跨域支持

#### 2.3 启动 Spark 服务器 (可选)

**Windows PowerShell**:
```powershell
cd zyyj-spark-rpc-server/target
$env:JAVA_HOME = "C:\Program Files\Java\jdk1.8.0_141"
$env:HADOOP_HOME = "E:\dev\project\zyyj-cerebro\hadoop"
java -Xms1g -Xmx2g `
  "-Dspring.profiles.active=dev" `
  "-Deureka.client.serviceUrl.defaultZone=******************************************/eureka/" `
  "-Dspring.main.allow-bean-definition-overriding=true" `
  "-Dspark.metastoreUris=thrift://***************:9083" `
  "-Dspark.warehouseDir=file:///E:/dev/project/zyyj-cerebro/warehouse/" `
  "-Dspark.master=local[*]" `
  "-Dspark.appName=ZYYJ-SPARK-RPC-SERVER" `
  "-Dspark.driver=localhost" `
  "-Dspark.driver.bindAddress=0.0.0.0" `
  "-Dhadoop.home.dir=E:\dev\project\zyyj-cerebro\hadoop" `
  -jar zyyj-spark-rpc-server.jar
```

**Linux/Mac**:
```bash
cd zyyj-spark-rpc-server/target
export JAVA_HOME="/usr/lib/jvm/java-8-openjdk"
export HADOOP_HOME="/opt/hadoop"
java -Xms1g -Xmx2g \
  -Dspring.profiles.active=dev \
  -Deureka.client.serviceUrl.defaultZone=******************************************/eureka/ \
  -Dspring.main.allow-bean-definition-overriding=true \
  -Dspark.metastoreUris=thrift://***************:9083 \
  -Dspark.warehouseDir=file:///opt/zyyj-cerebro/warehouse/ \
  -Dspark.master=local[*] \
  -Dspark.appName=ZYYJ-SPARK-RPC-SERVER \
  -Dspark.driver=localhost \
  -Dspark.driver.bindAddress=0.0.0.0 \
  -Dhadoop.home.dir=/opt/hadoop \
  -jar zyyj-spark-rpc-server.jar
```

**服务信息**:
- HTTP 端口: 8080
- Spark UI: http://localhost:4040
- 提供大数据处理能力
- 需要 Hadoop 3.2.0 兼容环境

## 服务验证

### 检查服务状态

1. **Eureka 注册中心**: http://***************:8761/
   - 查看所有服务注册状态

2. **REST API 文档**: http://localhost:8005/swagger-ui.html
   - 查看和测试 API 接口

3. **健康检查**:
   ```bash
   # RPC 服务
   curl http://localhost:8006/actuator/health
   
   # REST 服务  
   curl http://localhost:8005/actuator/health
   ```

### 日志检查

启动过程中注意观察控制台输出，确认：
- 数据库连接成功
- Redis 连接成功
- Eureka 注册成功
- Thrift 服务启动成功

## 配置说明

### 数据库配置

当前使用开发环境配置 (`dev` profile):
- **数据库**: MySQL 8.0
- **地址**: ***************:3306
- **数据库名**: cerebro
- **用户名**: root

### Redis 配置

- **地址**: **************:6379
- **数据库**: 1
- **密码**: 已配置

### Kafka 配置 (可选)

- **Bootstrap Server**: ***************:9092
- **Connector**: http://***************:9093

## 一键启动脚本

### Linux/Mac 环境

创建 `start-all.sh`:

```bash
#!/bin/bash

echo "===== 启动 zyyj-cerebro 项目 ====="

# 编译打包
echo "正在编译打包..."
mvn clean package -P local -DskipTests

if [ $? -ne 0 ]; then
    echo "编译失败，请检查错误信息"
    exit 1
fi

# 启动 RPC 服务
echo "启动 RPC 服务..."
cd zyyj-cerebro-rpc-server/target
nohup java -Xms1024M -Xmx1024M -jar zyyj-cerebro-rpc-server.jar > rpc.log 2>&1 &
RPC_PID=$!
echo "RPC 服务 PID: $RPC_PID"

# 等待 RPC 服务启动
sleep 30

# 启动 REST 服务
echo "启动 REST 服务..."
cd ../../zyyj-cerebro-rest/target
nohup java -Xms1024M -Xmx1024M -jar zyyj-cerebro-rest.jar > rest.log 2>&1 &
REST_PID=$!
echo "REST 服务 PID: $REST_PID"

# 启动 Spark 服务 (可选)
echo "启动 Spark 服务..."
cd ../../zyyj-spark-rpc-server/target
export HADOOP_HOME="/opt/hadoop"
nohup java -Xms1g -Xmx2g \
  -Dspring.profiles.active=dev \
  -Deureka.client.serviceUrl.defaultZone=******************************************/eureka/ \
  -Dspring.main.allow-bean-definition-overriding=true \
  -Dspark.metastoreUris=thrift://***************:9083 \
  -Dspark.warehouseDir=file:///opt/zyyj-cerebro/warehouse/ \
  -Dspark.master=local[*] \
  -Dspark.appName=ZYYJ-SPARK-RPC-SERVER \
  -Dspark.driver=localhost \
  -Dspark.driver.bindAddress=0.0.0.0 \
  -Dhadoop.home.dir=/opt/hadoop \
  -jar zyyj-spark-rpc-server.jar > spark.log 2>&1 &
SPARK_PID=$!
echo "Spark 服务 PID: $SPARK_PID"

echo "===== 所有服务启动完成 ====="
echo "RPC 服务: http://localhost:8006"
echo "REST 服务: http://localhost:8005"
echo "Eureka: http://***************:8761/"
```

### Windows 环境

创建 `start-all.bat`:

```batch
@echo off
echo ===== 启动 zyyj-cerebro 项目 =====

echo 正在编译打包...
call mvn clean package -P local -DskipTests

if %errorlevel% neq 0 (
    echo 编译失败，请检查错误信息
    pause
    exit /b 1
)

echo 启动 RPC 服务...
cd zyyj-cerebro-rpc-server\target
start "RPC服务" java -Xms1024M -Xmx1024M -jar zyyj-cerebro-rpc-server.jar

timeout /t 30

echo 启动 REST 服务...
cd ..\..\zyyj-cerebro-rest\target
start "REST服务" java -Xms1024M -Xmx1024M -jar zyyj-cerebro-rest.jar

echo 启动 Spark 服务...
cd ..\..\zyyj-spark-rpc-server\target
set HADOOP_HOME=E:\dev\project\zyyj-cerebro\hadoop
start "Spark服务" java -Xms1g -Xmx2g ^
  -Dspring.profiles.active=dev ^
  -Deureka.client.serviceUrl.defaultZone=******************************************/eureka/ ^
  -Dspring.main.allow-bean-definition-overriding=true ^
  -Dspark.metastoreUris=thrift://***************:9083 ^
  -Dspark.warehouseDir=file:///E:/dev/project/zyyj-cerebro/warehouse/ ^
  -Dspark.master=local[*] ^
  -Dspark.appName=ZYYJ-SPARK-RPC-SERVER ^
  -Dspark.driver=localhost ^
  -Dspark.driver.bindAddress=0.0.0.0 ^
  -Dhadoop.home.dir=E:\dev\project\zyyj-cerebro\hadoop ^
  -jar zyyj-spark-rpc-server.jar

echo ===== 所有服务启动完成 =====
echo RPC 服务: http://localhost:8006
echo REST 服务: http://localhost:8005
echo Eureka: http://***************:8761/
pause
```

## 停止服务

### 查找并停止进程

```bash
# 查找进程
ps aux | grep zyyj-cerebro

# 停止服务
kill -9 <PID>

# 或使用项目提供的停止脚本
./stop.sh
```

## 常见问题

### 1. 端口占用

如果遇到端口占用错误：
```bash
# 查看端口占用
netstat -tulpn | grep :8005
netstat -tulpn | grep :8006

# 停止占用进程
kill -9 <PID>
```

### 2. 数据库连接失败

- 检查 MySQL 服务是否启动
- 确认数据库 `cerebro` 是否存在
- 验证用户名密码是否正确

### 3. Redis 连接失败

- 检查 Redis 服务是否启动
- 确认网络连接是否正常
- 验证密码配置

### 4. Eureka 注册失败

- 检查 Eureka 服务器是否可访问
- 确认网络配置是否正确

## 开发调试

### IDE 中启动

在 IntelliJ IDEA 或 Eclipse 中：

1. 导入 Maven 项目
2. 设置 JDK 版本为 1.8
3. 配置 VM 参数: `-Xms1024M -Xmx1024M`
4. 按顺序运行主类：
   - `com.zyyj.cere.CerebroRPCServerApplication`
   - `com.zyyj.CerebroRestApplication`

### 日志配置

项目使用 Log4j2，日志配置文件位于：
- `src/main/resources/log4j2.xml`

可以通过修改日志级别来调试问题。

---

**注意**: 首次启动前请确保已执行 `database_init.sql` 初始化数据库。