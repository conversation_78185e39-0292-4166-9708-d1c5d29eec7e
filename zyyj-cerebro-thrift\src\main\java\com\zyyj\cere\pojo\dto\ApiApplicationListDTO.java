package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.sdk.processor.annotation.ThriftPaged;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2020/11/13 16:31
 */
@ThriftStruct
@ThriftPaged
@Setter
@Builder
@ToString
@NoArgsConstructor
public class ApiApplicationListDTO{
    private Integer id;
    private String apiName;
    private String describe;
    private String serviceName;
    private Integer method;

    @ThriftConstructor
    public ApiApplicationListDTO(Integer id, String apiName, String describe, String serviceName, Integer method) {
        this.id = id;
        this.apiName = apiName;
        this.describe = describe;
        this.serviceName = serviceName;
        this.method = method;
    }

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public String getApiName() {
        return apiName;
    }

    @ThriftField(3)
    public String getDescribe() {
        return describe;
    }

    @ThriftField(4)
    public String getServiceName() {
        return serviceName;
    }

    @ThriftField(5)
    public Integer getMethod() {
        return method;
    }
}
