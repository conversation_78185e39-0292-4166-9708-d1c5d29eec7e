package com.zyyj.spark;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableScheduling
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})  //不连接数据库
public class SparkRPCServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(SparkRPCServerApplication.class, args);
    }
}
