package com.zyyj.controller;

import com.zyyj.cere.service.ApiTestService;
import com.zyyj.domain.exception.ApplicationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/17 17:19
 */

@RestController
@RequestMapping("/api/v1/cerebro")
public class ApiTestController {

    @Autowired
    ApiTestService apiTestService;

    /**
     * @Description: 测试API(get)
     * @Param: [serverName, apiName, request]
     * @return: java.util.Map<java.lang.String, java.util.List < java.lang.String>>
     * @Author: bravelee
     * @Date: 2020/12/4
     */
    @GetMapping("/{serverName}/{apiName}/get")
    public Map<String, List<String>> getApiData(@PathVariable String serverName, @PathVariable String apiName, HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Map<String, String> map = new HashMap<>();
        if (map != null) {
            for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                String mapKey = entry.getKey();
                String mapValue = entry.getValue()[0];
                map.put(mapKey, mapValue);
            }
        }
        return apiTestService.getAPiData(serverName, apiName, map);
    }

    /**
     * @Description: 测试API(post)
     * @Param: [serverName, apiName, map]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/12/4
     */
    @PostMapping("/{serverName}/{apiName}/post")
    public void postApiData(@PathVariable String serverName, @PathVariable String apiName, @RequestBody Map<String, String> map) {
        String message = apiTestService.postAPiData(serverName, apiName, map);
        if (!message.equals("")) {
            throw new ApplicationException(message);
        }
    }


    /**
     * @Description: 数据湖执行sql 测试api
     * @Param: [sql]
     * @return: java.util.List<java.lang.String>
     * @Author: bravelee
     * @Date: 2020/12/4
     */
    @GetMapping("/spark_sql")
    public List<String> getSparkSqlRes(String sql) {

        return apiTestService.getSparkSqlRes(sql);
    }

}
