#!/bin/bash
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export HADOOP_USER_NAME=root

nohup $JAVA_HOME/bin/java \
    -Xms512m \
    -Xmx1g \
    -Dserver.port=8081 \
    -Dspring.profiles.active=dev \
    -Dspring.application.name=ZYYJ-SPARK-THRIFT \
    -Deureka.instance.appname=ZYYJ-SPARK-THRIFT \
    -Deureka.instance.non-secure-port=8081 \
    -Deureka.instance.hostname=*************** \
    -Deureka.instance.ip-address=*************** \
    -Deureka.client.serviceUrl.defaultZone=http://admin:admin123@***************:8761/eureka/ \
    -Dspring.main.allow-bean-definition-overriding=true \
    -Dspark.master=spark://***************:7077 \
    -Dspark.appName=ZYYJ-SPARK-RPC-SERVER \
    -Dspark.warehouseDir=hdfs://***************:9000/spark-warehouse \
    -Dspark.metastoreUris=thrift://***************:9083 \
    -Dspark.driver.host=*************** \
    -Dspark.driver.bindAddress=*************** \
    -Dspark.driver.port=0 \
    -Dspark.blockManager.port=0 \
    -Dspark.sql.adaptive.enabled=false \
    -Dzyyj.rpc.thrift.server.listen_port=9009 \
    -DHADOOP_USER_NAME=root \
    -Duser.name=root \
    -Dhadoop.security.authentication=simple \
    -Dspark.hadoop.hadoop.security.authentication=simple \
    -Dspark.hadoop.hadoop.security.authorization=false \
    -Dspark.sql.warehouse.dir=hdfs://***************:9000/spark-warehouse \
    -jar /opt/zyyj-spark-rpc-server.jar \
    > /opt/logs/spark-rpc.log 2>&1 &
