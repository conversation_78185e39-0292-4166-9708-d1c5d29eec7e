package com.zyyj.spark.service;

import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.rpc.thrift.server.ThriftServiceHandler;
import com.zyyj.spark.config.SparkConfig;
import io.delta.tables.DeltaTable;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Encoders;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
@ThriftServiceHandler
public class DeltaServiceImpl implements DeltaService {

    @Autowired
    SparkConfig sparkConfig;

    @Override
    public void createDatabase(String name) {
        sparkConfig.getSession().sql("CREATE DATABASE " + name);
    }

    @Override
    public void createTable(String sql) {
        sparkConfig.getSession().sql(sql);
    }

    @Override
    public void createTableByJson(String tableName, String data) {
        formatForDatasetByJson(data).show();
        formatForDatasetByJson(data)
                .write()
                .format("delta")
                .saveAsTable(tableName);
        Dataset<Row> df = sparkConfig.getSession().table(tableName);
        df.show();
    }

    @Override
    public void createView(String viewName, String sql) {
        sparkConfig.getSession().sql("CREATE VIEW " + viewName + " AS " + sql);
    }

    @Override
    public List<String> getTableList(String tableName) {
        return sparkConfig.getSession().table(tableName).toJSON().collectAsList();
    }

    @Override
    public List<String> getTableStruct(String tableName) {
        return sparkConfig.getSession().sql("DESC " + tableName).toJSON().collectAsList();
    }

    @Override
    public List<String> getTableListBySql(String sql) {
        return sparkConfig.getSession().sql(sql).toJSON().collectAsList();
    }

    @Override
    public List<String> getTableStructBySql(String sql) {
        return sparkConfig.getSession().sql("DESC QUERY " + sql).toJSON().collectAsList();
    }


    @Override
    public void insertTableByList(String tableName, String data) {
        formatForDatasetByJson(data)
                .write()
                .format("delta")
                .mode("append")
                .saveAsTable(tableName);
    }

    @Override
    public void renameTable(String oldName, String newName) {
        execSql("ALTER TABLE " + oldName + " RENAME TO " + newName);
    }

    @Override
    public void removeTable(String tableName, boolean isView) {
        if (isView) {//删除view
            execSql("DROP VIEW " + tableName);
        } else {//删除table
            execSql("DROP TABLE " + tableName);
        }
    }

    @Override
    public void execSql(String sql) {
        sparkConfig.getSession().sql(sql);
    }

    @Override
    public List<String> querySql(String sql) {
        return sparkConfig.getSession().sql(sql).toJSON().collectAsList();
    }


    @Override
    public Map<String, String> previewBySql(String sql, String order, Integer page, Integer size) {
        Dataset<Row> df = sparkConfig.getSession().sql(sql).orderBy(order);
        return new HashMap<String, String>() {{
            put("total", String.valueOf(df.count()));
            put("data", df.limit(page * size).except(df.limit((page - 1) * size)).toJSON().collectAsList().toString());
        }};
    }


    @Override
    public Map<String, String> preview(String tableName, String order, Integer page, Integer size) {
        Dataset<Row> df = sparkConfig.getSession().table(tableName).orderBy(order);
        return new HashMap<String, String>() {{
            put("total", String.valueOf(df.count()));
            put("data", df.limit(page * size).except(df.limit((page - 1) * size)).toJSON().collectAsList().toString());
        }};
    }


    @Override
    public boolean queryTableIsExist(String tableName) {
        String sql = "SHOW TABLES LIKE '" + tableName + "'";
        SparkSession session = sparkConfig.getSession();
        List<String> tables = session.sql(sql).toJSON().collectAsList();
        return !tables.isEmpty();
    }

    /**
     * 格式化数据
     * 将Json格式数据转换为Dataset<Row>格式数据
     *
     * @param data
     * @return
     */
    public Dataset<Row> formatForDatasetByJson(String data) {
        return sparkConfig
                .getSession()
                .read()
                .json(sparkConfig.getSession()
                        .createDataset(Collections.singletonList(data), Encoders.STRING()));
    }


}
