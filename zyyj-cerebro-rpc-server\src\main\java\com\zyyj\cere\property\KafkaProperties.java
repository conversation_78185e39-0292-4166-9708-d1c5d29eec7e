package com.zyyj.cere.property;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "kafka")
public class KafkaProperties {

    private String connectorHost;
    private String[] bootstrapServer;

    public String getConnectorHost() {
        return connectorHost;
    }

    public KafkaProperties setConnectorHost(String connectorHost) {
        this.connectorHost = connectorHost;
        return this;
    }

    public String[] getBootstrapServer() {
        return bootstrapServer;
    }

    public KafkaProperties setBootstrapServer(String[] bootstrapServer) {
        this.bootstrapServer = bootstrapServer;
        return this;
    }


    public List<String> getBootstrapServerList() {
        ArrayList<String> list = new ArrayList<>();
        for (String server: bootstrapServer) {
            list.add(server);
        }
        return list;
    };

    public String getBootstrapServers() {
        return String.join(",",this.bootstrapServer);
    };
}
