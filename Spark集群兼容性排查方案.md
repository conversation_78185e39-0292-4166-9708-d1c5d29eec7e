# Spark集群兼容性问题排查方案

## 🎯 目标
解决Spark RPC服务连接到157服务器Spark集群时的序列化兼容性问题，实现分布式计算能力。

## 🔍 问题分析

### 当前问题
- **现象**: 跨节点序列化失败
- **错误**: `ClassCastException: scala.collection.immutable.List$SerializationProxy`
- **影响**: 无法使用Spark集群的分布式计算能力

### 根本原因
1. **序列化机制不一致**: 客户端与集群使用不同的序列化策略
2. **Scala版本细微差异**: 虽然都是2.12.10，但可能有构建差异
3. **Delta Lake兼容性**: Delta组件可能有特殊序列化需求

## 📋 排查步骤

### 阶段1: 环境信息收集

#### 1.1 检查157服务器Spark集群详细信息
```bash
# 连接到157服务器
ssh root@***************

# 检查Spark版本和构建信息
/opt/spark/bin/spark-submit --version

# 检查Scala版本
ls -la /opt/spark/jars/scala-*

# 检查Delta版本
ls -la /opt/spark/jars/delta-*

# 检查序列化配置
cat /opt/spark/conf/spark-defaults.conf | grep -i serial
```

#### 1.2 检查158服务器应用信息
```bash
# 连接到158服务器
ssh root@***************

# 检查应用中的依赖版本
jar -tf /opt/zyyj-spark-rpc-server.jar | grep -E "(scala|delta)" | head -10

# 检查JVM版本
java -version
```

#### 1.3 对比版本信息
创建版本对比表：
```
组件          | 157集群版本 | 158应用版本 | 兼容性
-------------|------------|------------|--------
Spark        | 3.0.0      | 3.0.0      | ✅
Scala        | 2.12.10    | 2.12.10    | ❓
Delta        | ?          | 0.8.0      | ❓
JVM          | 1.8.0_362  | 1.8.0_362  | ✅
```

### 阶段2: 序列化配置统一

#### 2.1 统一序列化器配置
```bash
# 修改157服务器Spark配置
ssh root@***************
cd /opt/spark/conf

# 备份原配置
cp spark-defaults.conf spark-defaults.conf.bak

# 添加/修改序列化配置
cat >> spark-defaults.conf << EOF
# 序列化配置统一
spark.serializer                    org.apache.spark.serializer.KryoSerializer
spark.serializer.objectStreamReset  100
spark.kryo.unsafe                    true
spark.kryo.referenceTracking         false
spark.kryo.registrationRequired     false
EOF
```

#### 2.2 重启Spark集群
```bash
# 停止Spark集群
/opt/spark/sbin/stop-all.sh

# 启动Spark集群
/opt/spark/sbin/start-all.sh

# 验证启动
jps | grep -E "Master|Worker"
```

### 阶段3: Delta Lake兼容性处理

#### 3.1 检查Delta版本兼容性
```bash
# 检查157服务器Delta版本
ssh root@***************
find /opt -name "*delta*" -type f

# 检查158应用Delta版本
ssh root@***************
jar -tf /opt/zyyj-spark-rpc-server.jar | grep delta
```

#### 3.2 统一Delta版本
如果版本不一致，需要：
1. **升级集群Delta版本**（推荐）
2. **降级应用Delta版本**

```bash
# 方案1: 升级集群Delta版本
ssh root@***************
cd /opt/spark/jars
wget https://repo1.maven.org/maven2/io/delta/delta-core_2.12/0.8.0/delta-core_2.12-0.8.0.jar
# 删除旧版本，重启集群
```

### 阶段4: 渐进式测试

#### 4.1 创建测试启动脚本
```bash
# 创建集群模式测试脚本
cat > start-spark-rpc-158-cluster-test.sh << 'EOF'
#!/bin/bash
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export HADOOP_USER_NAME=root

echo "=== 测试Spark集群模式 ==="

nohup $JAVA_HOME/bin/java \
    -Xms512m -Xmx1g \
    -Dserver.port=8081 \
    -Dspring.profiles.active=dev \
    -Dspring.application.name=ZYYJ-SPARK-THRIFT \
    -Deureka.instance.appname=ZYYJ-SPARK-THRIFT \
    -Deureka.instance.non-secure-port=9009 \
    -Deureka.instance.hostname=*************** \
    -Deureka.instance.ip-address=*************** \
    -Deureka.client.serviceUrl.defaultZone=******************************************/eureka/ \
    -Dspring.main.allow-bean-definition-overriding=true \
    -Dspark.master=spark://***************:7077 \
    -Dspark.appName=ZYYJ-SPARK-RPC-SERVER-CLUSTER \
    -Dspark.warehouseDir=hdfs://***************:9000/spark-warehouse \
    -Dspark.metastoreUris=thrift://***************:9083 \
    -Dspark.driver.host=*************** \
    -Dspark.driver.bindAddress=*************** \
    -Dspark.driver.port=0 \
    -Dspark.blockManager.port=0 \
    -Dspark.sql.adaptive.enabled=false \
    -Dspark.serializer=org.apache.spark.serializer.KryoSerializer \
    -Dspark.serializer.objectStreamReset=100 \
    -Dspark.kryo.unsafe=true \
    -Dspark.kryo.referenceTracking=false \
    -Dspark.kryo.registrationRequired=false \
    -Dzyyj.rpc.thrift.server.listen_port=9009 \
    -DHADOOP_USER_NAME=root \
    -Duser.name=root \
    -Dhadoop.security.authentication=simple \
    -Dspark.hadoop.hadoop.security.authentication=simple \
    -Dspark.hadoop.hadoop.security.authorization=false \
    -Dspark.sql.warehouse.dir=hdfs://***************:9000/spark-warehouse \
    -jar /opt/zyyj-spark-rpc-server.jar \
    > /opt/logs/spark-rpc-cluster-test.log 2>&1 &

echo "集群模式测试启动完成"
EOF
```

#### 4.2 逐步测试
```bash
# 1. 传输测试脚本
scp start-spark-rpc-158-cluster-test.sh root@***************:/opt/

# 2. 停止本地模式服务
ssh root@*************** "pkill -f zyyj-spark-rpc-server"

# 3. 启动集群模式测试
ssh root@*************** "chmod +x /opt/start-spark-rpc-158-cluster-test.sh && cd /opt && ./start-spark-rpc-158-cluster-test.sh"

# 4. 监控日志
ssh root@*************** "tail -f /opt/logs/spark-rpc-cluster-test.log"
```

### 阶段5: 问题定位和解决

#### 5.1 如果仍有序列化问题
```bash
# 检查具体的序列化错误
ssh root@*************** "grep -A 20 -B 5 'ClassCastException\|SerializationProxy' /opt/logs/spark-rpc-cluster-test.log"

# 可能的解决方案：
# 1. 禁用特定的优化
-Dspark.sql.adaptive.enabled=false
-Dspark.sql.adaptive.coalescePartitions.enabled=false

# 2. 强制使用Java序列化
-Dspark.serializer=org.apache.spark.serializer.JavaSerializer

# 3. 调整Kryo配置
-Dspark.kryo.registrator=com.zyyj.spark.util.CustomKryoRegistrator
```

#### 5.2 创建自定义Kryo注册器
如果需要，创建自定义Kryo注册器：
```java
// 在应用中添加
public class CustomKryoRegistrator implements KryoRegistrator {
    @Override
    public void registerClasses(Kryo kryo) {
        kryo.register(scala.collection.immutable.List.class);
        kryo.register(scala.collection.immutable.List$SerializationProxy.class);
        // 注册其他需要的类
    }
}
```

### 阶段6: 性能验证

#### 6.1 创建性能对比测试
```bash
# 测试本地模式性能
time curl -X POST http://localhost:8005/api/v1/table/add \
  -H "Content-Type: application/json" \
  -d '{"name":"Local Test","tableName":"perf_local_test","fieldJson":"[{\"field\":\"id\",\"type\":\"long\",\"priKey\":1}]","businessId":1,"source":1}'

# 测试集群模式性能  
time curl -X POST http://localhost:8005/api/v1/table/add \
  -H "Content-Type: application/json" \
  -d '{"name":"Cluster Test","tableName":"perf_cluster_test","fieldJson":"[{\"field\":\"id\",\"type\":\"long\",\"priKey\":1}]","businessId":1,"source":1}'
```

## 📊 预期结果

### 成功指标
1. **✅ 服务启动成功**: 无序列化异常
2. **✅ 创建表成功**: 返回SUCCESS响应
3. **✅ 集群连接正常**: 可以看到executor注册日志
4. **✅ 性能提升**: 相比本地模式有性能提升

### 回退方案
如果集群模式仍有问题：
1. **立即回退到v2.0本地模式**
2. **记录详细错误信息**
3. **考虑升级Spark版本到3.3+**

## 🔧 工具脚本

### 快速切换脚本
```bash
# 切换到本地模式
ssh root@*************** "pkill -f zyyj-spark-rpc-server && cd /opt && ./start-spark-rpc-158-fixed-v2.0.sh"

# 切换到集群模式
ssh root@*************** "pkill -f zyyj-spark-rpc-server && cd /opt && ./start-spark-rpc-158-cluster-test.sh"
```

---
**执行建议**: 在业务低峰期进行测试，确保有回退方案
