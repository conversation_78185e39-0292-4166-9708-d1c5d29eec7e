package com.zyyj.cere.repository;

import com.zyyj.cere.pojo.dto.DatasetDto;
import com.zyyj.cere.pojo.entity.DatasetEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public interface DatasetRepository extends JpaRepository<DatasetEntity, Integer>, JpaSpecificationExecutor<DatasetEntity> {

    List<DatasetEntity> findByParentIdAndStatus(Integer parentId,Integer status);

    List<DatasetEntity> findAllByStatus(Integer status);

    @Query(value = "update dataset set status = 1 where id = ?1 or parent_id = ?1",nativeQuery = true)
    @Modifying
    void deleteByParentIdOrId(Integer parentId);

    @Query(value = "select new com.zyyj.cere.pojo.dto.DatasetDto(dd.id,dd.name,dd.parentId,dd.type) from DatasetEntity dd where dd.status =0 and dd.type = ?1")
    List<DatasetDto> findByType(Integer type);

    @Query(value = "select new com.zyyj.cere.pojo.dto.DatasetDto(dd.id,dd.name,dd.parentId,dd.type) from DatasetEntity dd where dd.status =0 ")
    List<DatasetDto> findAllDto();

}