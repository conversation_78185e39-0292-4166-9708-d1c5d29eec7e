package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2020/12/3 09:39
 */
@Setter
@ToString
@ThriftStruct
@NoArgsConstructor
public class DataMsgDTO<T> {
    private T data;
    private String errMsg;

    @ThriftConstructor
    public DataMsgDTO(T data, String errMsg) {
        this.data = data;
        this.errMsg = errMsg;
    }

    @ThriftField(1)
    public T getData() {
        return data;
    }

    @ThriftField(2)
    public String getErrMsg() {
        return errMsg;
    }
}
