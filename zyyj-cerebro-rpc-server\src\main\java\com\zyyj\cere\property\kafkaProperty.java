package com.zyyj.cere.property;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


@Component
@ConfigurationProperties(prefix = "kafka")
public class kafkaProperty {

    private String connectorHost;
    private String[] bootstrapServer;

//    public kafkaProperty() {
//    }
//
//    public kafkaProperty(String connectorHost, String[] bootstrapServer) {
//        this.connectorHost = connectorHost;
//        this.bootstrapServer = bootstrapServer;
//    }



    public String[] getBootstrapServer() {
        return bootstrapServer;
    }

    public kafkaProperty setBootstrapServer(String[] bootstrapServer) {
        this.bootstrapServer = bootstrapServer;
        return this;
    }

    public String getConnectorHost() {
        return connectorHost;
    }

    public kafkaProperty setConnectorHost(String connectorHost) {
        this.connectorHost = connectorHost;
        return this;
    }

}
