# zyyj-cerebro 项目解读

## 项目概述
zyyj-cerebro 是一个基于 Spring Boot 和 Apache Thrift 构建的分布式服务系统，主要用于数据处理和分析。项目采用了微服务架构，包含多个模块，每个模块负责不同的功能。

## 服务构成

项目由以下几个主要模块组成：

1. **zyyj-cerebro-thrift**：
   - 定义了 Thrift 接口和数据结构
   - 作为服务间通信的契约层
   - 包含 QueryDSL 支持，用于 JPA 动态查询

2. **zyyj-cerebro-rpc-server**：
   - 基于 Spring Boot 的 RPC 服务实现
   - 提供核心业务逻辑处理
   - 使用 JPA 进行数据库操作
   - 集成 Redis 缓存
   - 支持多环境配置（本地、开发、生产）

3. **zyyj-cerebro-rest**：
   - 提供 RESTful API 接口
   - 集成 Swagger 文档
   - 支持 PDF 生成、Excel 处理等功能
   - 作为系统的对外服务入口

4. **zyyj-spark-thrift**：
   - Spark 相关的 Thrift 接口定义

5. **zyyj-spark-rpc-server**：
   - 基于 Spark 的数据处理服务
   - 集成了 Spark SQL、Spark Streaming、Spark MLlib 等组件
   - 支持 Delta Lake 数据湖
   - 提供大数据处理和分析能力

## 技术栈

- **基础框架**：Spring Boot 2.1.0
- **RPC 框架**：Apache Thrift
- **数据库**：MySQL 8.0
- **缓存**：Redis
- **ORM**：Spring Data JPA + QueryDSL
- **大数据处理**：Apache Spark 3.0.0
- **数据湖**：Delta Lake 0.8.0
- **API 文档**：Swagger 2.9.2
- **构建工具**：Maven

## 部署方法

项目支持三种环境部署：

1. **本地环境 (local)**：
   - 默认激活的环境配置
   - 适用于开发调试

2. **开发环境 (dev)**：
   - 用于测试和集成测试

3. **生产环境 (pro)**：
   - 用于正式部署

### 部署步骤：

1. **编译打包**：
   ```bash
   mvn clean package -P [环境标识] -DskipTests
   ```
   环境标识可以是 local、dev 或 pro

2. **服务启动**：
   - zyyj-cerebro-rest：`java -jar zyyj-cerebro-rest.jar`
   - zyyj-cerebro-rpc-server：`java -jar zyyj-cerebro-rpc-server.jar`
   - zyyj-spark-rpc-server：`java -jar zyyj-spark-rpc-server.jar`

## 系统架构特点

1. **微服务架构**：通过 Thrift 实现服务间通信
2. **多环境支持**：通过 Maven profiles 实现不同环境的配置管理
3. **大数据处理能力**：集成 Spark 生态系统，支持复杂数据分析
4. **RESTful API**：提供标准化的 HTTP 接口
5. **文档自动化**：集成 Swagger 实现 API 文档自动生成

这个项目是一个典型的企业级应用，结合了微服务架构和大数据处理能力，适用于需要处理和分析大量数据的业务场景。