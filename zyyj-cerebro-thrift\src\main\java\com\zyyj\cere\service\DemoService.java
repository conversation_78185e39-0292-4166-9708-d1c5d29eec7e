package com.zyyj.cere.service;


import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.zyyj.cere.pojo.dto.DatasourceDTO;
import com.zyyj.cere.pojo.entity.DatasourceEntity;
import com.zyyj.domain.exception.ApplicationException;

import java.util.List;
import java.util.Map;

/**
 * Demo类
 */
@ThriftService
public interface DemoService {

    @ThriftMethod
    Long getId(Long id) throws ApplicationException;

    @ThriftMethod
    String get() throws ApplicationException;

    @ThriftMethod
    void deleteKafkaConnector() throws ApplicationException;

}
