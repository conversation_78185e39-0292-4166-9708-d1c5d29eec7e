package com.zyyj.cere.repository;

import com.zyyj.cere.pojo.dto.ApiNameDTO;
import com.zyyj.cere.pojo.entity.BusinessEntity;
import com.zyyj.cere.pojo.entity.SubjectEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;


public interface BusinessRepository extends JpaRepository<BusinessEntity, Integer> {

    List<BusinessEntity> findAllBySubjectId(Integer subjectId);

    @Query(value = "SELECT * FROM business b  WHERE b.id != ?1 AND b.name = ?2", nativeQuery = true)
    List<BusinessEntity> existByIdAndName(Integer id, String name);

    List<BusinessEntity> findBySubjectIdIn(Integer[] subjectIds);

    List<BusinessEntity> findByName(String name);

    List<BusinessEntity> findByNameContaining(String name);


    List<BusinessEntity> findByIdIn(Integer[] ids);

}