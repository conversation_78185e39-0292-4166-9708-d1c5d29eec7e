package com.zyyj.cere.pojo.entity;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.sdk.processor.annotation.ThriftPaged;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/11/16 09:27
 */

@Setter
@ThriftStruct
@ThriftPaged
@NoArgsConstructor
@ToString
@Builder
@Entity
@Table(name = "api")
@EntityListeners(ApiEntity.class)
public class ApiEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, length = 11)
    private Integer id;

    @Column(name = "name", nullable = false, length = 20)
    private String name = "";

    @Column(name = "`describe`", length = 100)
    private String describe = "";

    @Column(name = "service_id", nullable = false, length = 11)
    private Integer serviceId;

    @Column(name = "method", nullable = false, length = 1)
    private Integer method;

    @Column(name = "return_type", nullable = false, length = 1)
    private Integer returnType;

    @Column(name = "subject_id", nullable = false, length = 11)
    private Integer subjectId;

    @Column(name = "business_id", nullable = false, length = 11)
    private Integer businessId;

    @Column(name = "table_name", nullable = false, length = 50)
    private String tableName = "";

    @Column(name = "table_id", nullable = false, length = 11)
    private Integer tableId;

    @Column(name = "data_json", nullable = false, length = 1024)
    private String dataJson = "";

    @Column(name = "status", nullable = false, length = 1)
    private Integer status;

    @ThriftConstructor
    public ApiEntity(Integer id, String name, String describe, Integer serviceId, Integer method, Integer returnType, Integer subjectId, Integer businessId, String tableName, Integer tableId, String dataJson, Integer status) {
        this.id = id;
        this.name = name;
        this.describe = describe;
        this.serviceId = serviceId;
        this.method = method;
        this.returnType = returnType;
        this.subjectId = subjectId;
        this.businessId = businessId;
        this.tableName = tableName;
        this.tableId = tableId;
        this.dataJson = dataJson;
        this.status = status;
    }

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public String getName() {
        return name;
    }

    @ThriftField(3)
    public String getDescribe() {
        return describe;
    }

    @ThriftField(4)
    public Integer getServiceId() {
        return serviceId;
    }

    @ThriftField(5)
    public Integer getMethod() {
        return method;
    }

    @ThriftField(6)
    public Integer getReturnType() {
        return returnType;
    }

    @ThriftField(7)
    public Integer getSubjectId() {
        return subjectId;
    }

    @ThriftField(8)
    public Integer getBusinessId() {
        return businessId;
    }

    @ThriftField(9)
    public String getTableName() {
        return tableName;
    }

    @ThriftField(10)
    public Integer getTableId() {
        return tableId;
    }

    @ThriftField(11)
    public String getDataJson() {
        return dataJson;
    }

    @ThriftField(12)
    public Integer getStatus() {
        return status;
    }
}
