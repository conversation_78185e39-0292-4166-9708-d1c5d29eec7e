package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.sdk.processor.annotation.ThriftPaged;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * <AUTHOR>
 * @date 2020/11/23 14:22
 */
@ThriftStruct
@NoArgsConstructor
@ThriftPaged
@Setter
@ToString
@Builder
public class ApiResponseDTO {
    private Integer id;
    private String name;
    private String describe;
    private String apiUrl;
    private Integer status;

    @ThriftConstructor
    public ApiResponseDTO(Integer id, String name, String describe, String apiUrl, Integer status) {
        this.id = id;
        this.name = name;
        this.describe = describe;
        this.apiUrl = apiUrl;
        this.status = status;
    }

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public String getName() {
        return name;
    }

    @ThriftField(3)
    public String getDescribe() {
        return describe;
    }

    @ThriftField(4)
    public String getApiUrl() {
        return apiUrl;
    }

    @ThriftField(5)
    public Integer getStatus() {
        return status;
    }
}
