package com.zyyj.cere.pojo.entity;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.sdk.processor.annotation.ThriftPaged;
import io.swagger.annotations.ApiModel;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 主题域
 */

@Setter
@ThriftStruct
@ThriftPaged
@NoArgsConstructor
@ToString
@Builder
@ApiModel(value = "SubjectEntity", description = "数据源")
@Entity
@Table(name = "subject")
@EntityListeners(SubjectEntity.class)
public class SubjectEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, length = 11)
    private Integer id;

    /**
     * 名称
     */
    @NotEmpty
    @Column(name = "name", nullable = false, length = 20)
    private String name = "";

    /**
     * 1 正常 2 停用
     */
    @Column(name = "status", nullable = false, length = 1)
    private Integer status = 1;

    @ThriftConstructor
    public SubjectEntity(Integer id, @NotEmpty String name, Integer status) {
        this.id = id;
        this.name = name;
        this.status = status;
    }

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public String getName() {
        return name;
    }

    @ThriftField(3)
    public Integer getStatus() {
        return status;
    }
}
