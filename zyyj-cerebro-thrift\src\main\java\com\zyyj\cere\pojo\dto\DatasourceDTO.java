package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.sdk.processor.annotation.ThriftPaged;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * 数据域返回对象
 */
@ThriftStruct
@ThriftPaged
@Setter
@NoArgsConstructor
@ToString
@Builder
@ApiModel(value = "DatasourceQuery", description = "数据源查询")
public class DatasourceDTO {

    @ApiModelProperty(name = "id", value = "ID")
    private Long id;

    @ApiModelProperty(name = "typeId", value = "类型id")
    private Long typeId;

    @ApiModelProperty(name = "typeName", value = "类型名称")
    private String typeName;

    @ApiModelProperty(name = "name", value = "数据源名称")
    private String name;

    @ApiModelProperty(name = "describe", value = "描述")
    private String describe;

    @ApiModelProperty(name = "host", value = "连接地址")
    private String host = "";


    @ApiModelProperty(name = "username", value = "用户名")
    private String username = "";


    @ApiModelProperty(name = "password", value = "密码")
    private String password = "";


    @ApiModelProperty(name = "database", value = "数据库名称")
    private String database = "";

    @ThriftConstructor
    public DatasourceDTO(Long id, Long typeId, String typeName, String name, String describe, String host, String username, String password, String database) {
        this.id = id;
        this.typeId = typeId;
        this.typeName = typeName;
        this.name = name;
        this.describe = describe;
        this.host = host;
        this.username = username;
        this.password = password;
        this.database = database;
    }

    @ThriftField(1)
    public Long getId() {
        return id;
    }

    @ThriftField(2)
    public Long getTypeId() {
        return typeId;
    }

    @ThriftField(3)
    public String getTypeName() {
        return typeName;
    }

    @ThriftField(4)
    public String getName() {
        return name;
    }

    @ThriftField(5)
    public String getDescribe() {
        return describe;
    }

    @ThriftField(6)
    public String getHost() {
        return host;
    }

    @ThriftField(7)
    public String getUsername() {
        return username;
    }

    @ThriftField(8)
    public String getPassword() {
        return password;
    }

    @ThriftField(9)
    public String getDatabase() {
        return database;
    }
}
