package com.zyyj.utils.excel;

import java.util.ArrayList;
import java.util.List;

public class ZYTableData {

    int index;
    String name;
    List<List<String>> data;

    public ZYTableData(){
        this.index = 0;
        this.name = "";
        this.data = new ArrayList<List<String>>();
    }

    public int getIndex() {
        return index;
    }

    public ZYTableData setIndex(int index) {
        this.index = index;
        return this;
    }

    public String getName() {
        return name;
    }

    public ZYTableData setName(String name) {
        this.name = name;
        return this;
    }

    public List<List<String>> getData() {
        return data;
    }

    public ZYTableData setData(List<List<String>> data) {
        this.data = data;
        return this;
    }

    // 增加一行的数据
    public void addRowData(List<String> data){
        this.data.add(data);
    }
}
