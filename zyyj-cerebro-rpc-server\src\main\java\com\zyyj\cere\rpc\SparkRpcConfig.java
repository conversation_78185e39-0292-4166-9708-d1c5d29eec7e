package com.zyyj.cere.rpc;

import com.zyyj.rpc.thrift.client.AbstractThriftClientConfiguration;
import com.zyyj.spark.service.DeltaService;
import com.zyyj.spark.service.DeltaTunnelService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SparkRpcConfig extends AbstractThriftClientConfiguration {
    @Value("${service.spark}")
    private String serverName;

    @Override
    protected String getDiscoveryName() {
        return serverName;
    }

    @Bean
    public DeltaService deltaService() {
        return getClient(DeltaService.class);
    }

    @Bean
    public DeltaTunnelService deltaTunnelService() { return getClient(DeltaTunnelService.class); };
}
