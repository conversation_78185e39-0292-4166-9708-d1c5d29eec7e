package com.zyyj.utils.excel;

import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvParser;


import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;



public class ZYCSVUtil {

    public static ZYCSVUtil builder(){
        return new ZYCSVUtil();
    }

    public ZYTableData getData(InputStream inputStream) throws Exception {
        CsvMapper mapper = new CsvMapper();
        mapper.enable(CsvParser.Feature.WRAP_AS_ARRAY);
        String[][] values =  mapper.readerFor(String[][].class).readValue(inputStream);

        // 返回list
        ZYTableData data = new ZYTableData();

        List<List<String>> datas = new ArrayList<List<String>>();
        for (String[] value: values) {
            List rows = new ArrayList<String >();
            for (String v: value) {
                rows.add(v);
            }
            data.addRowData(rows);
        }

        data.data = datas;
        return data;
    }
}
