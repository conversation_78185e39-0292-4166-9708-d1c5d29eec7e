package com.zyyj.cere.service;

import com.zyyj.cere.CerebroRPCServerApplication;
import com.zyyj.cere.pojo.kafka.KafkaConnectorResp;
import com.zyyj.cere.pojo.kafka.KCMySqlSourceConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CerebroRPCServerApplication.class)
public class KafkaServiceImplTest {



    @Autowired
    KafkaServiceImpl kafkaService;

    @Test
    public void testProperties() {

    }

    @Test
    public void createMySqlSourceConnector() {
        System.out.println(kafkaService);
        // 配置 kafka source connect 参数
        KCMySqlSourceConfig config = new KCMySqlSourceConfig();
        //MARK: 3306 默认mysql端口
        config.setHostName("**************");
        config.setPort("3306");
        config.setUser("test");
        config.setPassword("123456");
        // 配置 库
        config.setName("mysql_source_1");
        config.setDbName("k12_platform");
        config.setServerId("1111");
        config.setServerName("1111");
        config.setTableNames(new String[]{"organ"});
        KafkaConnectorResp resp = kafkaService.CreateMySqlSourceConnector(config);
        System.out.println(resp.getConfig());
    }


    private String getTimeStamp(){
        long millis = System.currentTimeMillis();
        return String.valueOf(millis);
    }
}