package com.zyyj.cere.utils;

import com.zyyj.domain.pagination.Paging;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PageUtils {
    /**
     * 开始分页
     * @param list
     * @return
     */
    public static Map getPageList(List list, Paging paging) {
        int pageSize = paging.getPageSize();
        int pageNum = paging.getPageNumber();
        Integer count = list.size(); // 记录总数
        Integer pageCount = count % pageSize==0 ? count / pageSize : count / pageSize + 1;//总页数
        List pageList = new ArrayList();

        if(list.size()>0){
            int fromIndex = 0; // 开始索引
            int toIndex = 0; // 结束索引

            if (pageNum != pageCount) {
                fromIndex = (pageNum - 1) * pageSize;
                toIndex = fromIndex + pageSize;
            } else {
                fromIndex = (pageNum - 1) * pageSize;
                toIndex = count;
            }
            pageList = list.subList(fromIndex, toIndex);
        }
        Map map = new HashMap();
        map.put("pageSize",pageSize);
        map.put("pageNum",pageNum);
        map.put("count",count);
        map.put("pageCount",pageCount);
        map.put("list",pageList);
        return map;
    }
}
