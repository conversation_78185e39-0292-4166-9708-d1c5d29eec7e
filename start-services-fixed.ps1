# =============================================================================
# 启动所有服务 - 使用***************远程集群
# =============================================================================

Write-Host "=== 启动所有服务 (使用***************远程集群) ===" -ForegroundColor Cyan
Write-Host "时间: $(Get-Date)" -ForegroundColor Green
Write-Host ""

# 创建日志目录
if (-not (Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs" -Force
    Write-Host "创建日志目录: logs" -ForegroundColor Green
}

# 设置Java环境
$env:JAVA_HOME = "C:\Program Files\Java\jdk1.8.0_141"
Write-Host "Java环境: $env:JAVA_HOME" -ForegroundColor Green
Write-Host ""

# 1. 启动 Spark RPC 服务
Write-Host "启动 Spark RPC 服务..." -ForegroundColor Yellow
$sparkArgs = @(
    "-Xms512m"
    "-Xmx1g"
    "-Dserver.port=8081"
    "-Dspring.profiles.active=dev"
    "-Dspring.application.name=ZYYJ-SPARK-THRIFT"
    "-Deureka.instance.appname=ZYYJ-SPARK-THRIFT"
    "-Deureka.instance.non-secure-port=8081"
    "-Deureka.client.serviceUrl.defaultZone=******************************************/eureka/"
    "-Dspring.main.allow-bean-definition-overriding=true"
    "-Dspark.master=spark://***************:7077"
    "-Dspark.appName=ZYYJ-SPARK-RPC-SERVER"
    "-Dspark.warehouseDir=hdfs://***************:9000/spark-warehouse"
    "-Dspark.metastoreUris=thrift://***************:9083"
    "-Dspark.driver=localhost"
    "-Dspark.driver.bindAddress=0.0.0.0"
    "-Dzyyj.rpc.thrift.server.listen_port=9009"
    "-jar"
    "E:\dev\project\zyyj-cerebro\zyyj-spark-rpc-server\target\zyyj-spark-rpc-server.jar"
)

$sparkProcess = Start-Process -FilePath "$env:JAVA_HOME\bin\java" -ArgumentList $sparkArgs -RedirectStandardOutput "logs\spark-rpc.log" -RedirectStandardError "logs\spark-rpc-error.log" -PassThru

Write-Host "Spark RPC 进程ID: $($sparkProcess.Id)" -ForegroundColor Green
Write-Host "日志文件: logs\spark-rpc.log" -ForegroundColor Gray
Write-Host ""

# 等待30秒
Write-Host "等待Spark RPC服务启动 (30秒)..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# 2. 启动 Cerebro RPC 服务
Write-Host "启动 Cerebro RPC 服务..." -ForegroundColor Yellow
$cerebroArgs = @(
    "-Dspring.profiles.active=dev"
    "-Dspring.main.allow-bean-definition-overriding=true"
    "-Dserver.port=9007"
    "-Dzyyj.rpc.thrift.server.listen_port=9006"
    "-Deureka.client.serviceUrl.defaultZone=******************************************/eureka/"
    "-Dspring.config.location=file:E:\dev\project\zyyj-cerebro\zyyj-cerebro-rpc-server\src\main\resources\dev\application.yml"
    "-jar"
    "E:\dev\project\zyyj-cerebro\bak1.0\zyyj-cerebro-rpc-server\zyyj-cerebro-rpc-server-bin\zyyj-cerebro-rpc-server\zyyj-cerebro-rpc-server.jar"
)

$cerebroProcess = Start-Process -FilePath "$env:JAVA_HOME\bin\java" -ArgumentList $cerebroArgs -RedirectStandardOutput "logs\cerebro-rpc.log" -RedirectStandardError "logs\cerebro-rpc-error.log" -PassThru

Write-Host "Cerebro RPC 进程ID: $($cerebroProcess.Id)" -ForegroundColor Green
Write-Host "日志文件: logs\cerebro-rpc.log" -ForegroundColor Gray
Write-Host ""

# 等待20秒
Write-Host "等待Cerebro RPC服务启动 (20秒)..." -ForegroundColor Yellow
Start-Sleep -Seconds 20

# 3. 启动 REST API 服务
Write-Host "启动 REST API 服务..." -ForegroundColor Yellow
$restArgs = @(
    "-Dserver.port=8005"
    "-Dspring.profiles.active=dev"
    "-Dspring.application.name=ZYYJ-CEREBRO-REST"
    "-Deureka.instance.appname=ZYYJ-CEREBRO-REST"
    "-Deureka.instance.non-secure-port=8005"
    "-Dspring.main.allow-bean-definition-overriding=true"
    "-Deureka.client.serviceUrl.defaultZone=******************************************/eureka/"
    "-Dservice.cerebro=ZYYJ-CEREBRO-THRIFT"
    "-Dscan.package=com.zyyj.controller"
    "-jar"
    "E:\dev\project\zyyj-cerebro\zyyj-cerebro-rest\target\zyyj-cerebro-rest.jar"
)

$restProcess = Start-Process -FilePath "$env:JAVA_HOME\bin\java" -ArgumentList $restArgs -RedirectStandardOutput "logs\rest-api.log" -RedirectStandardError "logs\rest-api-error.log" -PassThru

Write-Host "REST API 进程ID: $($restProcess.Id)" -ForegroundColor Green
Write-Host "日志文件: logs\rest-api.log" -ForegroundColor Gray
Write-Host ""

# 等待15秒
Write-Host "等待REST API服务启动 (15秒)..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# 4. 检查服务状态
Write-Host "检查服务状态..." -ForegroundColor Yellow
Write-Host ""

# 检查端口
$ports = @(8081, 9009, 9007, 9006, 8005)
$portNames = @("Spark RPC HTTP", "Spark RPC Thrift", "Cerebro RPC HTTP", "Cerebro RPC Thrift", "REST API")

for ($i = 0; $i -lt $ports.Count; $i++) {
    $port = $ports[$i]
    $name = $portNames[$i]
    $listening = netstat -ano | findstr ":$port" | findstr "LISTENING"
    if ($listening) {
        Write-Host "$name (端口 $port) - 正在监听" -ForegroundColor Green
    } else {
        Write-Host "$name (端口 $port) - 未监听" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== 启动完成 ===" -ForegroundColor Cyan
Write-Host ""
Write-Host "服务访问地址:"
Write-Host "  REST API: http://localhost:8005"
Write-Host "  Spark RPC HTTP: http://localhost:8081"
Write-Host "  Cerebro RPC HTTP: http://localhost:9007"
Write-Host ""
Write-Host "远程集群Web界面:"
Write-Host "  Spark Master: http://***************:8080"
Write-Host "  HDFS: http://***************:9870"
Write-Host "  YARN: http://***************:8088"
Write-Host ""
Write-Host "进程ID:"
Write-Host "  Spark RPC: $($sparkProcess.Id)"
Write-Host "  Cerebro RPC: $($cerebroProcess.Id)"
Write-Host "  REST API: $($restProcess.Id)"
Write-Host ""
