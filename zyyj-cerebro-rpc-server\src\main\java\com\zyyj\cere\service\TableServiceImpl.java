package com.zyyj.cere.service;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.zyyj.cere.constants.ErrorConstants;
import com.zyyj.cere.pojo.body.TableAddBody;
import com.zyyj.cere.pojo.dto.*;
import com.zyyj.cere.pojo.entity.QTableEntity;
import com.zyyj.cere.pojo.entity.TableEntity;
import com.zyyj.cere.pojo.entity.TableModelingEntity;
import com.zyyj.cere.pojo.entity.TableTunnelEntity;
import com.zyyj.cere.pojo.resp.TableTunnelUpdateInfoResp;
import com.zyyj.cere.repository.TableModelingRepository;
import com.zyyj.cere.repository.TableRepository;
import com.zyyj.cere.repository.TableTunnelRepository;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.rpc.thrift.server.ThriftServiceHandler;
import com.zyyj.spark.service.DeltaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 数据表实现类
 */
@Slf4j
@Service
@Component
@ThriftServiceHandler
public class TableServiceImpl implements TableService {
    @Autowired
    JPAQueryFactory queryFactory;
    @Autowired
    TableRepository tableRepository;
    @Autowired
    TableModelingRepository tableModelingRepository;
    @Autowired
    TableTunnelRepository tableTunnelRepository;

    @Autowired
    DeltaService deltaService;

    @Override
    public List<TableListDTO> getTableList(String s, String name, Long bus) {
        QTableEntity tableEntity = QTableEntity.tableEntity;
        // 动态查询
        BooleanBuilder builder = new BooleanBuilder();
        /*
        条件查询
         */
        if (!StringUtils.isEmpty(bus)) {
            builder.and(tableEntity.businessId.eq(bus));
        }

        if (!StringUtils.isEmpty(s)) {
            List<Byte> l = new ArrayList<Byte>();
            for (String string : s.split(",")) {
                l.add(Byte.valueOf(string));
            }
            builder.and(tableEntity.status.in(l));
        }

        if (!StringUtils.isEmpty(name)) {
            builder.and(tableEntity.name.like("%" + name + "%"));
        }

        return queryFactory.select(
                Projections.bean(TableListDTO.class, tableEntity.id, tableEntity.name, tableEntity.tableName, tableEntity.typeId, tableEntity.status, tableEntity.source, tableEntity.tunnelId, tableEntity.modelingId)
        )
                .from(tableEntity)
                .where(builder)
                .fetch();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TableEntity setTableByManual(TableAddBody tb, List<List<String>> data) {
        //判断表名称是否重复
        if (tableRepository.existsByTableName(tb.getTableName())) throw new ApplicationException(ErrorConstants.TABLE_NAME_ERROR);
        //json转对象
        List<TableFieldDTO> lt = new Gson().fromJson(tb.getFieldJson(), new TypeToken<List<TableFieldDTO>>() {
        }.getType());
        //初始化建表类
        TableCreateDTO tableCreateDTO = TableCreateDTO
                .builder()
                .tableName(tb.getTableName())
                .field(new ArrayList<>())
                .comment(tb.getName())
                .build();
        lt.forEach(tableCreateDTO::setFieldArr);

        TableEntity t = TableEntity.of(tb);
        //添加物理表数据入数据库
        t = tableRepository.save(t);
        //传递给spark rpc创建hdfs中delta格式的物理表
        deltaService.createTable(tableCreateDTO.getSqlForDelta());
        //当data有数据时 插入新建的表中
        if (data.size() > 1) {
            Map<String, Integer> mapField = new HashMap<>();
            for (TableFieldDTO value : lt) {
                mapField.put(value.getField(), 1);
                if (value.getType().equals("long")) {
                    mapField.put(value.getField(), 2);
                }
                if (value.getType().equals("date")) {
                    mapField.put(value.getField(), 2);
                }
            }
            List<String> arrData = new ArrayList<>();
            StringBuilder str = new StringBuilder();
            for (List<String> val : data) {
                if (str.length() == 0) {
                    str = new StringBuilder("{");
                    for (String v : val) {
                        if (mapField.get(v) == 2) {  //数字
                            str.append("\"").append(v).append("\":%s,");
                        } else if (mapField.get(v) == 1) {
                            str.append("\"").append(v).append("\":\"%s\",");
                        } else {  //key不存在  直接报错
                            throw new ApplicationException(ErrorConstants.EXCEL_ERROR);
                        }
                    }
                    str = new StringBuilder(str.substring(0, str.toString().length() - 1));
                    str.append("}");
                    continue;
                }
                arrData.add(String.format(str.toString(), val.toArray()));
            }
            String jsonData = ArrayUtils.toString(arrData, ",");
            deltaService.insertTableByList(tb.getTableName(), jsonData);
        }
        return t;
    }

    @Override
    public List<String> getTableData(String tableName) {
        return deltaService.getTableList(tableName);
    }

    @Override
    public List<TableStructDTO> getTableStruct(Long id) {
        //获取表信息
        TableEntity t = getTableInfo(id);
        // 未发布和待审核
        if (t.getStatus() == 1 || t.getStatus() == 2) {
            return this.getTableStructByModeling(t);
        }
        List<String> l = deltaService.getTableStruct(t.getTableName());
        return TableStructDTO.getList(l);
    }

    @Override
    public List<TableStructDTO> getTablePreview(String tableName) {
        TableEntity t = tableRepository.findOneByTableName(tableName);
        // 未发布和待审核
        if (t != null && (t.getStatus() == 1 || t.getStatus() == 2)) {
            return this.getTableStructByModeling(t);
        }
        List<String> l = deltaService.getTableStruct(tableName);
        return TableStructDTO.getList(l);
    }

    @Transactional
    @Override
    public void renameTable(TableEntity t) {
        tableRepository.updateNameById(t.getName(), t.getId());
    }

    @Transactional
    @Override
    public void moveTable(TableEntity t) {
        tableRepository.updateBusinessIdById(t.getBusinessId(), t.getId());
    }

    @Transactional
    @Override
    public void removeTable(Long id) {
        //获取表信息
        TableEntity t = getTableInfo(id);
        if (t.getTypeId() == 2) {//主题表删除建模信息
            tableModelingRepository.deleteById(t.getModelingId().longValue());
        }
        //删除表信息
        tableRepository.deleteById(id);
        //删除Delta数据表或view
        if (t.getTypeId() == 2 && t.getStatus() == 3) {//删除主题表
            deltaService.removeTable(t.getTableName(), true);
            return;
        }
        if (t.getTypeId() == 1) {//删除物理表
            deltaService.removeTable(t.getTableName(), false);
        }

    }

    @Transactional
    @Override
    public void operationTable(int id, int status) {
        //获取表信息
        TableEntity tableEntity = getTableInfo(((long) id));
        tableRepository.updateStatusById((byte) status, (long) id);
        System.out.println("=====表状态更新成功====");
        System.out.println("=====status====" + status);
        if (status == 3) {//审核通过创建主题表
            System.out.println("=====开始往数据湖创建view====");
            System.out.println("=====表名====" + tableEntity.getTableName() + "===sql=====" + tableModelingRepository.getSql(new Long(tableEntity.getModelingId())));
            deltaService.createView(tableEntity.getTableName(), tableModelingRepository.getSql(new Long(tableEntity.getModelingId())));
            System.out.println("=====数据湖创建view成功====");
        }

    }

    @Override
    public Map<String, String> previewTable(Long id, String order, Integer page, Integer size) {
        TableEntity t = getTableInfo(id);
        // 未发布和待审核
        if (t.getStatus() == 1 || t.getStatus() == 2) {
            TableModelingEntity tableModelingEntity = getTableModelingBy(t);
            if (tableModelingEntity != null) {
                return deltaService.previewBySql(tableModelingEntity.getDataSql(), order, page, size);
            }
        }
        return deltaService.preview(t.getTableName(), order, page, size);
    }

    @Override
    public TableTunnelUpdateInfoResp getUpdateInfo(Long id) throws ApplicationException {
        // 查询表的通道是否存在
        Optional<TableEntity> optionalTableEntity = tableRepository.findById(id);
        TableTunnelUpdateInfoResp resp = new TableTunnelUpdateInfoResp();

        if (optionalTableEntity.isPresent()){
            TableEntity tableEntity = optionalTableEntity.get();
            Integer tunnelId = tableEntity.getTunnelId();
            // 存在管道
            if (tunnelId>0){

                Optional<TableTunnelEntity> optionalTableTunnelEntity = tableTunnelRepository.findById(Long.valueOf(tunnelId));
                if (optionalTableEntity.isPresent()){
                    TableTunnelEntity tableTunnelEntity = optionalTableTunnelEntity.get();
                    // 没有删除
                    if (!tableTunnelEntity.isDelete()){
                        resp.setName(tableEntity.getName()+"的集成管道");
                        resp.setSync_type(tableEntity.getSyncType());
                        resp.setSync_type_name(tableEntity.getSyncTypeName());
                        resp.setStatus(tableTunnelEntity.getStatus());
                        resp.setStatus_name(tableTunnelEntity.getStatusName());
                        resp.setTunnel_id(tableTunnelEntity.getId());
                        System.out.println(resp.toString());
                        return resp;
                    }
                }
            }
        }
        throw new ApplicationException("");
    }



    private TableEntity getTableInfo(Long id) {
        //获取表信息
        Optional<TableEntity> optional = tableRepository.findById(id);
        if (!optional.isPresent()) throw new ApplicationException(ErrorConstants.TABLE_EXIST_ERROR);
        return optional.get();
    }

    private TableModelingEntity getTableModelingBy(TableEntity t) {
        // 不是建模
        if (t.getModelingId() == 0) {
            return null;
        }
        // 查询table_modeling
        Optional<TableModelingEntity> optionalTableModelingEntity = tableModelingRepository.findById(t.getModelingId().longValue());
        // 空
        if (!optionalTableModelingEntity.isPresent()) {
            return null;
        }
        return optionalTableModelingEntity.get();
    }

    private List<TableStructDTO> getTableStructByModeling(TableEntity t) {
        TableModelingEntity tableModelingEntity = getTableModelingBy(t);
        if (tableModelingEntity == null) {
            return new ArrayList<>();
        }
        String sql = tableModelingEntity.getDataSql();
        List<String> l = deltaService.getTableStructBySql(sql);
        return TableStructDTO.getList(l);
    }
}
