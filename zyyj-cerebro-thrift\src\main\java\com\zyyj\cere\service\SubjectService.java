package com.zyyj.cere.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.zyyj.cere.pojo.dto.ApiNameDTO;
import com.zyyj.cere.pojo.dto.FilterBusinessAndTableDTO;
import com.zyyj.cere.pojo.dto.SubjectBusinessDTO;
import com.zyyj.cere.pojo.dto.SubjectDTO;
import com.zyyj.cere.pojo.entity.SubjectEntity;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2020/9/18 16:00
 */
@ThriftService
public interface SubjectService {

    /**
     * @Description: 主题域列表左侧导航
     * @Param:
     * @return:
     * @Author: bravelee
     * @Date: 2020/9/23
     */
    @ThriftMethod
    Map<String, List<SubjectBusinessDTO>> getSubjectNavigateList();

    /** 
     * @Description: 搜索业务集/数据表
     * @Param: [name]
     * @return: java.util.Map<java.lang.String,java.util.List<com.zyyj.cere.pojo.dto.SubjectBusinessDTO>>
     * @Author: bravelee
     * @Date: 2020/11/26
     */
    @ThriftMethod
    Map<String, List<FilterBusinessAndTableDTO>> filterBusinessAndTable(String name,Integer isMinJie);

    /**
     * @Description: 主题域列表
     * @Param:
     * @return:
     * @Author: bravelee
     * @Date: 2020/9/23
     */
    @ThriftMethod
    Map<String, List<SubjectDTO>> getSubjectList();

    /**
     * @Description: 添加主题域
     * @Param: [subjectEntity]
     * @return:[ergMsg]
     * @Author: bravelee
     * @Date: 2020/9/23
     */
    @ThriftMethod
    String addSubject(SubjectEntity subjectEntity);

    /**
     * @Description:编辑主题域
     * @Param: [subjectEntity]
     * @return: [ergMsg]
     * @Author: bravelee
     * @Date: 2020/9/22
     */
    @ThriftMethod
    String editSubject(SubjectEntity subjectEntity);


    /**
     * @Description: 删除主题域
     * @Param: [subjectEntity]
     * @return: [ergMsg]
     * @Author: bravelee
     * @Date: 2020/9/23
     */
    @ThriftMethod
    String delSubject(Integer id);
}
