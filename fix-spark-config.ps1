# =============================================================================
# 修复Spark配置脚本 - 使用远程Spark集群避免Windows兼容性问题
# =============================================================================

param(
    [string]$Mode = "remote",  # remote 或 local
    [switch]$Help
)

if ($Help) {
    Write-Host "用法: .\fix-spark-config.ps1 [-Mode <remote|local>]"
    Write-Host ""
    Write-Host "参数:"
    Write-Host "  -Mode    配置模式 (remote: 使用远程集群, local: 修复本地环境)"
    Write-Host "  -Help    显示帮助信息"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  .\fix-spark-config.ps1 -Mode remote   # 使用远程Spark集群"
    Write-Host "  .\fix-spark-config.ps1 -Mode local    # 修复本地Hadoop环境"
    exit 0
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colors = @{
        "Red" = "Red"
        "Green" = "Green" 
        "Yellow" = "Yellow"
        "Blue" = "Blue"
        "White" = "White"
    }
    
    Write-Host $Message -ForegroundColor $colors[$Color]
}

function Log-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Green"
}

function Log-Warn {
    param([string]$Message)
    Write-ColorOutput "[WARN] $Message" "Yellow"
}

function Log-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

function Log-Step {
    param([string]$Message)
    Write-ColorOutput "[STEP] $Message" "Blue"
}

# 停止现有Spark服务
function Stop-SparkService {
    Log-Step "停止现有Spark服务..."
    
    try {
        taskkill /F /IM java.exe 2>$null
        Start-Sleep -Seconds 3
        Log-Info "服务已停止"
    } catch {
        Log-Warn "停止服务时出现警告: $($_.Exception.Message)"
    }
}

# 配置远程Spark集群模式
function Configure-RemoteMode {
    Log-Step "配置远程Spark集群模式..."
    
    # 创建远程模式启动脚本
    $remoteStartScript = @"
Write-Host "🚀 启动 Spark RPC 服务 (远程集群模式)..."
`$env:JAVA_HOME = "C:\Program Files\Java\jdk1.8.0_141"

# 使用远程Spark集群的配置
java ``
    -Xms512m ``
    -Xmx1g ``
    -Dserver.port=8081 ``
    -Dspring.profiles.active=dev ``
    -Dspring.application.name=ZYYJ-SPARK-THRIFT ``
    -Deureka.instance.appname=ZYYJ-SPARK-THRIFT ``
    -Deureka.instance.non-secure-port=8081 ``
    -Deureka.client.serviceUrl.defaultZone=******************************************/eureka/ ``
    -Dspring.main.allow-bean-definition-overriding=true ``
    -jar "E:\dev\project\zyyj-cerebro\zyyj-spark-rpc-server\target\zyyj-spark-rpc-server.jar"
"@
    
    $remoteStartScript | Out-File -FilePath "start-spark-remote.ps1" -Encoding UTF8
    Log-Info "远程模式启动脚本已创建: start-spark-remote.ps1"
}

# 配置本地模式（修复Hadoop环境）
function Configure-LocalMode {
    Log-Step "配置本地模式（修复Hadoop环境）..."
    
    # 检查Hadoop目录
    if (-not (Test-Path "hadoop\bin\winutils.exe")) {
        Log-Error "缺少 winutils.exe，需要下载Windows Hadoop工具"
        Download-HadoopUtils
    }
    
    if (-not (Test-Path "hadoop\bin\hadoop.dll")) {
        Log-Error "缺少 hadoop.dll，需要下载Windows Hadoop库"
        Download-HadoopUtils
    }
    
    # 创建本地模式启动脚本
    $localStartScript = @"
Write-Host "🚀 启动 Spark RPC 服务 (本地模式 - 修复版)..."
`$env:JAVA_HOME = "C:\Program Files\Java\jdk1.8.0_141"
`$env:HADOOP_HOME = "E:\dev\project\zyyj-cerebro\hadoop"
`$env:PATH = "`$env:HADOOP_HOME\bin;" + `$env:PATH

# 创建必要的目录
if (-not (Test-Path "warehouse")) { New-Item -ItemType Directory -Name "warehouse" }
if (-not (Test-Path "logs")) { New-Item -ItemType Directory -Name "logs" }

# 设置权限（Windows特殊处理）
& "`$env:HADOOP_HOME\bin\winutils.exe" chmod 777 warehouse 2>`$null

java ``
    -Xms512m ``
    -Xmx1g ``
    -Dserver.port=8081 ``
    -Dspring.profiles.active=dev ``
    -Dspring.application.name=ZYYJ-SPARK-THRIFT ``
    -Deureka.instance.appname=ZYYJ-SPARK-THRIFT ``
    -Deureka.instance.non-secure-port=8081 ``
    -Deureka.client.serviceUrl.defaultZone=******************************************/eureka/ ``
    -Dspring.main.allow-bean-definition-overriding=true ``
    -Dspark.metastoreUris=thrift://***************:9083 ``
    -Dspark.warehouseDir=file:///E:/dev/project/zyyj-cerebro/warehouse/ ``
    -Dspark.master=local[*] ``
    -Dspark.appName=ZYYJ-SPARK-RPC-SERVER ``
    -Dspark.driver=localhost ``
    -Dspark.driver.bindAddress=0.0.0.0 ``
    -Dhadoop.home.dir=E:\dev\project\zyyj-cerebro\hadoop ``
    -Dzyyj.rpc.thrift.server.listen_port=9009 ``
    -Dspark.sql.warehouse.dir=file:///E:/dev/project/zyyj-cerebro/warehouse/ ``
    -Dspark.hadoop.fs.file.impl=org.apache.hadoop.fs.LocalFileSystem ``
    -Dspark.hadoop.fs.file.impl.disable.cache=true ``
    -jar "E:\dev\project\zyyj-cerebro\zyyj-spark-rpc-server\target\zyyj-spark-rpc-server.jar"
"@
    
    $localStartScript | Out-File -FilePath "start-spark-local.ps1" -Encoding UTF8
    Log-Info "本地模式启动脚本已创建: start-spark-local.ps1"
}

# 下载Hadoop Windows工具
function Download-HadoopUtils {
    Log-Step "下载Hadoop Windows工具..."
    
    try {
        # 创建目录
        if (-not (Test-Path "hadoop\bin")) {
            New-Item -ItemType Directory -Path "hadoop\bin" -Force
        }
        
        # 下载winutils.exe
        $winutilsUrl = "https://github.com/steveloughran/winutils/raw/master/hadoop-3.0.0/bin/winutils.exe"
        $winutilsPath = "hadoop\bin\winutils.exe"
        
        if (-not (Test-Path $winutilsPath)) {
            Log-Info "下载 winutils.exe..."
            Invoke-WebRequest -Uri $winutilsUrl -OutFile $winutilsPath
        }
        
        # 下载hadoop.dll
        $hadoopDllUrl = "https://github.com/steveloughran/winutils/raw/master/hadoop-3.0.0/bin/hadoop.dll"
        $hadoopDllPath = "hadoop\bin\hadoop.dll"
        
        if (-not (Test-Path $hadoopDllPath)) {
            Log-Info "下载 hadoop.dll..."
            Invoke-WebRequest -Uri $hadoopDllUrl -OutFile $hadoopDllPath
        }
        
        Log-Info "Hadoop Windows工具下载完成"
    } catch {
        Log-Error "下载失败: $($_.Exception.Message)"
        Log-Info "请手动下载以下文件到 hadoop\bin\ 目录:"
        Log-Info "1. winutils.exe: https://github.com/steveloughran/winutils/raw/master/hadoop-3.0.0/bin/winutils.exe"
        Log-Info "2. hadoop.dll: https://github.com/steveloughran/winutils/raw/master/hadoop-3.0.0/bin/hadoop.dll"
    }
}

# 测试配置
function Test-Configuration {
    param([string]$TestMode)
    
    Log-Step "测试配置..."
    
    if ($TestMode -eq "remote") {
        # 测试远程集群连接
        Log-Info "测试远程Spark集群连接..."
        $sparkMasterTest = Test-NetConnection -ComputerName "***************" -Port 7077 -InformationLevel Quiet
        if ($sparkMasterTest) {
            Log-Info "✅ Spark Master连接正常"
        } else {
            Log-Warn "❌ Spark Master连接失败，可能需要启动集群"
        }
        
        $hdfsTest = Test-NetConnection -ComputerName "***************" -Port 9000 -InformationLevel Quiet
        if ($hdfsTest) {
            Log-Info "✅ HDFS连接正常"
        } else {
            Log-Warn "❌ HDFS连接失败"
        }
    } else {
        # 测试本地环境
        Log-Info "测试本地Hadoop环境..."
        if (Test-Path "hadoop\bin\winutils.exe") {
            Log-Info "✅ winutils.exe 存在"
        } else {
            Log-Error "❌ winutils.exe 缺失"
        }
        
        if (Test-Path "hadoop\bin\hadoop.dll") {
            Log-Info "✅ hadoop.dll 存在"
        } else {
            Log-Error "❌ hadoop.dll 缺失"
        }
    }
}

# 启动服务
function Start-SparkService {
    param([string]$StartMode)
    
    Log-Step "启动Spark服务..."
    
    if ($StartMode -eq "remote") {
        Log-Info "使用远程集群模式启动..."
        & ".\start-spark-remote.ps1"
    } else {
        Log-Info "使用本地模式启动..."
        & ".\start-spark-local.ps1"
    }
}

# 主函数
function Main {
    Log-Info "Spark配置修复工具"
    Log-Info "模式: $Mode"
    
    # 停止现有服务
    Stop-SparkService
    
    # 根据模式配置
    if ($Mode -eq "remote") {
        Configure-RemoteMode
        Test-Configuration -TestMode "remote"
        
        Log-Info ""
        Log-Info "远程集群模式配置完成！"
        Log-Info "启动命令: .\start-spark-remote.ps1"
        Log-Info ""
        Log-Info "优势:"
        Log-Info "✅ 避免Windows兼容性问题"
        Log-Info "✅ 使用生产级Spark集群"
        Log-Info "✅ 数据存储在HDFS上"
        Log-Info ""
        Log-Info "注意: 确保远程Spark集群正在运行"
        
    } else {
        Configure-LocalMode
        Test-Configuration -TestMode "local"
        
        Log-Info ""
        Log-Info "本地模式配置完成！"
        Log-Info "启动命令: .\start-spark-local.ps1"
        Log-Info ""
        Log-Info "优势:"
        Log-Info "✅ 独立运行，不依赖外部集群"
        Log-Info "✅ 开发调试方便"
        Log-Info ""
        Log-Info "注意: 需要正确的Hadoop Windows工具"
    }
}

# 执行主函数
Main
