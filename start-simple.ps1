Write-Host "=== 启动所有服务 (使用***************远程集群) ==="
Write-Host "时间: $(Get-Date)"
Write-Host ""

# 创建日志目录
if (-not (Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs" -Force
    Write-Host "创建日志目录: logs"
}

# 设置Java环境
$env:JAVA_HOME = "C:\Program Files\Java\jdk1.8.0_141"
Write-Host "Java环境: $env:JAVA_HOME"
Write-Host ""

# 1. 启动 Spark RPC 服务
Write-Host "启动 Spark RPC 服务..."
$sparkProcess = Start-Process -FilePath "$env:JAVA_HOME\bin\java" -ArgumentList "-Xms512m","-Xmx1g","-Dserver.port=8081","-Dspring.profiles.active=dev","-Dspring.application.name=ZYYJ-SPARK-THRIFT","-Deureka.instance.appname=ZYYJ-SPARK-THRIFT","-Deureka.instance.non-secure-port=8081","-Deureka.client.serviceUrl.defaultZone=******************************************/eureka/","-Dspring.main.allow-bean-definition-overriding=true","-Dspark.master=spark://***************:7077","-Dspark.appName=ZYYJ-SPARK-RPC-SERVER","-Dspark.warehouseDir=hdfs://***************:9000/spark-warehouse","-Dspark.metastoreUris=thrift://***************:9083","-Dspark.driver=localhost","-Dspark.driver.bindAddress=0.0.0.0","-Dzyyj.rpc.thrift.server.listen_port=9009","-jar","E:\dev\project\zyyj-cerebro\zyyj-spark-rpc-server\target\zyyj-spark-rpc-server.jar" -RedirectStandardOutput "logs\spark-rpc.log" -RedirectStandardError "logs\spark-rpc-error.log" -PassThru

Write-Host "Spark RPC 进程ID: $($sparkProcess.Id)"
Write-Host ""

# 等待30秒
Write-Host "等待Spark RPC服务启动 (30秒)..."
Start-Sleep -Seconds 30

# 2. 启动 Cerebro RPC 服务
Write-Host "启动 Cerebro RPC 服务..."
$cerebroProcess = Start-Process -FilePath "$env:JAVA_HOME\bin\java" -ArgumentList "-Dspring.profiles.active=dev","-Dspring.main.allow-bean-definition-overriding=true","-Dserver.port=9007","-Dzyyj.rpc.thrift.server.listen_port=9006","-Deureka.client.serviceUrl.defaultZone=******************************************/eureka/","-Dspring.config.location=file:E:\dev\project\zyyj-cerebro\zyyj-cerebro-rpc-server\src\main\resources\dev\application.yml","-jar","E:\dev\project\zyyj-cerebro\bak1.0\zyyj-cerebro-rpc-server\zyyj-cerebro-rpc-server-bin\zyyj-cerebro-rpc-server\zyyj-cerebro-rpc-server.jar" -RedirectStandardOutput "logs\cerebro-rpc.log" -RedirectStandardError "logs\cerebro-rpc-error.log" -PassThru

Write-Host "Cerebro RPC 进程ID: $($cerebroProcess.Id)"
Write-Host ""

# 等待20秒
Write-Host "等待Cerebro RPC服务启动 (20秒)..."
Start-Sleep -Seconds 20

# 3. 启动 REST API 服务
Write-Host "启动 REST API 服务..."
$restProcess = Start-Process -FilePath "$env:JAVA_HOME\bin\java" -ArgumentList "-Dserver.port=8005","-Dspring.profiles.active=dev","-Dspring.application.name=ZYYJ-CEREBRO-REST","-Deureka.instance.appname=ZYYJ-CEREBRO-REST","-Deureka.instance.non-secure-port=8005","-Dspring.main.allow-bean-definition-overriding=true","-Deureka.client.serviceUrl.defaultZone=******************************************/eureka/","-Dservice.cerebro=ZYYJ-CEREBRO-THRIFT","-Dscan.package=com.zyyj.controller","-jar","E:\dev\project\zyyj-cerebro\zyyj-cerebro-rest\target\zyyj-cerebro-rest.jar" -RedirectStandardOutput "logs\rest-api.log" -RedirectStandardError "logs\rest-api-error.log" -PassThru

Write-Host "REST API 进程ID: $($restProcess.Id)"
Write-Host ""

# 等待15秒
Write-Host "等待REST API服务启动 (15秒)..."
Start-Sleep -Seconds 15

# 检查服务状态
Write-Host "检查服务状态..."
Write-Host ""

$listening8081 = netstat -ano | findstr ":8081" | findstr "LISTENING"
$listening9009 = netstat -ano | findstr ":9009" | findstr "LISTENING"
$listening9007 = netstat -ano | findstr ":9007" | findstr "LISTENING"
$listening9006 = netstat -ano | findstr ":9006" | findstr "LISTENING"
$listening8005 = netstat -ano | findstr ":8005" | findstr "LISTENING"

if ($listening8081) { Write-Host "Spark RPC HTTP (端口 8081) - 正在监听" } else { Write-Host "Spark RPC HTTP (端口 8081) - 未监听" }
if ($listening9009) { Write-Host "Spark RPC Thrift (端口 9009) - 正在监听" } else { Write-Host "Spark RPC Thrift (端口 9009) - 未监听" }
if ($listening9007) { Write-Host "Cerebro RPC HTTP (端口 9007) - 正在监听" } else { Write-Host "Cerebro RPC HTTP (端口 9007) - 未监听" }
if ($listening9006) { Write-Host "Cerebro RPC Thrift (端口 9006) - 正在监听" } else { Write-Host "Cerebro RPC Thrift (端口 9006) - 未监听" }
if ($listening8005) { Write-Host "REST API (端口 8005) - 正在监听" } else { Write-Host "REST API (端口 8005) - 未监听" }

Write-Host ""
Write-Host "=== 启动完成 ==="
Write-Host ""
Write-Host "服务访问地址:"
Write-Host "  REST API: http://localhost:8005"
Write-Host "  Spark RPC HTTP: http://localhost:8081"
Write-Host "  Cerebro RPC HTTP: http://localhost:9007"
Write-Host ""
Write-Host "远程集群Web界面:"
Write-Host "  Spark Master: http://***************:8080"
Write-Host "  HDFS: http://***************:9870"
Write-Host "  YARN: http://***************:8088"
Write-Host ""
Write-Host "进程ID:"
Write-Host "  Spark RPC: $($sparkProcess.Id)"
Write-Host "  Cerebro RPC: $($cerebroProcess.Id)"
Write-Host "  REST API: $($restProcess.Id)"
Write-Host ""
Write-Host "测试创建表接口:"
Write-Host "  POST http://localhost:8005/api/delta/createTable"
Write-Host ""
