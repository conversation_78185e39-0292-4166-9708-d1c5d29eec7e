@echo off
echo ========================================
echo Starting All ZYYJ Cerebro Services
echo ========================================

echo.
echo [1/3] Starting Spark RPC Server...
start "Spark RPC Server" cmd /k "start-spark-rpc.bat"

echo Waiting 15 seconds for Spark RPC to initialize...
timeout /t 15 /nobreak > nul

echo.
echo [2/3] Starting Cerebro RPC Server...
start "Cerebro RPC Server" cmd /k "start-cerebro-rpc.bat"

echo Waiting 15 seconds for Cerebro RPC to initialize...
timeout /t 15 /nobreak > nul

echo.
echo [3/3] Starting REST API Server...
start "REST API Server" cmd /k "start-rest-api.bat"

echo.
echo ========================================
echo All services are starting...
echo Please wait for all services to be ready.
echo ========================================
echo.
echo Service URLs:
echo - REST API: http://localhost:8005
echo - Cerebro RPC HTTP: http://localhost:9007
echo - Spark RPC HTTP: http://localhost:8081
echo - Eureka Dashboard: ******************************************
echo.

pause
