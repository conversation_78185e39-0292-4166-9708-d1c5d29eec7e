# 部署到 *************** 服务器说明

## 1. 准备 158 服务器环境

### 安装 Java 8
```bash
sudo apt update
sudo apt install openjdk-8-jdk

# 验证安装
java -version
```

### 设置环境变量
```bash
echo 'export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64' >> ~/.bashrc
echo 'export PATH=$JAVA_HOME/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

## 2. 复制文件到 158 服务器

### 从 Windows 机器复制 jar 包
```bash
# 在 Windows 机器上执行
scp E:\dev\project\zyyj-cerebro\zyyj-spark-rpc-server\target\zyyj-spark-rpc-server.jar root@***************:/opt/

# 复制启动脚本
scp deploy-to-158-server.sh root@***************:/opt/
scp stop-spark-rpc.sh root@***************:/opt/
```

### 在 158 服务器上设置权限
```bash
chmod +x /opt/deploy-to-158-server.sh
chmod +x /opt/stop-spark-rpc.sh
```

## 3. 启动服务

```bash
# 启动服务
cd /opt
./deploy-to-158-server.sh

# 检查服务状态
tail -f /opt/logs/spark-rpc.log

# 检查进程
ps aux | grep java

# 检查端口
netstat -tlnp | grep -E '(8081|9009)'
```

## 4. 验证服务

### 检查 Eureka 注册
访问: http://***************:8761/
查看是否有 ZYYJ-SPARK-THRIFT 服务注册

### 检查 Spark UI
访问: http://***************:4040/
查看 Spark 应用状态

### 测试 Thrift 服务
```bash
telnet 192.168.************
```

## 5. 停止服务

```bash
./stop-spark-rpc.sh
```

## 6. 关键配置说明

- **Driver Host**: *************** (158 服务器 IP)
- **Spark Master**: spark://***************:7077 (仍连接 157 的 Master)
- **HDFS**: hdfs://***************:9000 (仍连接 157 的 HDFS)
- **Hive Metastore**: thrift://***************:9083 (仍连接 157 的 Hive)
- **Eureka**: http://***************:8761/eureka/ (仍注册到 157 的 Eureka)

## 7. 网络优势

- 158 和 157 在同一网段，网络连通性好
- Executor 可以直接连接到 158 的 Driver
- 避免了跨网段的防火墙和路由问题