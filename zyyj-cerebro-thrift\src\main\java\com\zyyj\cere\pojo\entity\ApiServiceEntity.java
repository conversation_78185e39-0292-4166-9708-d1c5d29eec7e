package com.zyyj.cere.pojo.entity;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.sdk.processor.annotation.ThriftPaged;
import lombok.*;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/11/16 09:12
 */
@Setter
@ThriftStruct
@ThriftPaged
@NoArgsConstructor
@ToString
@Builder
@Entity
@EntityListeners(ApiServiceEntity.class)
@Table(name = "api_service")
public class ApiServiceEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, length = 11)
    private Integer id;
    @Column(name = "name", nullable = false, length = 20)
    private String name = "";

    @ThriftConstructor
    public ApiServiceEntity(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public String getName() {
        return name;
    }
}
