package com.zyyj.cere.repository;

import com.zyyj.cere.pojo.entity.DatasetAttributeEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public interface DatasetAttributeRepository extends JpaRepository<DatasetAttributeEntity, Integer>, JpaSpecificationExecutor<DatasetAttributeEntity> {
    List<DatasetAttributeEntity> findByIdIn(List attributeIds);

    List<DatasetAttributeEntity> findByIdNotInAndIdLessThan(List attributeIds,Integer maxNum);

    @Override
    @Query(value = "update dataset_attribute set status = 1 where id = ?1",nativeQuery = true)
    @Modifying
    void deleteById(Integer id);
}