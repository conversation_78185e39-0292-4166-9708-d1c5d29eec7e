package com.zyyj.cere.service;

import com.zyyj.cere.exception.CereExceptionEnum;
import com.zyyj.cere.pojo.dto.ApiNameDTO;
import com.zyyj.cere.pojo.dto.FilterBusinessAndTableDTO;
import com.zyyj.cere.pojo.dto.SubjectBusinessDTO;
import com.zyyj.cere.pojo.dto.SubjectDTO;
import com.zyyj.cere.pojo.entity.BusinessEntity;
import com.zyyj.cere.pojo.entity.SubjectEntity;
import com.zyyj.cere.pojo.entity.TableEntity;
import com.zyyj.cere.repository.BusinessRepository;
import com.zyyj.cere.repository.SubjectRepository;
import com.zyyj.cere.repository.TableRepository;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.rpc.thrift.server.ThriftServiceHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/9/18 16:03
 */
@Service
@ThriftServiceHandler
public class SubjectServiceImpl implements SubjectService {

    @Autowired
    SubjectRepository subjectRepository;
    @Autowired
    BusinessRepository businessRepository;
    @Autowired
    TableRepository tableRepository;

    @Override
    public Map<String, List<SubjectBusinessDTO>> getSubjectNavigateList() {

        Map<String, List<SubjectBusinessDTO>> data = new HashMap<>();
        List<SubjectBusinessDTO> list = new ArrayList<>();

        //查询主题域
        List<SubjectEntity> listSubject = subjectRepository.findAllByStatus(1);
        if (listSubject == null || listSubject.size() == 0) {
            data.put("list", list);
            return data;
        }

        Integer[] subjectIdsArr = new Integer[listSubject.size()];
        for (int i = 0; i < listSubject.size(); i++) {
            subjectIdsArr[i] = listSubject.get(i).getId();
        }
        //查询主题域下的业务集
        List<BusinessEntity> listBusiness = businessRepository.findBySubjectIdIn(subjectIdsArr);

        //数据处理
        Map<Integer, List<BusinessEntity>> map = new HashMap<>();
        for (int i = 0; i < listBusiness.size(); i++) {
            List<BusinessEntity> bList;
            if (!map.containsKey(listBusiness.get(i).getSubjectId())) {
                bList = new ArrayList<>();
            } else {
                bList = map.get(listBusiness.get(i).getSubjectId());
            }
            bList.add(listBusiness.get(i));
            map.put(listBusiness.get(i).getSubjectId(), bList);
        }

        for (int i = 0; i < listSubject.size(); i++) {
            SubjectBusinessDTO subjectBusinessDTO = new SubjectBusinessDTO();
            subjectBusinessDTO.setSubjectId(listSubject.get(i).getId());
            subjectBusinessDTO.setSubjectName(listSubject.get(i).getName());
            if (map.get(listSubject.get(i).getId()) == null) {
                subjectBusinessDTO.setList(new ArrayList<>());
            } else {
                subjectBusinessDTO.setList(map.get(listSubject.get(i).getId()));
            }
            list.add(subjectBusinessDTO);
        }
        System.out.println("subjectBusinessDTO = " + list);

        data.put("list", list);
        return data;

    }

    @Override
    public Map<String, List<FilterBusinessAndTableDTO>> filterBusinessAndTable(String name, Integer isMinJie) {
        Map<String, List<FilterBusinessAndTableDTO>> map = new HashMap<>();
        List<FilterBusinessAndTableDTO> listBusiness = new ArrayList<>();
        List<FilterBusinessAndTableDTO> listTable = new ArrayList<>();
        List<BusinessEntity> listRes = businessRepository.findByNameContaining(name);
        if (listRes != null) {
            for (BusinessEntity b : listRes) {
                listBusiness.add(new FilterBusinessAndTableDTO(b.getId(), b.getName(), "", 0, b.getSubjectId(), 0, ""));
            }
        }
        List<TableEntity> listResTable = null;
        if (isMinJie == 1) {//敏捷建模  只需要物理表和已发布的主题表
            listResTable = tableRepository.findByStatusInAndNameContaining(new Byte[]{0, 3}, name);
        } else {
            listResTable = tableRepository.findByNameContaining(name);
        }

        Integer[] idsArr = new Integer[listResTable.size()];
        if (listResTable != null) {
            for (int i = 0; i < listResTable.size(); i++) {
                TableEntity t = listResTable.get(i);
                listTable.add(new FilterBusinessAndTableDTO(t.getId().intValue(), t.getName(), t.getTableName(), t.getBusinessId().intValue(), 0, t.getStatus(), ""));
                idsArr[i] = t.getBusinessId().intValue();
            }
            List<BusinessEntity> listResBusiness = businessRepository.findByIdIn(idsArr);
            for (int i = 0; i < listResBusiness.size(); i++) {
                listTable.get(i).setSubjectId(listResBusiness.get(i).getSubjectId());
                listTable.get(i).setBusinessName(listResBusiness.get(i).getName());
            }
        }

        map.put("business_list", listBusiness);
        map.put("table_list", listTable);
        return map;
    }

    @Override
    public Map<String, List<SubjectDTO>> getSubjectList() {
        //查询数据
        List<SubjectEntity> list = subjectRepository.findAllByStatus(1);
        //数据转换成返回json格式
        List<SubjectDTO> resultList = new ArrayList();
        if (list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                SubjectDTO s = new SubjectDTO();
                s.setId(list.get(i).getId());
                s.setName(list.get(i).getName());
                resultList.add(s);
            }
        }
        Map<String, List<SubjectDTO>> data = new HashMap<>();
        data.put("list", resultList);
        return data;

    }

    @Override
    public String addSubject(SubjectEntity subjectEntity) {
        //设置默认添加状态为正常1
        subjectEntity.setStatus(1);
        //中文名查重
        List<SubjectEntity> list = subjectRepository.findByNameAndStatus(subjectEntity.getName(), 1);
        if (list != null && list.size() > 0) {
            return "该主题域名称已存在";
        }
        SubjectEntity subjectRes = subjectRepository.saveAndFlush(subjectEntity);
        if (subjectRes == null) {
            throw new ApplicationException(CereExceptionEnum.ERROR_ADD);
        }
        return "";
    }

    @Override
    public String editSubject(SubjectEntity subjectEntity) {
        //检验主题域是否已被删除
        boolean exist = subjectRepository.existsByIdAndStatus(subjectEntity.getId(), 1);
        if (!exist) {
            return "该主题域名称已被删除";
        }
        //检验主题域名称是否已存在
        List<SubjectEntity> list = subjectRepository.existByIdAndName(subjectEntity.getId(), subjectEntity.getName());

        if (list != null && list.size() > 0) {
            return "该主题域名称已存在";
        }
        subjectEntity.setStatus(1);
        subjectRepository.saveAndFlush(subjectEntity);
        return "";
    }

    @Override
    public String delSubject(Integer id) {
        boolean exist = subjectRepository.existsById(id);
        if (!exist) {
            return "该主题域已被删除";
        }
        subjectRepository.updateById(id.intValue());
        return "";
    }

}
