@echo off
echo Starting ZYYJ Cerebro RPC Server...

set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_141

REM 检查 JAR 文件是否存在（使用备份目录）
if exist "bak1.0\zyyj-cerebro-rpc-server\zyyj-cerebro-rpc-server-bin\zyyj-cerebro-rpc-server\zyyj-cerebro-rpc-server.jar" (
    cd /d bak1.0\zyyj-cerebro-rpc-server\zyyj-cerebro-rpc-server-bin\zyyj-cerebro-rpc-server
    echo Found JAR file in backup directory, starting service...
) else (
    echo JAR file not found in backup directory
    pause
    exit /b 1
)

java "-Dspring.profiles.active=dev" ^
  "-Dspring.main.allow-bean-definition-overriding=true" ^
  "-Dserver.port=9007" ^
  "-Dzyyj.rpc.thrift.server.listen_port=9006" ^
  "-Deureka.client.serviceUrl.defaultZone=******************************************/eureka/" ^
  -jar zyyj-cerebro-rpc-server.jar

pause
