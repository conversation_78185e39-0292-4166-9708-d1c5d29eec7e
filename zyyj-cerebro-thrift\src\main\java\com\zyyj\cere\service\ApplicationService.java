package com.zyyj.cere.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.zyyj.cere.pojo.dto.PagedAppApiDTO;
import com.zyyj.cere.pojo.dto.PagedApplicationDTO;
import com.zyyj.cere.pojo.entity.ApplicationEntity;
import com.zyyj.cere.pojo.entity.ApplicationTypeEntity;
import com.zyyj.domain.pagination.Paging;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2020/11/10 16:36
 */

@ThriftService
public interface ApplicationService {

    @ThriftMethod
    PagedApplicationDTO getApplicationList(String key, Integer typeId, Paging paging);

    @ThriftMethod
    ApplicationEntity getApplicationDetail(Integer id);

    @ThriftMethod
    String addApplication(ApplicationEntity applicationEntity);

    @ThriftMethod
    String editApplication(ApplicationEntity applicationEntity);

    @ThriftMethod
    String delApplication(Integer id);

    @ThriftMethod
    Map<String, List<ApplicationTypeEntity>> getApplicationTypes();

    @ThriftMethod
    String addApplicationType(ApplicationTypeEntity applicationTypeEntity);

    @ThriftMethod
    String editApplicationType(ApplicationTypeEntity applicationTypeEntity);

    @ThriftMethod
    String delApplicationType(Integer id);

    @ThriftMethod
    PagedAppApiDTO getApiApplicationList(Integer applicationId, Paging paging);

    @ThriftMethod
    void setApplicationApi(Integer applicationId, String apiIds);

    @ThriftMethod
    void delApplicationApi(Integer id);

}
