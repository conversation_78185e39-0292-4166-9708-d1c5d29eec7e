package com.zyyj.cere.repository;

import com.zyyj.cere.pojo.dto.ApiNameDTO;
import com.zyyj.cere.pojo.dto.ApiResponseDTO;
import com.zyyj.cere.pojo.entity.ApiEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/16 16:45
 */

public interface ApiRepository extends JpaRepository<ApiEntity, Integer> {
    //自定义原生sql联表分页查询
    @Query(value = "SELECT a.id,a.name,a.describe,CASE a.method WHEN 1 THEN CONCAT('/cerebro/',ase.name,'/',a.name,'/get') ELSE CONCAT('/cerebro/',ase.name,'/',a.name,'/post') END AS apiUrl,a.status  " +
            "FROM api a " +
            "LEFT JOIN api_service ase ON ase.id=a.service_id " +
            "WHERE a.id > 0 " +
            "AND IF (?1 = 0,1=1,a.service_id = ?1) " +
            "AND IF (?2 = '',1=1,a.name LIKE ?2) " +
            "ORDER BY a.id DESC " +
            "LIMIT ?3,?4", nativeQuery = true)
    List<Object> queryByServiceIdAndNameLike(Integer serviceId, String name, Integer startIndex, Integer size);

    //查询总数
    @Query(value = "SELECT count(a.id)" +
            "FROM api a " +
            "LEFT JOIN api_service ase ON ase.id=a.service_id " +
            "WHERE a.id > 0 " +
            "AND IF (?1 = 0,1=1,a.service_id = ?1) " +
            "AND IF (?2 = '',1=1,a.name LIKE ?2)", nativeQuery = true)
    Integer countByServiceIdAndNameLike(Integer serviceId, String name);


    List<ApiEntity> findByServiceId(Integer serviceId);

    List<ApiEntity> findByServiceIdAndName(Integer serviceId, String name);

    @Query(value = "SELECT * FROM api WHERE id != ?1 AND service_id = ?2 AND name = ?3", nativeQuery = true)
    List<ApiEntity> existByIdAndServiceIdAndName(Integer id, Integer serviceId, String name);

    @Query(value = "SELECT new com.zyyj.cere.pojo.dto.ApiNameDTO(a.id,a.name) " +
            "FROM ApiEntity a " +
            "WHERE a.status = 2 AND a.serviceId = ?1")
    List<ApiNameDTO> findApiByServiceId(Integer serviceId);

    @Modifying //更新插入操作必须加此注解
    @Query(value = "UPDATE api SET status = ?1 WHERE id = ?2", nativeQuery = true)
    void updateStatusById(Integer status, Integer id);


}
