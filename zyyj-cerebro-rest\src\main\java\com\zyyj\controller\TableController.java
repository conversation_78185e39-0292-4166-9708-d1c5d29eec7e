package com.zyyj.controller;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.zyyj.cere.pojo.body.TableAddBody;
import com.zyyj.cere.pojo.body.TunnelAddFormModel;
import com.zyyj.cere.pojo.dto.PreviewTableDTO;
import com.zyyj.cere.pojo.dto.TableListDTO;
import com.zyyj.cere.pojo.dto.TableStructDTO;
import com.zyyj.cere.pojo.entity.TableEntity;
import com.zyyj.cere.pojo.resp.TableTunnelUpdateInfoResp;
import com.zyyj.cere.service.TableService;
import com.zyyj.cere.service.TunnelService;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.json.OperationTableParam;
import com.zyyj.pojo.bo.TunnelAddBatchFormModel;
import com.zyyj.utils.PublicUtils;
import com.zyyj.utils.excel.ZYExcelUtil;
import com.zyyj.utils.excel.ZYTableData;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;


import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/v1/table")
@CrossOrigin(origins = "*", maxAge = 3600)
public class TableController {

    @Autowired
    TableService tableService;

    @Autowired
    TunnelService tunnelService;

    @ApiOperation("数据表")
    @GetMapping("/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "status", value = "筛选状态", required = true, paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "name", value = "表名", required = true, paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "business_id", value = "业务域id", required = true, paramType = "query", dataType = "int")
    })
    public List<TableListDTO> getDatasourcePage(String status, String name, Long business_id) {
        //参数非空校验
        PublicUtils.checkNotNullArr(business_id);

        return tableService.getTableList(status, name, business_id);
    }

    @ApiOperation("添加物理表")
    @PostMapping("/add")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "TableAddBody", value = "添加物理表对象", required = true, paramType = "body", dataType = "TableAddBody"),
    })
    public TableEntity addTable(@RequestBody @Valid TableAddBody t, BindingResult bindingResult) {
        System.out.println(t);
        //参数非空校验
        PublicUtils.checkNotNullArr(t.getBusinessId());
        PublicUtils.checkNotEmptyArr(t.getName(), t.getTableName(), t.getFieldJson());

        if (bindingResult.hasErrors()) {
            throw new ApplicationException(bindingResult.getFieldError().getDefaultMessage());
        }

        List<List<String>> data = new ArrayList<>();

        if (t.getFileUrl() != null && !t.getFileUrl().isEmpty()) {
            //解析excel
            List<ZYTableData> l = ZYExcelUtil.builder().getExcelData(t.getFileUrl());
            if (l.size() == 0) throw new ApplicationException("Excel数据不可为空");
            data = l.get(0).getData();
        }
        TableEntity tableEntity = tableService.setTableByManual(t, data);
        return tableEntity;
    }

    @ApiOperation("重命名")
    @PostMapping("/rename")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "TableEntity", value = "表对象", required = true, paramType = "body", dataType = "TableEntity"),
    })
    public void renameTable(@RequestBody TableEntity tableEntity) {
        //参数非空校验
        PublicUtils.checkNotNullArr(tableEntity.getId());
        PublicUtils.checkNotEmptyArr(tableEntity.getName());

        tableService.renameTable(tableEntity);
    }

    @ApiOperation("移动表")
    @PostMapping("/move")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "TableEntity", value = "表移动对象", required = true, paramType = "body", dataType = "TableEntity"),
    })
    public void moveTable(@RequestBody TableEntity tableEntity) {
        //参数非空校验
        PublicUtils.checkNotNullArr(tableEntity.getId(), tableEntity.getBusinessId());

        tableService.moveTable(tableEntity);
    }

    @ApiOperation("删除表")
    @PostMapping("/remove/{id}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true, paramType = "path", dataType = "long")
    })
    public void removeTable(@PathVariable Long id) {
        tableService.removeTable(id);
    }

    @ApiOperation("发布/审核")
    @PostMapping("/operation")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "TableEntity", value = "表发布/审核对象", required = true, paramType = "body", dataType = "TableEntity"),
    })
    public void operationTable(@RequestBody OperationTableParam model) {
        //参数非空校验
        PublicUtils.checkIntNotNull(model.getId());
        PublicUtils.checkIntNotNull(model.getStatus());
        System.out.println("=====参数======" + model);

        tableService.operationTable(model.getId(),model.getStatus());
    }

    @ApiOperation("结构预览")
    @GetMapping("/struct/{id}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true, paramType = "path", dataType = "long")
    })
    public List<TableStructDTO> getTableStruct(@PathVariable Long id) {
        return tableService.getTableStruct(id);
    }


    /**
     * @Description: 查看表结构
     * @Param: [tableName]
     * @return: java.util.List<com.zyyj.cere.pojo.dto.TableStructDTO>
     * @Author: bravelee
     * @Date: 2020/11/25
     */
    @GetMapping("/preview/{tableName}")
    public List<TableStructDTO> getTablePreview(@PathVariable String tableName) {
        return tableService.getTablePreview(tableName);
    }

    @ApiOperation("获取文件头信息")
    @GetMapping("/get_head")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileUrl", value = "文件地址", required = true, paramType = "query", dataType = "string")
    })
    public List<String> getHead(String fileUrl) {
        //解析excel
        List<ZYTableData> l = ZYExcelUtil.builder().getExcelData(fileUrl);
        if (l.size() == 0) throw new ApplicationException("Excel数据不可为空");
        return l.get(0).getData().get(0);
    }

    @ApiOperation("数据预览")
    @GetMapping("/data")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page", value = "当前页面", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "size", value = "每页数量", required = true, paramType = "query", dataType = "int")
    })
    public PreviewTableDTO previewTable(Long id, Integer page, Integer size) {
        List<TableStructDTO> head = tableService.getTableStruct(id);
        if (head.size() == 0) throw new ApplicationException("表不存在");
        Map<String, String> m = tableService.previewTable(id, head.get(0).getCol_name(), page, size);
        return PreviewTableDTO
                .builder()
                .total(Integer.parseInt(m.get("total")))
                .data(new Gson().fromJson(m.get("data"), new TypeToken<List<Map<String, String>>>() {
                }.getType()))
                .head(head)
                .build();
    }


    /* 获取表更多信息 */
    @GetMapping("/update_info/{id}")
    public Map<String, Object> getUpdateInfo(@PathVariable Long id){
        // 数据参数校验
        PublicUtils.checkLongNotNull(id);

        Map map = new HashMap<String, Object>();
        try {
            TableTunnelUpdateInfoResp tunnel = tableService.getUpdateInfo(id);
            map.put("tunnel", tunnel);
        }catch (Exception e){
            map.put("tunnel", new HashMap<String, Object>());
        }
        return map;
    }


    /* 添加管道 */
    @PostMapping("/tunnel/add")
    public HashMap<String, Long> postTunnelAdd(@RequestBody TunnelAddFormModel formModel) {
        // 数据参数校验
        PublicUtils.checkLongNotNull(formModel.getDatasourceId());
        PublicUtils.checkNotEmptyArr(formModel.getSourceTable());
        PublicUtils.checkLongNotNull(formModel.getBusinessId());
        PublicUtils.checkNotEmptyArr(formModel.getTableName());
        PublicUtils.checkNotEmptyArr(formModel.getTableComment());
        PublicUtils.checkIntNotNull(formModel.getMappingType());

        if (formModel.getMappingConf().isEmpty()){
            throw  new ApplicationException("参数错误");
        }
        Long tableId = tunnelService.initTunnel(formModel);
        return  new HashMap<String, Long>() {{
            put("table_id", tableId);
        }};
    }


    /* 批量添加管道 */
    @PostMapping("/tunnel/add_batch")
    public String postAddTunnelBatch(@RequestBody TunnelAddBatchFormModel model) {
        return "";
    }


    /* 获取管道信息 */
    @GetMapping("/tunnel/info/{id}")
    public String getTunnelInfo(@PathVariable Long id) {
        PublicUtils.checkLongNotNull(id);
        return tunnelService.getTunnelInfo(id);
    }


    /* 删除管道 */
    @PostMapping("/tunnel/del/{id}")
    public String postTunnelDel(@PathVariable Long id){
        System.out.println("id is:"+id);
        PublicUtils.checkLongNotNull(id);
        return tunnelService.deleteTunnel(id);
    }


    /* 停止管道 */
    @PostMapping("/tunnel/stop/{id}")
    public String postTunnelStop(@PathVariable Long id){
        System.out.println("id is: "+id);
        PublicUtils.checkLongNotNull(id);
        return tunnelService.stopTunnel(id);
    }

    /* 重启管道 */
    @PostMapping("/tunnel/resume/{id}")
    public String postTunnelResume(@PathVariable Long id){
        PublicUtils.checkLongNotNull(id);
        return tunnelService.resumeTunnel(id);
    }



}
