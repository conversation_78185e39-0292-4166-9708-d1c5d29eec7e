package com.zyyj.cere.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.zyyj.cere.pojo.dto.ApiNameDTO;
import com.zyyj.cere.pojo.dto.PageApiDTO;
import com.zyyj.cere.pojo.entity.ApiEntity;
import com.zyyj.cere.pojo.entity.ApiServiceEntity;
import com.zyyj.domain.pagination.Paging;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/16 15:57
 */

@ThriftService
public interface ApiService {

    @ThriftMethod
    Map<String, List<ApiServiceEntity>> getServerList();

    @ThriftMethod
    ApiServiceEntity getServerDetail(Integer id);

    @ThriftMethod
    String addServer(ApiServiceEntity apiServiceEntity);

    @ThriftMethod
    String editServer(ApiServiceEntity ApiServiceEntity);

    @ThriftMethod
    String delServer(Integer id);

    @ThriftMethod
    PageApiDTO getPageApiList(Integer serviceId, String name, Paging paging);

    @ThriftMethod
    Map<String, List<ApiNameDTO>> getApiList(Integer serviceId);

    @ThriftMethod
    ApiEntity getApiDetail(Integer id);

    @ThriftMethod
    String addApi(ApiEntity apiEntity);

    @ThriftMethod
    String editApi(ApiEntity apiEntity);


    @ThriftMethod
    String operationApi(ApiEntity apiEntity);
}
