package com.zyyj.cere.repository;


import com.zyyj.cere.pojo.dto.DatasourceDTO;
import com.zyyj.cere.pojo.entity.DatasourceEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Component;


@Component
public interface DatasourceRepository extends JpaRepository<DatasourceEntity, Long>, JpaSpecificationExecutor<DatasourceEntity> {
    /**
     * 详情
     *
     * @return
     */
    @Query(value = "SELECT new com.zyyj.cere.pojo.dto.DatasourceDTO(d.id,d.typeId,dt.name AS typeName,d.name,d.describe,d.host,d.username,d.password,d.database) " +
            "       FROM DatasourceEntity d " +
            "       LEFT JOIN DatasourceTypeEntity dt ON dt.id=d.typeId" +
            "       WHERE d.id=?1")
    DatasourceDTO queryInfoById(Long id);

}