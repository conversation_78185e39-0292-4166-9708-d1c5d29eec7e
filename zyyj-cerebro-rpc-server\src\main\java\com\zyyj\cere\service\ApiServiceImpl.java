package com.zyyj.cere.service;

import com.zyyj.cere.exception.CereExceptionEnum;
import com.zyyj.cere.pojo.dto.ApiNameDTO;
import com.zyyj.cere.pojo.dto.ApiResponseDTO;
import com.zyyj.cere.pojo.dto.PageApiDTO;
import com.zyyj.cere.pojo.entity.ApiEntity;
import com.zyyj.cere.pojo.entity.ApiServiceEntity;
import com.zyyj.cere.repository.ApiRepository;
import com.zyyj.cere.repository.ApiServiceRepository;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.domain.pagination.Paging;
import com.zyyj.rpc.thrift.server.ThriftServiceHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/16 16:07
 */
@Slf4j
@Service
@ThriftServiceHandler
public class ApiServiceImpl implements ApiService {

    @Autowired
    ApiServiceRepository apiServiceRepository;
    @Autowired
    ApiRepository apiRepository;

    @Override
    public Map<String, List<ApiServiceEntity>> getServerList() {
        List<ApiServiceEntity> list = apiServiceRepository.findAll();
        Map<String, List<ApiServiceEntity>> data = new HashMap<>();
        data.put("list", list);
        return data;
    }

    @Override
    public ApiServiceEntity getServerDetail(Integer id) {

        return apiServiceRepository.findById(id).get();
    }

    @Override
    public String addServer(ApiServiceEntity apiServiceEntity) {
        //名称查重
        List<ApiServiceEntity> list = apiServiceRepository.findByName(apiServiceEntity.getName());
        if (list != null && list.size() > 0) {
            return "该服务单元名称已存在";
        }

        ApiServiceEntity res = apiServiceRepository.saveAndFlush(apiServiceEntity);
        if (res == null) {
            throw new ApplicationException(CereExceptionEnum.ERROR_ADD);
        }

        return "";
    }

    @Override
    public String editServer(ApiServiceEntity apiServiceEntity) {
        boolean exist = apiServiceRepository.existsById(apiServiceEntity.getId());
        if (!exist) {
            return "该服务单元已被删除,请刷新页面";
        }

        //名称查重
        List<ApiServiceEntity> list = apiServiceRepository.existByIdAndName(apiServiceEntity.getId(), apiServiceEntity.getName());
        if (list != null && list.size() > 0) {
            return "该服务单元已存在";
        }

        apiServiceRepository.saveAndFlush(apiServiceEntity);
        return "";
    }

    @Override
    public String delServer(Integer id) {
        //检验服务单元是否存在
        boolean exist = apiServiceRepository.existsById(id);
        if (!exist) {
            return "该服务单元已被删除,请刷新页面";
        }
        //查询服务单元下是否还存在应用api
        List<ApiEntity> list = apiRepository.findByServiceId(id);
        if (list != null && list.size() > 0) {
            return "该服务单元下还存在应用api不可删除";
        }
        apiServiceRepository.deleteById(id);
        return "";
    }

    @Override
    public PageApiDTO getPageApiList(Integer serviceId, String key, Paging paging) {
        //关键字key
        key = "%" + key + "%";
        Integer total = apiRepository.countByServiceIdAndNameLike(serviceId, key);
        List<ApiResponseDTO> list = new ArrayList<>();
        List<Object> res = apiRepository.queryByServiceIdAndNameLike(serviceId, key, (paging.getPage() - 1) * paging.getSize(), paging.getSize());
        for (Object o : res) {
            ApiResponseDTO apiResponseDTO = new ApiResponseDTO();
            Object[] rowArr = (Object[]) o;
            apiResponseDTO.setId((Integer) rowArr[0]);
            apiResponseDTO.setName((String) rowArr[1]);
            apiResponseDTO.setDescribe((String) rowArr[2]);
            apiResponseDTO.setApiUrl((String) rowArr[3]);
            apiResponseDTO.setStatus((int) (byte) rowArr[4]);
            list.add(apiResponseDTO);
        }

        return new PageApiDTO(total, list);
    }

    @Override
    public Map<String, List<ApiNameDTO>> getApiList(Integer serviceId) {
        List<ApiNameDTO> list = apiRepository.findApiByServiceId(serviceId);
        if (list == null) {
            list = new ArrayList<>();
        }
        Map<String, List<ApiNameDTO>> map = new HashMap<>();
        map.put("list", list);
        return map;
    }

    @Override
    public ApiEntity getApiDetail(Integer id) {
        return apiRepository.findById(id).get();
    }

    @Override
    public String addApi(ApiEntity apiEntity) {

        List<ApiEntity> list = apiRepository.findByServiceIdAndName(apiEntity.getServiceId(), apiEntity.getName());
        if (list != null && list.size() > 0) {
            return "该服务单元下API名称已存在";
        }
        apiRepository.saveAndFlush(apiEntity);
        return "";
    }

    @Override
    public String editApi(ApiEntity apiEntity) {

        boolean exists = apiRepository.existsById(apiEntity.getId());
        if (!exists) {
            return "该API已被删除,请刷新页面";
        }
        ApiEntity res = apiRepository.findById(apiEntity.getId()).get();

        List<ApiEntity> list = apiRepository.existByIdAndServiceIdAndName(apiEntity.getId(), apiEntity.getServiceId(), apiEntity.getName());
        if (list != null && list.size() > 0) {
            return "该服务单元下API名称已存在";
        }

        apiEntity.setStatus(res.getStatus());//保存原来状态
        apiRepository.saveAndFlush(apiEntity);
        return "";
    }

    @Transactional
    @Override
    public String operationApi(ApiEntity apiEntity) {
        boolean exist = apiRepository.existsById(apiEntity.getId());
        if (!exist) {
            return "该API已被删除,请刷新页面";
        }
        if (apiEntity.getStatus() == 3) {
            apiRepository.deleteById(apiEntity.getId());
            return "";
        }
        apiRepository.updateStatusById(apiEntity.getStatus(), apiEntity.getId());
        return "";
    }
}
