#!/bin/bash

# =============================================================================
# Spark RPC 服务 Ubuntu 部署脚本
# =============================================================================

set -e

# 配置变量
UBUNTU_SERVER="***************"
UBUNTU_USER="root"  # 根据实际情况修改
DEPLOY_DIR="/opt/zyyj-cerebro"
SERVICE_NAME="zyyj-spark-rpc"
JAR_NAME="zyyj-spark-rpc-server.jar"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查本地文件
check_local_files() {
    log_step "检查本地文件..."
    
    if [ ! -f "zyyj-spark-rpc-server/target/${JAR_NAME}" ]; then
        log_error "本地JAR文件不存在: zyyj-spark-rpc-server/target/${JAR_NAME}"
        log_info "请先执行: mvn clean package -DskipTests"
        exit 1
    fi
    
    log_info "本地文件检查完成"
}

# 测试服务器连接
test_connection() {
    log_step "测试服务器连接..."
    
    if ! ssh -o ConnectTimeout=5 ${UBUNTU_USER}@${UBUNTU_SERVER} "echo 'Connection test successful'" > /dev/null 2>&1; then
        log_error "无法连接到Ubuntu服务器: ${UBUNTU_SERVER}"
        log_info "请检查:"
        log_info "1. 服务器IP地址是否正确"
        log_info "2. SSH密钥是否配置"
        log_info "3. 用户名是否正确"
        exit 1
    fi
    
    log_info "服务器连接测试成功"
}

# 检查服务器环境
check_server_environment() {
    log_step "检查服务器环境..."
    
    # 检查Java
    JAVA_VERSION=$(ssh ${UBUNTU_USER}@${UBUNTU_SERVER} "java -version 2>&1 | head -n 1" || echo "")
    if [[ $JAVA_VERSION == *"1.8"* ]] || [[ $JAVA_VERSION == *"openjdk version \"8"* ]]; then
        log_info "Java 8 环境正常: $JAVA_VERSION"
    else
        log_warn "Java 8 未找到，将尝试安装"
        install_java8
    fi
    
    # 检查Hadoop
    HADOOP_VERSION=$(ssh ${UBUNTU_USER}@${UBUNTU_SERVER} "hadoop version 2>/dev/null | head -n 1" || echo "")
    if [[ $HADOOP_VERSION == *"Hadoop"* ]]; then
        log_info "Hadoop环境正常: $HADOOP_VERSION"
    else
        log_warn "Hadoop未找到，需要安装Hadoop环境"
    fi
    
    # 检查Spark
    SPARK_VERSION=$(ssh ${UBUNTU_USER}@${UBUNTU_SERVER} "spark-submit --version 2>&1 | grep version" || echo "")
    if [[ $SPARK_VERSION == *"version"* ]]; then
        log_info "Spark环境正常: $SPARK_VERSION"
    else
        log_warn "Spark未找到，需要安装Spark环境"
    fi
}

# 安装Java 8
install_java8() {
    log_step "安装Java 8..."
    ssh ${UBUNTU_USER}@${UBUNTU_SERVER} "
        apt update
        apt install -y openjdk-8-jdk
        echo 'export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64' >> ~/.bashrc
        source ~/.bashrc
    "
    log_info "Java 8 安装完成"
}

# 创建部署目录
create_deploy_directory() {
    log_step "创建部署目录..."
    
    ssh ${UBUNTU_USER}@${UBUNTU_SERVER} "
        mkdir -p ${DEPLOY_DIR}
        mkdir -p ${DEPLOY_DIR}/logs
        mkdir -p ${DEPLOY_DIR}/config
        mkdir -p ${DEPLOY_DIR}/warehouse
    "
    
    log_info "部署目录创建完成: ${DEPLOY_DIR}"
}

# 上传文件
upload_files() {
    log_step "上传应用文件..."
    
    # 上传JAR文件
    scp "zyyj-spark-rpc-server/target/${JAR_NAME}" ${UBUNTU_USER}@${UBUNTU_SERVER}:${DEPLOY_DIR}/
    
    # 上传配置文件
    scp "zyyj-spark-rpc-server/src/main/resources/dev/application.yml" ${UBUNTU_USER}@${UBUNTU_SERVER}:${DEPLOY_DIR}/config/
    
    log_info "文件上传完成"
}

# 创建启动脚本
create_startup_script() {
    log_step "创建启动脚本..."
    
    ssh ${UBUNTU_USER}@${UBUNTU_SERVER} "cat > ${DEPLOY_DIR}/start.sh << 'EOF'
#!/bin/bash

# Spark RPC 服务启动脚本

export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export HADOOP_HOME=/opt/hadoop
export SPARK_HOME=/opt/spark
export PATH=\$JAVA_HOME/bin:\$HADOOP_HOME/bin:\$SPARK_HOME/bin:\$PATH

cd ${DEPLOY_DIR}

# 停止现有服务
pkill -f '${JAR_NAME}' || true
sleep 5

# 启动服务
nohup java \\
    -Xms512m \\
    -Xmx1g \\
    -Dserver.port=8081 \\
    -Dspring.profiles.active=dev \\
    -Dspring.application.name=ZYYJ-SPARK-THRIFT \\
    -Deureka.instance.appname=ZYYJ-SPARK-THRIFT \\
    -Deureka.instance.non-secure-port=8081 \\
    -Deureka.client.serviceUrl.defaultZone=http://admin:admin123@***************:8761/eureka/ \\
    -Dspring.main.allow-bean-definition-overriding=true \\
    -Dspark.metastoreUris=thrift://***************:9083 \\
    -Dspark.warehouseDir=file://${DEPLOY_DIR}/warehouse/ \\
    -Dspark.master=local[*] \\
    -Dspark.appName=ZYYJ-SPARK-RPC-SERVER \\
    -Dspark.driver=\$(hostname -I | awk '{print \$1}') \\
    -Dspark.driver.bindAddress=0.0.0.0 \\
    -Dzyyj.rpc.thrift.server.listen_port=9009 \\
    -Dspring.config.location=file:${DEPLOY_DIR}/config/application.yml \\
    -jar ${JAR_NAME} \\
    > logs/spark-rpc.log 2>&1 &

echo \"Spark RPC 服务启动中...\"
echo \"PID: \$!\"
echo \"日志文件: ${DEPLOY_DIR}/logs/spark-rpc.log\"
EOF"

    # 设置执行权限
    ssh ${UBUNTU_USER}@${UBUNTU_SERVER} "chmod +x ${DEPLOY_DIR}/start.sh"
    
    log_info "启动脚本创建完成"
}

# 创建停止脚本
create_stop_script() {
    log_step "创建停止脚本..."
    
    ssh ${UBUNTU_USER}@${UBUNTU_SERVER} "cat > ${DEPLOY_DIR}/stop.sh << 'EOF'
#!/bin/bash

echo \"停止 Spark RPC 服务...\"
pkill -f '${JAR_NAME}'
sleep 3
echo \"服务已停止\"
EOF"

    ssh ${UBUNTU_USER}@${UBUNTU_SERVER} "chmod +x ${DEPLOY_DIR}/stop.sh"
    
    log_info "停止脚本创建完成"
}

# 创建状态检查脚本
create_status_script() {
    log_step "创建状态检查脚本..."
    
    ssh ${UBUNTU_USER}@${UBUNTU_SERVER} "cat > ${DEPLOY_DIR}/status.sh << 'EOF'
#!/bin/bash

echo \"=== Spark RPC 服务状态 ===\"

# 检查进程
PID=\$(pgrep -f '${JAR_NAME}')
if [ -n \"\$PID\" ]; then
    echo \"✅ 服务运行中 (PID: \$PID)\"
else
    echo \"❌ 服务未运行\"
fi

# 检查端口
echo \"\"
echo \"=== 端口状态 ===\"
netstat -tlnp | grep -E ':8081|:9009' || echo \"端口未监听\"

# 检查日志
echo \"\"
echo \"=== 最近日志 ===\"
tail -n 10 ${DEPLOY_DIR}/logs/spark-rpc.log 2>/dev/null || echo \"日志文件不存在\"
EOF"

    ssh ${UBUNTU_USER}@${UBUNTU_SERVER} "chmod +x ${DEPLOY_DIR}/status.sh"
    
    log_info "状态检查脚本创建完成"
}

# 部署服务
deploy_service() {
    log_step "部署Spark RPC服务..."
    
    # 停止现有服务
    ssh ${UBUNTU_USER}@${UBUNTU_SERVER} "${DEPLOY_DIR}/stop.sh" 2>/dev/null || true
    
    # 启动服务
    ssh ${UBUNTU_USER}@${UBUNTU_SERVER} "${DEPLOY_DIR}/start.sh"
    
    # 等待启动
    sleep 10
    
    # 检查状态
    ssh ${UBUNTU_USER}@${UBUNTU_SERVER} "${DEPLOY_DIR}/status.sh"
    
    log_info "服务部署完成"
}

# 主函数
main() {
    log_info "开始部署 Spark RPC 服务到 Ubuntu 服务器"
    log_info "目标服务器: ${UBUNTU_SERVER}"
    log_info "部署目录: ${DEPLOY_DIR}"
    
    check_local_files
    test_connection
    check_server_environment
    create_deploy_directory
    upload_files
    create_startup_script
    create_stop_script
    create_status_script
    deploy_service
    
    log_info "部署完成！"
    log_info ""
    log_info "管理命令:"
    log_info "启动服务: ssh ${UBUNTU_USER}@${UBUNTU_SERVER} '${DEPLOY_DIR}/start.sh'"
    log_info "停止服务: ssh ${UBUNTU_USER}@${UBUNTU_SERVER} '${DEPLOY_DIR}/stop.sh'"
    log_info "查看状态: ssh ${UBUNTU_USER}@${UBUNTU_SERVER} '${DEPLOY_DIR}/status.sh'"
    log_info "查看日志: ssh ${UBUNTU_USER}@${UBUNTU_SERVER} 'tail -f ${DEPLOY_DIR}/logs/spark-rpc.log'"
}

# 执行主函数
main "$@"
