package com.zyyj.cere.service;



import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.zyyj.cere.pojo.entity.*;
import com.zyyj.cere.pojo.body.TunnelAddFormModel;
import com.zyyj.cere.pojo.kafka.KafkaConnectorResp;
import com.zyyj.cere.pojo.kafka.KCMySqlSourceConfig;


import com.zyyj.cere.property.KafkaProperties;
import com.zyyj.cere.repository.DatasourceRepository;
import com.zyyj.cere.repository.KccRepository;
import com.zyyj.cere.repository.TableRepository;
import com.zyyj.cere.repository.TableTunnelRepository;
import com.zyyj.cere.utils.IDWorker;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.rpc.thrift.server.ThriftServiceHandler;
import com.zyyj.spark.pojo.dto.DTMappingModel;
import com.zyyj.spark.pojo.dto.DataTunnelModel;
import com.zyyj.spark.service.DeltaTunnelService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Slf4j
@Service
@ThriftServiceHandler
public class TunnelServiceImpl implements TunnelService {

    @Autowired
    KafkaProperties kafkaProperty;


    // kafka 服务
    @Autowired
    KafkaServiceImpl kafkaService;

    @Autowired
    DeltaTunnelService tunnelService;


    // 数据库操作
    @Autowired
    KccRepository kccRepository;

    @Autowired
    TableRepository tableRepository;

    @Autowired
    DatasourceRepository datasourceRepository;

    @Autowired
    TableTunnelRepository tableTunnelRepository;


    @Override
    @Transactional
    public Long initTunnel(TunnelAddFormModel model) throws ApplicationException {
        Long sid = model.getDatasourceId();

        //查询来源表信息
        Optional<DatasourceEntity> opDsEntity = datasourceRepository.findById(sid);
        if (!opDsEntity.isPresent()){
            throw new ApplicationException("数据源为空");
        }

        // 目标表名
        String tableName = model.getTableName();
        // 原始表名
        String sourceTableName = model.getSourceTable();

        // 生成kafka source config
        KCMySqlSourceConfig config = createMySqlSourceConfig(opDsEntity.get(), sourceTableName);
        String kafkaName = config.getName();


        //2。 数据库操作
        //2.1 。添加 table_tunnel 表
        TableTunnelEntity entity = new TableTunnelEntity();
        entity.setDatasourceId(model.getDatasourceId());
        entity.setSourceTableName(model.getSourceTable());
        entity.setTargetTableName(model.getTableName());

        // 判断相同的数据源相同的来源表相同目标表是否存在
        ExampleMatcher matcher = ExampleMatcher.matching();
        Example<TableTunnelEntity> example = Example.of(entity, matcher);
        boolean isExist = tableTunnelRepository.exists(example);
        if (isExist){ //存在
            throw new ApplicationException("已经存在相同的数据管道");
        }

        entity.setName(model.getSourceTable());
        entity.setMappingType(model.getMappingType());
        entity.setMappingConf(model.getMappingConfig());
        tableTunnelRepository.save(entity);


        // 查询通道生成的表在delta库中是否存在
//        isExist = deltaService.queryTableIsExist(tableName);
//        if (isExist) { // 表已存在, 需要对数据结构判断
//            List<String> l = deltaService.getTableStruct(tableName);
//            List<TableStructDTO> tableStructDTOS = TableStructDTO.getList(l);
//            for (TableStructDTO table:tableStructDTOS) {
//                String colName = table.getCol_name();
//                DTMappingModel m = dtModel.getMappingConfByField(colName);
//                if (m==null){
//                    throw new ApplicationException("当前通道目标数据表已存在; 通道生成数据与原始表数据无法匹配");
//                }
//            }
//        }


        //2.2 。添加 kcc 表 将source 的配置添加 到 kcc 中
        KccEntity kccEntity = new KccEntity();
        kccEntity.setName(config.getName());
        kccEntity.setDatasourceId(sid);
        kccEntity.setTunnelId(entity.getId());
        kccEntity.setType(1);
        kccEntity.setStatus(1);
        kccEntity.setServerName(config.getServerName());
        kccEntity.setDbName(config.getDbName());
        kccEntity.setConfig("");
        kccRepository.save(kccEntity);

        //2.3 。添加 table 表
        TableEntity tableEntity = new TableEntity();
        // 判断，表是否存在
        isExist = tableRepository.existsByTableName(tableName);
        if (isExist) {
            tableEntity = tableRepository.findOneByTableName(tableName);
        }
        tableEntity.setBusinessId(model.getBusinessId());
        tableEntity.setName(model.getTableComment());
        tableEntity.setTableName(model.getTableName());
        tableEntity.setSource((byte) 3);
        tableEntity.setStatus((byte) 0);
        tableEntity.setSyncType((byte) 1);
        tableEntity.setTunnelId(entity.getId().intValue());
        tableRepository.save(tableEntity);

        //3 创建 kafka 抽取通道
        // 调用kafka connector http 接口,将 mysql 实时读取到 kafka 中
        KafkaConnectorResp resp = kafkaService.CreateMySqlSourceConnector(config);
        log.warn("kafka connector 启动");

        // 更新 kafka-connector 配置  设置 配置
        kccEntity.setConfig(resp.getConfigString());
        kccRepository.save(kccEntity);

        //3.1 调用spark 服务 生成 sink
        // 创建spark 通道
        log.warn("初始化 spark 通道 ");
        // 生成spark通道模型
        DataTunnelModel dtModel = new DataTunnelModel();
        dtModel.setBootstrapServer(kafkaProperty.getBootstrapServerList());
        dtModel.setTableName(model.getTableName());
        dtModel.setTopicName(getTopicNameBy(config.getServerName(),config.getDbName(), sourceTableName));
        dtModel.setMappingConf(model.getMappingConf().stream().map(conf->{
            DTMappingModel m = new DTMappingModel();
            m.setField(conf.getField());
            m.setSourceField(conf.getSourceField());
            m.setType(conf.getType());
            m.setComment(conf.getComment());
            m.setPriKey(conf.getPriKey());
            m.setDbField(conf.getDbField());
            m.setUpdateComment(conf.getUpdateComment());
            return m;
        }).collect(Collectors.toList()));
        log.info(dtModel.getMappingConf().toString());
        log.info(dtModel.getBootstrapServers());
        tunnelService.initTunnel(dtModel);
        return tableEntity.getId();
    }


    @Override
    @Transactional
    public String getTunnelInfo(Long id) throws ApplicationException {
        Optional<TableTunnelEntity>  opEntity = tableTunnelRepository.findById(id);
        if (!opEntity.isPresent()) {
            throw  new ApplicationException("通道已被删除");
        }
        TableTunnelEntity entity = opEntity.get();
        String tableName = entity.getTargetTableName();
        String info = tunnelService.getTunnelInfo(tableName);
        if (info.isEmpty()){
            throw new ApplicationException("获取通道失败");
        }
        return info;
    }

    // 删除管道
    @Override
    @Transactional
    public String deleteTunnel(Long id) throws ApplicationException{
        Optional<TableTunnelEntity> optionalTableTunnelEntity = tableTunnelRepository.findById(id);
        if (!optionalTableTunnelEntity.isPresent()){
            throw new ApplicationException("通道不存在");
        }


        //查询kafka通道
        Optional<KccEntity> optionalKccEntity = kccRepository.findByTunnelId(id);
        if (optionalKccEntity.isPresent()){
            KccEntity kccEntity = optionalKccEntity.get();

            // 删除 kafka connector
            kafkaService.delConnector(kccEntity.getName());

            // 更新为删除
            kccEntity.setDeleteStatus();
            kccRepository.save(kccEntity);

            //删除spark通道 ,通过表名删除
            tunnelService.stopTunnel(optionalTableTunnelEntity.get().getTargetTableName());

            // 删除相关topic
            // topic 等通道删除后删
            kafkaService.deleteTopics(kccEntity.getServerName());

            // 删除通道表
            tableTunnelRepository.deleteById(id);
            //删除表对应的管道信息
            tableRepository.deleteTunnelIdBy(id.intValue());
        } else {
            return "通道不存在， 或已被删除";
        }
        return "";
    }

    @Override
    @Transactional
    public String stopTunnel(Long id) throws ApplicationException{
        Optional<TableTunnelEntity> optionalTableTunnelEntity = tableTunnelRepository.findById(id);
        if (!optionalTableTunnelEntity.isPresent()){
            throw new ApplicationException("通道不存在");
        }

        TableTunnelEntity tableTunnelEntity = optionalTableTunnelEntity.get();
        if (tableTunnelEntity.getStatus()==2){
            throw new ApplicationException("通道已停止");
        }

        //查询kafka通道
        Optional<KccEntity> optionalKccEntity = kccRepository.findByTunnelId(id);
        if (optionalKccEntity.isPresent()){
            KccEntity kccEntity = optionalKccEntity.get();
            String kafkaName = kccEntity.getName();
            // 删除kafka connector
            kafkaService.pauseConnector(kafkaName);
        }


        //删除spark通道 ,通过表名删除
        tunnelService.stopTunnel(tableTunnelEntity.getTargetTableName());

        // 状态变更
        tableTunnelEntity.setDisable();
        tableTunnelRepository.save(tableTunnelEntity);
        return "";
    }


    @Override
    @Transactional
    public String resumeTunnel(Long id) throws ApplicationException{
        Optional<TableTunnelEntity> optionalTableTunnelEntity = tableTunnelRepository.findById(id);
        if (!optionalTableTunnelEntity.isPresent()){
            throw new ApplicationException("通道不存在");
        }

        TableTunnelEntity tableTunnelEntity = optionalTableTunnelEntity.get();
        if (tableTunnelEntity.getStatus()==1){
            throw new ApplicationException("通道运行中");
        }

        //查询kafka通道
        Optional<KccEntity> optionalKccEntity = kccRepository.findByTunnelId(id);
        if (!optionalKccEntity.isPresent()){
            throw new ApplicationException("入通道不存在");
        }

        KccEntity kccEntity = optionalKccEntity.get();
        String kafkaName = kccEntity.getName();
        kafkaService.resumeConnector(kafkaName);

        //spark通道 重启生成
        String conf = tableTunnelEntity.getMappingConf();
        Gson gson = new Gson();
        Type type = new TypeToken<ArrayList<DTMappingModel>>() {}.getType();
        List<DTMappingModel> models = gson.fromJson(conf, type);

        DataTunnelModel dtModel = new DataTunnelModel();
        dtModel.setMappingConf(models);
        dtModel.setBootstrapServer(kafkaProperty.getBootstrapServerList());
        dtModel.setTableName(tableTunnelEntity.getTargetTableName());
        dtModel.setTopicName(getTopicNameBy(kccEntity.getServerName(),kccEntity.getDbName(), tableTunnelEntity.getSourceTableName()));
        tunnelService.initTunnel(dtModel);

        // 状态变更
        tableTunnelEntity.setStart();
        tableTunnelRepository.save(tableTunnelEntity);
        return "";
    }

    private KCMySqlSourceConfig createMySqlSourceConfig(DatasourceEntity dsEntity,String tableName){
        // 生成唯一id
        long id = IDWorker.builder().nextId();

        // 配置 kafka source connect 参数
        KCMySqlSourceConfig config = new KCMySqlSourceConfig();
        //MARK: 3306 默认mysql端口
        config.setHostName(dsEntity.getHost());
        config.setPort("3306");
        config.setUser(dsEntity.getUsername());
        config.setPassword(dsEntity.getPassword());

        // 配置 库
        config.setName("mysql_source_"+id);
        config.setDbName(dsEntity.getDatabase());
        config.setServerId(String.valueOf(id));
        config.setServerName(String.valueOf(id));
        config.setTableNames(new String[]{tableName});
        // 配置kafka
        config.setBootstrapServer(kafkaProperty.getBootstrapServer());
        return config;
    }

    // 通过表名生成 kafka topic name
    private String getTopicNameBy(String serverName, String dbName,  String tableName){
        return serverName.concat(".").concat(dbName).concat(".").concat(tableName);
    }
}
