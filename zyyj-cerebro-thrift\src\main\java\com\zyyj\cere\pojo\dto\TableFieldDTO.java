package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.sdk.processor.annotation.ThriftPaged;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@ThriftStruct
@Setter
@NoArgsConstructor
@ToString
@Builder
public class TableFieldDTO {
    private String field;
    private String comment;
    private String  type;
    private Byte priKey;

    @ThriftConstructor
    public TableFieldDTO(String field, String comment, String type, Byte priKey) {
        this.field = field;
        this.comment = comment;
        this.type = type;
        this.priKey = priKey;
    }

    @ThriftField(1)
    public String getField() {
        return field;
    }

    @ThriftField(2)
    public String getComment() {
        return comment;
    }

    @ThriftField(3)
    public String getType() {
        return type;
    }

    @ThriftField(4)
    public Byte getPriKey() {
        return priKey;
    }
}
