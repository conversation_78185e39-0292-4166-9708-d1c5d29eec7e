#!/bin/bash

# =============================================================================
# 修复192.168.121.157服务器上的Spark Master配置
# =============================================================================

echo "修复Spark Master配置..."

# 设置环境变量
export JAVA_HOME="/usr/lib/jvm/java-8-openjdk-amd64"
export SPARK_HOME="/opt/spark"
export PATH=$JAVA_HOME/bin:$SPARK_HOME/bin:$PATH

# 停止现有的Spark Master
echo "停止现有的Spark Master..."
pkill -f "spark.*Master" || true
sleep 3

# 启动Spark Master，监听所有网络接口
echo "启动Spark Master (监听所有网络接口)..."
nohup $SPARK_HOME/bin/spark-class org.apache.spark.deploy.master.Master \
    --host 0.0.0.0 --port 7077 --webui-port 8080 \
    > /var/log/spark/master.log 2>&1 &

sleep 5

# 检查状态
echo "检查Spark Master状态..."
jps | grep Master
netstat -tlnp | grep :7077

echo "修复完成！"
