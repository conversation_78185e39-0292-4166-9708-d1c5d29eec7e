server:
  port: 8081

eureka:
  instance:
    prefer-ip-address: true
    non-secure-port: 8081
    appname: ZYYJ-SPARK-THRIFT
  client:
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://admin:admin123@***************:8761/eureka/

spring:
  application:
    name: ZYYJ-SPARK-THRIFT
  main:
    allow-bean-definition-overriding: true
  redis:
    database: 1
    host: **************
    port: 6379
    password: Vhzu4U5MZJw2lmc
    timeout: 60000
zyyj:
  rpc:
    thrift:
      server:
        listen_port: 9009
spark:
  master: spark://***************:7077
  appName: ZYYJ-SPARK
  warehouseDir: hdfs://***************:9000/spark-warehouse
  metastoreUris: thrift://***************:9083
  driver: localhost
  sparkYarnDistJars:
    - hdfs://***************:9000/jars/io.delta_delta-core_2.12-0.7.0.jar
  sparkJars:
    - hdfs://***************:9000/jars/mysql-connector-java*.jar
    - hdfs://***************:9000/jars/delta-core_2.12-0.7.0.jar
