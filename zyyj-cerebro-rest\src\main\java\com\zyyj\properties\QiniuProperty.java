package com.zyyj.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties("qiniu")
public class QiniuProperty {
    private String bucket;
    private String accessKey;
    private String secretKey;

    public String getBucket() {
        return bucket;
    }

    public QiniuProperty setBucket(String bucket) {
        this.bucket = bucket;
        return this;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public QiniuProperty setAccessKey(String accessKey) {
        this.accessKey = accessKey;
        return this;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public QiniuProperty setSecretKey(String secretKey) {
        this.secretKey = secretKey;
        return this;
    }
}
