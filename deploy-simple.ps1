# =============================================================================
# 简化版一键部署脚本 - 部署到***************服务器
# =============================================================================

param(
    [string]$ServerIP = "***************",
    [string]$Username = "root"
)

function Log-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Log-Warn {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Log-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Log-Step {
    param([string]$Message)
    Write-Host "[STEP] $Message" -ForegroundColor Blue
}

# 检查本地文件
function Check-LocalFiles {
    Log-Step "检查本地脚本文件..."
    
    $requiredFiles = @(
        "check-server-157.sh",
        "install-hadoop-spark-157.sh", 
        "start-hadoop-spark-157.sh",
        "stop-hadoop-spark-157.sh"
    )
    
    $missingFiles = @()
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            $missingFiles += $file
        }
    }
    
    if ($missingFiles.Count -gt 0) {
        Log-Error "缺少以下脚本文件:"
        $missingFiles | ForEach-Object { Write-Host "  - $_" }
        return $false
    }
    
    Log-Info "✅ 所有脚本文件已准备就绪"
    return $true
}

# 测试SSH连接
function Test-SSHConnection {
    Log-Step "测试SSH连接..."
    
    try {
        $result = ssh -o ConnectTimeout=5 "${Username}@${ServerIP}" "echo 'SSH连接测试成功'" 2>$null
        if ($result -like "*SSH连接测试成功*") {
            Log-Info "✅ SSH连接正常"
            return $true
        } else {
            Log-Error "❌ SSH连接失败"
            return $false
        }
    } catch {
        Log-Error "❌ SSH连接异常: $($_.Exception.Message)"
        return $false
    }
}

# 上传脚本文件
function Upload-Scripts {
    Log-Step "上传脚本文件到服务器..."
    
    $scripts = @(
        "check-server-157.sh",
        "install-hadoop-spark-157.sh",
        "start-hadoop-spark-157.sh", 
        "stop-hadoop-spark-157.sh"
    )
    
    foreach ($script in $scripts) {
        try {
            Log-Info "上传 $script..."
            scp $script "${Username}@${ServerIP}:/root/"
            if ($LASTEXITCODE -ne 0) {
                Log-Error "上传 $script 失败"
                return $false
            }
        } catch {
            Log-Error "上传 $script 异常: $($_.Exception.Message)"
            return $false
        }
    }
    
    # 设置执行权限
    Log-Info "设置脚本执行权限..."
    ssh "${Username}@${ServerIP}" "chmod +x /root/*.sh"
    
    Log-Info "✅ 脚本文件上传完成"
    return $true
}

# 执行环境检查
function Execute-EnvironmentCheck {
    Log-Step "执行服务器环境检查..."
    
    Log-Info "运行环境检查脚本..."
    ssh "${Username}@${ServerIP}" "/root/check-server-157.sh"
    
    if ($LASTEXITCODE -eq 0) {
        Log-Info "✅ 环境检查完成"
        return $true
    } else {
        Log-Warn "环境检查有警告，但继续执行"
        return $true
    }
}

# 主函数
function Main {
    Write-Host "=== *************** 服务器一键部署脚本 ===" -ForegroundColor Cyan
    Write-Host "服务器: $ServerIP"
    Write-Host "用户: $Username"
    Write-Host ""
    
    # 检查本地文件
    if (-not (Check-LocalFiles)) {
        return
    }
    
    # 测试SSH连接
    if (-not (Test-SSHConnection)) {
        Log-Error "无法连接到服务器，请检查:"
        Log-Error "1. 服务器IP地址是否正确"
        Log-Error "2. SSH密钥是否配置"
        Log-Error "3. 用户名是否正确"
        Log-Error "4. 服务器是否在线"
        return
    }
    
    # 上传脚本
    if (-not (Upload-Scripts)) {
        return
    }
    
    # 执行环境检查
    Execute-EnvironmentCheck
    
    Write-Host ""
    Write-Host "=== 第一阶段完成 ===" -ForegroundColor Green
    Write-Host "脚本已上传到服务器，现在需要手动执行后续步骤："
    Write-Host ""
    Write-Host "1. SSH到服务器："
    Write-Host "   ssh $Username@$ServerIP"
    Write-Host ""
    Write-Host "2. 检查环境（已执行）："
    Write-Host "   ./check-server-157.sh"
    Write-Host ""
    Write-Host "3. 如果需要安装Hadoop/Spark："
    Write-Host "   ./install-hadoop-spark-157.sh"
    Write-Host ""
    Write-Host "4. 启动服务："
    Write-Host "   ./start-hadoop-spark-157.sh"
    Write-Host ""
    Write-Host "5. 检查状态："
    Write-Host "   ./check-status.sh"
    Write-Host ""
}

# 执行主函数
Main
