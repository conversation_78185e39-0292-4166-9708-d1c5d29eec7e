package com.zyyj.cere.service;


import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.zyyj.cere.pojo.body.TableAddBody;
import com.zyyj.cere.pojo.dto.TableListDTO;
import com.zyyj.cere.pojo.dto.TableStructDTO;
import com.zyyj.cere.pojo.entity.TableEntity;
import com.zyyj.cere.pojo.resp.TableTunnelUpdateInfoResp;
import com.zyyj.domain.exception.ApplicationException;

import java.util.List;
import java.util.Map;

@ThriftService
public interface TableService {

    /**
     * 数据表列表
     *
     * @param status
     * @param key
     * @param business_id
     * @return
     */
    @ThriftMethod
    List<TableListDTO> getTableList(String status, String key, Long business_id);

    /**
     * 导入/手动创建物理表
     *
     * @param tb   消息体
     * @param data 导入数据json字符串
     * @return
     */
    @ThriftMethod
    TableEntity setTableByManual(TableAddBody tb, List<List<String>> data);

    /**
     * 获取表数据
     *
     * @param tableName
     */
    @ThriftMethod
    List<String> getTableData(String tableName);

    /**
     * 获取表结构
     *
     * @param id
     * @return
     */
    @ThriftMethod
    List<TableStructDTO> getTableStruct(Long id);

    /**
     * 查看表结构
     *
     * @param tableName
     * @return
     */
    @ThriftMethod
    List<TableStructDTO> getTablePreview(String tableName);

    /**
     * 表重命名
     *
     * @param t
     * @return
     */
    @ThriftMethod
    void renameTable(TableEntity t);

    /**
     * 表移动
     *
     * @param t
     */
    @ThriftMethod
    void moveTable(TableEntity t);

    /**
     * 表删除
     *
     * @param id
     */
    @ThriftMethod
    void removeTable(Long id);

    /**
     * 发布/审核
     *
     * @param id,status
     */
    @ThriftMethod
    void operationTable(int id, int status);

    /**
     * 数据预览
     *
     * @param id
     * @param page
     * @param size
     * @return
     */
    @ThriftMethod
    Map<String, String> previewTable(Long id, String order, Integer page, Integer size);


    /* 获取 table 更新信息 */
    @ThriftMethod
    TableTunnelUpdateInfoResp getUpdateInfo(Long id) throws ApplicationException;
}
