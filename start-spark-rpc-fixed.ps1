# 停止现有进程
Write-Host "正在停止现有 Spark RPC 服务..."
Get-Process | Where-Object {$_.ProcessName -eq "java" -and $_.CommandLine -like "*zyyj-spark-rpc-server*"} | ForEach-Object {
    Stop-Process -Id $_.Id -Force
    Write-Host "已停止进程 ID: $($_.Id)"
}

# 等待进程完全停止
Start-Sleep -Seconds 3

# 设置环境变量
$env:JAVA_HOME = "C:\Program Files\Java\jdk1.8.0_141"
$env:HADOOP_USER_NAME = "root"

# 添加主机名映射到hosts文件
$hostsPath = "C:\Windows\System32\drivers\etc\hosts"
$hostsContent = Get-Content $hostsPath
if (-not ($hostsContent -match "kai12")) {
    Write-Host "添加kai12主机名映射到hosts文件..."
    Add-Content -Path $hostsPath -Value "`n*************** kai12" -Force
}

Write-Host "正在启动 Spark RPC 服务 (修复版)..."

$process = Start-Process -FilePath "$env:JAVA_HOME\bin\java" -ArgumentList `
    "-Xms512m",
    "-Xmx1g",
    "-Dspark.network.timeout=300s",
    "-Dspark.sql.execution.arrow.pyspark.enabled=false",
    "-Dspark.serializer.objectStreamReset=100",
    "-Dspark.rpc.askTimeout=300s",
    "-Dspark.rpc.lookupTimeout=300s",
    "-Dserver.port=8081",
    "-Dspring.profiles.active=dev",
    "-Dspring.application.name=ZYYJ-SPARK-THRIFT",
    "-Deureka.instance.appname=ZYYJ-SPARK-THRIFT",
    "-Deureka.instance.non-secure-port=8081",
    "-Deureka.client.serviceUrl.defaultZone=http://admin:admin123@***************:8761/eureka/",
    "-Dspring.main.allow-bean-definition-overriding=true",
    # 修改这里，使用主机名而不是IP
    "-Dspark.master=spark://kai12:7077",
    "-Dspark.appName=ZYYJ-SPARK-RPC-SERVER",
    "-Dspark.warehouseDir=hdfs://kai12:9000/spark-warehouse",
    "-Dspark.metastoreUris=thrift://kai12:9083",
    "-Dspark.driver=localhost",
    "-Dspark.driver.bindAddress=0.0.0.0",
    "-Dzyyj.rpc.thrift.server.listen_port=9009",
    # 用户映射和安全配置
    "-DHADOOP_USER_NAME=root",
    "-Duser.name=root",
    "-Dhadoop.security.authentication=simple",
    "-Dspark.hadoop.hadoop.security.authentication=simple",
    "-Dspark.hadoop.hadoop.security.authorization=false",
    "-jar","E:\dev\project\zyyj-cerebro\zyyj-spark-rpc-server\target\zyyj-spark-rpc-server.jar" `
    -RedirectStandardOutput "logs\spark-rpc.log" `
    -RedirectStandardError "logs\spark-rpc-error.log" `
    -PassThru

Write-Host "Spark RPC 服务已启动，进程 ID: $($process.Id)"
Write-Host "日志文件: logs\spark-rpc.log"
Write-Host "错误日志: logs\spark-rpc-error.log"