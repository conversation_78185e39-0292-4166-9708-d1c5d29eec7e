package com.zyyj.spark.service;


import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.zyyj.domain.exception.ApplicationException;

import java.util.List;
import java.util.Map;

@ThriftService
public interface DeltaService {

    /**
     * 创建数据库
     *
     * @param name
     */
    @ThriftMethod
    void createDatabase(String name);

    /**
     * 建表
     *
     * @return
     * @throws ApplicationException
     */
    @ThriftMethod
    void createTable(String sql);

    /**
     * 建表--通过List Json 格式的数据
     *
     * @param tableName
     * @param data
     */
    @ThriftMethod
    void createTableByJson(String tableName, String data);

    /**
     * 建视图
     *
     * @param sql
     */
    @ThriftMethod
    void createView(String viewName, String sql);

    /**
     * 获取表数据
     *
     * @param tableName
     * @return
     */
    @ThriftMethod
    List<String> getTableList(String tableName);

    /**
     * 获取表结构
     *
     * @param tableName
     * @return
     */
    @ThriftMethod
    List<String> getTableStruct(String tableName);

    /**
     * 获取数据列表--sql语句
     *
     * @param sql
     * @return
     */
    @ThriftMethod
    List<String> getTableListBySql(String sql);

    /**
     * 根据查询sql查询表头--sql语句
     *
     * @param sql
     * @return
     */
    @ThriftMethod
    List<String> getTableStructBySql(String sql);

    /**
     * 批量插入数据---通过List json格式的数据
     *
     * @param tableName
     * @param data
     */
    @ThriftMethod
    void insertTableByList(String tableName, String data);

    /**
     * 重命名
     *
     * @param oldName
     * @param newName
     */
    @ThriftMethod
    void renameTable(String oldName, String newName);

    /**
     * 删除表/视图
     *
     * @param tableName,isView
     */
    @ThriftMethod
    void removeTable(String tableName, boolean isView);

    /**
     * 执行sql
     *
     * @param sql
     */
    @ThriftMethod
    void execSql(String sql);

    /**
     * 查询sql
     *
     * @param sql
     */
    @ThriftMethod
    List<String> querySql(String sql);

    /**
     * 数据预览
     *
     * @param tableName
     */
    @ThriftMethod
    Map<String, String> preview(String tableName, String order, Integer page, Integer size);

    /**
     * 通过 Sql 数据预览
     *
     * @param sql
     */
    @ThriftMethod
    Map<String, String> previewBySql(String sql, String order, Integer page, Integer size);

    // 查询表是否存在
    @ThriftMethod
    boolean queryTableIsExist(String tableName);

}
