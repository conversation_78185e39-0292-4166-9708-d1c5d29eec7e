package com.zyyj.utils;


import com.zyyj.utils.excel.ZYCSVUtil;
import com.zyyj.utils.excel.ZYExcelUtil;
import com.zyyj.utils.excel.ZYTableData;
import junit.framework.TestCase;
import org.junit.Test;
import org.springframework.core.io.ClassPathResource;

import java.io.*;
import java.text.DecimalFormat;
import java.util.List;

public class ZYExcelUtilTest extends TestCase {


    @Test
    public void testFileType() throws FileNotFoundException {
        File file = new File("/Users/<USER>/Work/zyyj-cerebro/zyyj-cerebro-rest/src/test/java/com/zyyj/utils/1.xlsx");
        InputStream is = new BufferedInputStream(new FileInputStream(file));
        ZYExcelUtil util = ZYExcelUtil.builder();
        System.out.println(util.isExcelFile(is));
    }



    @Test
    public void testExcel() throws Exception {
        File file = new ClassPathResource("zfiles/1.xlsx").getFile();
        InputStream is = new FileInputStream(file);

        ZYExcelUtil util = ZYExcelUtil.builder();
        List<ZYTableData> list = util.getExcelData(is);

        for (int i=0; i<list.size();i++){
            ZYTableData d = list.get(i);

            for (int j=0; j<d.getData().size(); j++){
                List<String> list1 = d.getData().get(j);

                for (int k=0; k<list1.size(); k++) {
                    String  cell =  list1.get(k);
                    System.out.println(cell);
                }
            }
        }
    }

    @Test
    public void testExcel1() throws Exception {
        String v =  1111111111111.1111111111 + "";
        System.out.println(v);


        ZYExcelUtil util = ZYExcelUtil.builder();
//        List<ZYTableData> list = util.getExcelData("http://file-hub.online.zyyj.com.cn/excel/20201021/school.xlsx");
        List<ZYTableData> list = util.getExcelData("http://file-hub.online.zyyj.com.cn/excel/physicaltable.xlsx");

        for (int i=0; i<list.size();i++){
            ZYTableData d = list.get(i);

            for (int j=0; j<d.getData().size(); j++){
                List<String> list1 = d.getData().get(j);

                for (int k=0; k<list1.size(); k++) {
                    String  cell =  list1.get(k);
                    System.out.print(cell);
                    System.out.print("   ");
                }
                System.out.println("");
            }
        }
    }

    @Test
    public void testCSV() throws Exception {
        File file = new ClassPathResource("zfiles/1.csv").getFile();

        InputStream is = new FileInputStream(file);

        ZYTableData d = ZYCSVUtil.builder().getData(is);

        System.out.println(d.getData());

//        System.out.println( ZYCSVUtil.builder().getData(is) );
    }
}