eureka:
  instance:
    prefer-ip-address: true
    non-secure-port: 8005
    appname: ZYYJ-CEREBRO-REST
  client:
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: ******************************************/eureka/

# actuator security switch
management:
  security:
    enabled: false

server:
  port: 8005

spring:
  application:
    name: ZYYJ-CEREBRO-REST
  main:
    allow-bean-definition-overriding: true
  redis:
    database: 1
    host: **************
    port: 6379
    password: Vhzu4U5MZJw2lmc
    timeout: 60000# 在 REST API 服务的配置中添加
zyyj:
  rpc:
    thrift:
      client:
        cerebro-rpc:
          service-id: ZYYJ-CEREBRO-THRIFT
          path: /thrift
          port: 9006  # 确保这是正确的端口
        spark-rpc:
          service-id: ZYYJ-SPARK-THRIFT
          path: /thrift
          port: 9009  # 确保这是正确的端口
logging:
  level:
    root: INFO
    com.facebook.swift: DEBUG
    org.apache.thrift: DEBUG
    com.zyyj.spark: DEBUG
    com.zyyj.rpc: DEBUG
    org.apache.tomcat: DEBUG
    org.apache.catalina: DEBUG

service:
  #thrift 客户端配置(demo)
  cerebro: ZYYJ-CEREBRO-THRIFT
scan:
  package: com.zyyj.controller
