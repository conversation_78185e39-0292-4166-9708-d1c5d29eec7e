package com.zyyj.cere.repository;

import com.zyyj.cere.pojo.entity.TableModelingEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface TableModelingRepository extends JpaRepository<TableModelingEntity, Long>, JpaSpecificationExecutor<TableModelingEntity> {

    @Query(value = "SELECT dataSql FROM TableModelingEntity WHERE id=?1")
    String getSql(Long id);

    List<TableModelingEntity> findByName(String name);

    List<TableModelingEntity> findByTableName(String tableName);

    @Query(value = "SELECT * FROM table_modeling tm  WHERE tm .id != ?1 AND tm.name = ?2", nativeQuery = true)
    List<TableModelingEntity> queryByIdAndName(Long id, String name);

    @Query(value = "SELECT * FROM table_modeling tm  WHERE tm .id != ?1 AND tm.table_name = ?2", nativeQuery = true)
    List<TableModelingEntity> queryByIdAndTableName(Long id, String tableName);
}
