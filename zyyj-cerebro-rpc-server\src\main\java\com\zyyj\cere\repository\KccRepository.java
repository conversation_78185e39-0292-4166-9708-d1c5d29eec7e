package com.zyyj.cere.repository;


import com.zyyj.cere.pojo.entity.KccEntity;
import com.zyyj.cere.pojo.entity.TableEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;


public interface KccRepository extends JpaRepository<KccEntity, Long>, JpaSpecificationExecutor<TableEntity> {

    Optional<KccEntity> findByTunnelId(Long tunnelId);

}