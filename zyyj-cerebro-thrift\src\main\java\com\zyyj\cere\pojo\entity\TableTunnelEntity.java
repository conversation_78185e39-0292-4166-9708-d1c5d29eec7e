package com.zyyj.cere.pojo.entity;


import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@ThriftStruct
@Setter
@NoArgsConstructor
@ToString
@Builder
@Entity
@Table(name = "`table_tunnel`")
@EntityListeners(TableTunnelEntity.class)
public class TableTunnelEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "`id`", nullable = false)
    private Long id;

    /* datasource 表 id*/
    @Column(name = "`datasource_id`", nullable = false)
    private Long datasourceId;

    /* 来源表 */
    @Column(name = "`source_table_name`", nullable = false)
    private String sourceTableName;

    /* 目标表 */
    @Column(name = "`target_table_name`", nullable = false)
    private String targetTableName;

    /* 表中文名 */
    @Column(name = "`name`", nullable = false)
    private String name;

    /*
    * 映射规则
    * 1 同行 2 同名
    *  */
    @Column(name = "`mapping_type`", nullable = false)
    private Integer mappingType;


    /*
     * 映射配置
     * [
     *    {
     *       "source_field":"来源字段",
     *       "field":"name",
     *       "comment":"字段标题",
     *       "type":"varchar",
     *       "priKey":1
     *     }
     * ]
     *  */
    @Column(name = "`mapping_conf`", nullable = false)
    private String mappingConf;


    // 1 正常 2 停用 3 删除
    @Column(name = "`status`", nullable = false)
    @Builder.Default
    private Byte status=1;


    @ThriftConstructor
    public TableTunnelEntity(Long id, Long datasourceId, String sourceTableName, String targetTableName, String name, Integer mappingType, String mappingConf, Byte status) {
        this.id = id;
        this.datasourceId = datasourceId;
        this.sourceTableName = sourceTableName;
        this.targetTableName = targetTableName;
        this.name = name;
        this.mappingType = mappingType;
        this.mappingConf = mappingConf;
        this.status = status;
    }



    @ThriftField(1)
    public Long getId() {
        return id;
    }

    @ThriftField(2)
    public Long getDatasourceId() {
        return datasourceId;
    }

    @ThriftField(3)
    public String getSourceTableName() {
        return sourceTableName;
    }

    @ThriftField(4)
    public String getTargetTableName() {
        return targetTableName;
    }

    @ThriftField(5)
    public String getName() {
        return name;
    }

    @ThriftField(6)
    public Integer getMappingType() {
        return mappingType;
    }

    @ThriftField(7)
    public String getMappingConf() {
        return mappingConf;
    }

    @ThriftField(8)
    public Byte getStatus() {
        return status;
    }

    public void setDisable(){
        this.setStatus(Byte.valueOf((byte) 2));
    }

    public void setStart(){
        this.setStatus(Byte.valueOf((byte) 1));
    }

    public String getStatusName() {
        switch (status){
            case 1:
                return "已启用";
            case 2:
                return "已停用";
            default:
                return "";
        }
    }

    public boolean isDelete(){
        return status == 3;
    }
}
