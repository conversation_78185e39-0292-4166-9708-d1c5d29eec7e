#!/bin/bash

# =============================================================================
# 简化版***************服务器检查脚本
# =============================================================================

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查系统基本信息
check_system_info() {
    log_step "检查系统基本信息..."
    
    echo "=== 系统信息 ==="
    echo "操作系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2 2>/dev/null || echo '未知')"
    echo "主机名: $(hostname)"
    echo "IP地址: $(hostname -I | awk '{print $1}' 2>/dev/null || echo '未知')"
    echo "当前用户: $(whoami)"
    echo ""
    
    echo "=== 资源信息 ==="
    echo "CPU核心数: $(nproc 2>/dev/null || echo '未知')"
    echo "内存信息:"
    free -h 2>/dev/null || echo "无法获取内存信息"
    echo ""
    echo "磁盘空间:"
    df -h / 2>/dev/null || echo "无法获取磁盘信息"
    echo ""
}

# 检查Java环境
check_java_environment() {
    log_step "检查Java环境..."
    
    if command -v java >/dev/null 2>&1; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1)
        log_info "✅ Java已安装: $JAVA_VERSION"
        
        if [ -n "$JAVA_HOME" ]; then
            log_info "✅ JAVA_HOME已设置: $JAVA_HOME"
        else
            log_warn "❌ JAVA_HOME未设置"
            # 尝试自动检测
            if command -v java >/dev/null 2>&1; then
                JAVA_PATH=$(which java 2>/dev/null)
                if [ -n "$JAVA_PATH" ]; then
                    JAVA_HOME_GUESS=$(readlink -f "$JAVA_PATH" 2>/dev/null | sed "s:/bin/java::" 2>/dev/null)
                    if [ -n "$JAVA_HOME_GUESS" ]; then
                        log_info "建议设置: export JAVA_HOME=$JAVA_HOME_GUESS"
                    fi
                fi
            fi
        fi
    else
        log_error "❌ Java未安装"
        log_info "请先安装Java 8:"
        log_info "  apt update && apt install -y openjdk-8-jdk"
    fi
    echo ""
}

# 检查/root目录下的安装包
check_installation_packages() {
    log_step "检查/root目录下的安装包..."
    
    echo "=== /root目录内容 ==="
    ls -la /root/ 2>/dev/null || echo "无法访问/root目录"
    echo ""
    
    # 检查Hadoop安装包
    HADOOP_PACKAGES=$(ls /root/ 2>/dev/null | grep -i hadoop || true)
    if [ -n "$HADOOP_PACKAGES" ]; then
        log_info "✅ 发现Hadoop安装包:"
        echo "$HADOOP_PACKAGES" | while read package; do
            echo "  - $package"
        done
    else
        log_warn "❌ 未发现Hadoop安装包"
    fi
    
    # 检查Spark安装包
    SPARK_PACKAGES=$(ls /root/ 2>/dev/null | grep -i spark || true)
    if [ -n "$SPARK_PACKAGES" ]; then
        log_info "✅ 发现Spark安装包:"
        echo "$SPARK_PACKAGES" | while read package; do
            echo "  - $package"
        done
    else
        log_warn "❌ 未发现Spark安装包"
    fi
    echo ""
}

# 检查已安装的服务
check_installed_services() {
    log_step "检查已安装的服务..."
    
    # 检查Hadoop
    if command -v hadoop >/dev/null 2>&1; then
        HADOOP_VERSION=$(hadoop version 2>/dev/null | head -n 1 || echo "版本获取失败")
        log_info "✅ Hadoop已安装: $HADOOP_VERSION"
        log_info "   HADOOP_HOME: ${HADOOP_HOME:-未设置}"
        log_info "   Hadoop路径: $(which hadoop 2>/dev/null || echo '未知')"
    else
        log_warn "❌ Hadoop未安装或未在PATH中"
    fi
    
    # 检查Spark
    if command -v spark-submit >/dev/null 2>&1; then
        SPARK_VERSION=$(spark-submit --version 2>&1 | grep version | head -n 1 || echo "版本获取失败")
        log_info "✅ Spark已安装: $SPARK_VERSION"
        log_info "   SPARK_HOME: ${SPARK_HOME:-未设置}"
        log_info "   Spark路径: $(which spark-submit 2>/dev/null || echo '未知')"
    else
        log_warn "❌ Spark未安装或未在PATH中"
    fi
    echo ""
}

# 检查运行中的服务
check_running_services() {
    log_step "检查运行中的服务..."
    
    echo "=== Java进程 ==="
    if command -v jps >/dev/null 2>&1; then
        JPS_OUTPUT=$(jps 2>/dev/null || echo "")
        if [ -n "$JPS_OUTPUT" ]; then
            echo "$JPS_OUTPUT"
            
            # 分析进程
            if echo "$JPS_OUTPUT" | grep -q "NameNode"; then
                log_info "✅ HDFS NameNode 正在运行"
            else
                log_warn "❌ HDFS NameNode 未运行"
            fi
            
            if echo "$JPS_OUTPUT" | grep -q "Master"; then
                log_info "✅ Spark Master 正在运行"
            else
                log_warn "❌ Spark Master 未运行"
            fi
        else
            log_warn "❌ 没有Java进程在运行"
        fi
    else
        log_warn "❌ jps命令不可用"
        # 尝试使用ps命令
        JAVA_PROCESSES=$(ps aux | grep java | grep -v grep || true)
        if [ -n "$JAVA_PROCESSES" ]; then
            echo "Java进程:"
            echo "$JAVA_PROCESSES"
        else
            log_warn "❌ 没有Java进程在运行"
        fi
    fi
    echo ""
}

# 检查端口监听状态
check_port_status() {
    log_step "检查端口监听状态..."
    
    echo "=== 端口监听状态 ==="
    
    check_port() {
        local port=$1
        local name=$2
        if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
            log_info "✅ $name (端口 $port) - 正在监听"
        else
            log_warn "❌ $name (端口 $port) - 未监听"
        fi
    }
    
    check_port 7077 "Spark Master"
    check_port 8080 "Spark Web UI"
    check_port 9000 "HDFS NameNode"
    check_port 9083 "Hive Metastore"
    check_port 8088 "YARN ResourceManager"
    check_port 9870 "HDFS Web UI"
    
    echo ""
}

# 生成建议
generate_suggestions() {
    log_step "生成建议..."
    
    echo "=== 建议的操作 ==="
    echo ""
    
    echo "1. 如果服务未安装，请运行安装脚本:"
    echo "   ./install-hadoop-spark-157.sh"
    echo ""
    
    echo "2. 如果服务已安装但未运行，请启动服务:"
    echo "   ./start-hadoop-spark-157.sh"
    echo ""
    
    echo "3. 检查服务状态:"
    echo "   jps"
    echo "   netstat -tlnp | grep -E ':7077|:8080|:9000|:9083'"
    echo ""
    
    echo "4. 访问Web界面:"
    HOST_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "localhost")
    echo "   Spark Master: http://$HOST_IP:8080"
    echo "   HDFS: http://$HOST_IP:9870"
    echo "   YARN: http://$HOST_IP:8088"
    echo ""
}

# 主函数
main() {
    echo "============================================================================="
    echo "*************** 服务器环境检查脚本 (简化版)"
    echo "============================================================================="
    echo "检查时间: $(date)"
    echo ""
    
    check_system_info
    check_java_environment
    check_installation_packages
    check_installed_services
    check_running_services
    check_port_status
    generate_suggestions
    
    echo "============================================================================="
    echo "检查完成！"
    echo "============================================================================="
}

# 执行主函数
main "$@"
