package com.zyyj.cere.utils;

import com.zyyj.cere.exception.CereExceptionEnum;
import com.zyyj.cere.pojo.entity.DatasourceEntity;
import com.zyyj.domain.util.PreChecks;
import org.junit.jupiter.api.Test;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Random;
import java.util.regex.Pattern;

/**
 * 共用方法工具类
 */
public class PublicUtils {

    /**
     * 封装一层 CheckNull
     *
     * @param reference
     * @param <T>
     */
    @SafeVarargs
    public static <T> void checkNotNullArr(T... reference) {
        for (T t : reference) {
            PreChecks.checkNotNull(t, CereExceptionEnum.PARAM_NOT_NULL);
        }
    }


    /**
     * 封装一层 checkNotEmpty
     *
     * @param reference
     */
    public static void checkNotEmptyArr(String... reference) {
        for (String s : reference) {
            PreChecks.checkNotEmpty(s, CereExceptionEnum.PARAM_NOT_NULL);
        }
    }

    /**
     * @Description: 测试连接
     * @Param: [data]
     * @return: int
     * @Author: bravelee
     * @Date: 2020/11/9
     */
    public static int testConnect(DatasourceEntity data) {
        int status = 400;//200成功 400失败
        switch (data.getTypeId().intValue()) {
            case 1://Mysql
                //加载驱动类
                try {
                    Class.forName("com.mysql.cj.jdbc.Driver");
                    // 处理host参数，如果已包含端口则直接使用，否则添加默认端口3306
                    String host = data.getHost();
                    if (!host.contains(":")) {
                        host = host + ":3306";
                    }
                    String url = "jdbc:mysql://" + host + "/" + data.getDatabase() + "?characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&serverTimezone=Asia/Shanghai";
                    System.out.println("Testing connection to: " + url);
                    DriverManager.getConnection(url, data.getUsername(), data.getPassword());
                    status = 200;
                    System.out.println("Connection test successful");
                } catch (ClassNotFoundException e) {
                    System.err.println("MySQL driver not found: " + e.getMessage());
                    e.printStackTrace();
                } catch (SQLException e) {
                    System.err.println("Database connection failed: " + e.getMessage());
                    e.printStackTrace();
                }
                break;
            case 2://Oracle
                break;
            case 3://Microsoft SQL Server
                break;
            case 4://IBM DB2
                break;
            case 5://Hsql
                break;
            default:
                break;
        }

        return status;
    }

    /**
     * @Description: 获取数据源连接
     * @Param: [data]
     * @return: int
     * @Author: bravelee
     * @Date: 2020/11/9
     */
    public static Connection getConnection(DatasourceEntity data) {
        Connection connection = null;
        switch (data.getTypeId().intValue()) {
            case 1://Mysql
                //加载驱动类
                try {
                    Class.forName("com.mysql.cj.jdbc.Driver");
                    // 处理host参数，如果已包含端口则直接使用，否则添加默认端口3306
                    String host = data.getHost();
                    if (!host.contains(":")) {
                        host = host + ":3306";
                    }
                    String url = "jdbc:mysql://" + host + "/" + data.getDatabase() + "?characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&serverTimezone=Asia/Shanghai";
                    connection = DriverManager.getConnection(url, data.getUsername(), data.getPassword());
                } catch (ClassNotFoundException e) {
                    System.err.println("MySQL driver not found: " + e.getMessage());
                    e.printStackTrace();
                } catch (SQLException e) {
                    System.err.println("Database connection failed: " + e.getMessage());
                    e.printStackTrace();
                }
                break;
            case 2://Oracle
                break;
            case 3://Microsoft SQL Server
                break;
            case 4://IBM DB2
                break;
            case 5://Hsql
                break;
            default:
                break;
        }

        return connection;
    }

    /**
     * @Description: 生成随机字符串 0-9 a-z A-Z
     * @Param: [kind, length] kind 0纯数字 1小写字母 2大写字母 3数字/小写字母/大写字母
     * @return: java.lang.String
     * @Author: bravelee
     * @Date: 2020/11/11
     */
    public static String getRandomString(int kind, int length) {
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = kind;
            if (kind == 3) {
                number = random.nextInt(3);
            }
            long result = 0;
            switch (number) {
                case 0:
                    sb.append(new Random().nextInt(10));
                    break;
                case 1:
                    result = Math.round(Math.random() * 25 + 97);
                    sb.append((char) result);
                    break;
                case 2:
                    result = Math.round(Math.random() * 25 + 65);
                    sb.append((char) result);
                    break;
            }

        }
        return sb.toString();
    }




}
