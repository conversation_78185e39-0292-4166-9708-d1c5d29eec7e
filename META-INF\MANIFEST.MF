Manifest-Version: 1.0
Implementation-Title: zyyj-cerebro-rpc-server
Implementation-Version: 1.0.0-SNAPSHOT
Built-By: Administrator
Implementation-Vendor-Id: zyyj
Class-Path: lib/spring-boot-starter-actuator-2.1.0.RELEASE.jar lib/spr
 ing-boot-actuator-autoconfigure-2.1.0.RELEASE.jar lib/spring-boot-act
 uator-2.1.0.RELEASE.jar lib/jackson-databind-2.9.7.jar lib/jackson-co
 re-2.9.7.jar lib/spring-context-5.1.2.RELEASE.jar lib/jackson-datatyp
 e-jsr310-2.9.7.jar lib/micrometer-core-1.1.0.jar lib/HdrHistogram-2.1
 .9.jar lib/LatencyUtils-2.0.3.jar lib/spring-boot-starter-web-2.1.0.R
 ELEASE.jar lib/spring-boot-starter-json-2.1.0.RELEASE.jar lib/jackson
 -datatype-jdk8-2.9.7.jar lib/jackson-module-parameter-names-2.9.7.jar
  lib/spring-boot-starter-tomcat-2.1.0.RELEASE.jar lib/tomcat-embed-co
 re-9.0.12.jar lib/tomcat-embed-el-9.0.12.jar lib/tomcat-embed-websock
 et-9.0.12.jar lib/hibernate-validator-6.0.13.Final.jar lib/jboss-logg
 ing-3.3.2.Final.jar lib/classmate-1.4.0.jar lib/spring-web-5.1.2.RELE
 ASE.jar lib/spring-beans-5.1.2.RELEASE.jar lib/spring-webmvc-5.1.2.RE
 LEASE.jar lib/spring-expression-5.1.2.RELEASE.jar lib/spring-boot-sta
 rter-log4j2-2.1.0.RELEASE.jar lib/log4j-slf4j-impl-2.11.1.jar lib/log
 4j-api-2.11.1.jar lib/log4j-core-2.11.1.jar lib/log4j-jul-2.11.1.jar 
 lib/jul-to-slf4j-1.7.25.jar lib/zyyj-thrift-boot-starter-1.0.2-SNAPSH
 OT.jar lib/zyyj-thrift-boot-autoconfigure-1.0.2-SNAPSHOT.jar lib/spri
 ng-cloud-starter-netflix-eureka-client-2.1.2.RELEASE.jar lib/spring-c
 loud-starter-2.1.2.RELEASE.jar lib/spring-cloud-context-2.1.2.RELEASE
 .jar lib/spring-security-rsa-1.0.7.RELEASE.jar lib/bcpkix-jdk15on-1.6
 0.jar lib/bcprov-jdk15on-1.60.jar lib/spring-cloud-netflix-hystrix-2.
 1.2.RELEASE.jar lib/spring-cloud-netflix-eureka-client-2.1.2.RELEASE.
 jar lib/eureka-client-1.9.12.jar lib/jettison-1.3.7.jar lib/stax-api-
 1.0.1.jar lib/netflix-eventbus-0.3.0.jar lib/netflix-infix-0.3.0.jar 
 lib/commons-jxpath-1.3.jar lib/joda-time-2.10.1.jar lib/antlr-runtime
 -3.4.jar lib/stringtemplate-3.2.1.jar lib/commons-math-2.2.jar lib/ar
 chaius-core-0.7.6.jar lib/jsr311-api-1.1.1.jar lib/servo-core-0.12.21
 .jar lib/jersey-core-1.19.1.jar lib/jersey-client-1.19.1.jar lib/jers
 ey-apache-client4-1.19.1.jar lib/httpclient-4.5.6.jar lib/guice-4.1.0
 .jar lib/aopalliance-1.0.jar lib/compactmap-1.2.1.jar lib/dexx-collec
 tions-0.2.jar lib/eureka-core-1.9.12.jar lib/woodstox-core-5.2.1.jar 
 lib/stax2-api-4.2.jar lib/spring-cloud-starter-netflix-archaius-2.1.2
 .RELEASE.jar lib/spring-cloud-netflix-ribbon-2.1.2.RELEASE.jar lib/sp
 ring-cloud-netflix-archaius-2.1.2.RELEASE.jar lib/commons-configurati
 on-1.8.jar lib/commons-lang-2.6.jar lib/spring-cloud-starter-netflix-
 ribbon-2.1.2.RELEASE.jar lib/ribbon-2.3.0.jar lib/ribbon-transport-2.
 3.0.jar lib/rxnetty-contexts-0.4.9.jar lib/rxnetty-servo-0.4.9.jar li
 b/hystrix-core-1.4.3.jar lib/rxnetty-0.4.9.jar lib/ribbon-core-2.3.0.
 jar lib/ribbon-httpclient-2.3.0.jar lib/commons-collections-3.2.2.jar
  lib/netflix-commons-util-0.1.1.jar lib/ribbon-loadbalancer-2.3.0.jar
  lib/netflix-statistics-0.1.1.jar lib/rxjava-1.3.8.jar lib/ribbon-eur
 eka-2.3.0.jar lib/xstream-1.4.10.jar lib/xmlpull-1.1.3.1.jar lib/xpp3
 _min-1.1.4c.jar lib/spring-tx-5.1.2.RELEASE.jar lib/zyyj-cerebro-thri
 ft-1.0.0-SNAPSHOT.jar lib/zyyj-domain-common-1.0.2-SNAPSHOT.jar lib/g
 son-2.8.5.jar lib/commons-collections4-4.2.jar lib/commons-beanutils-
 1.7.0.jar lib/spring-data-commons-2.1.2.RELEASE.jar lib/pinyin4j-2.5.
 0.jar lib/zyyj-rpc-thrift-common-1.0.2-SNAPSHOT.jar lib/zyyj-processo
 r-annotation-1.0.0-SNAPSHOT.jar lib/commons-lang3-3.8.1.jar lib/swift
 -annotations-0.23.1.jar lib/validation-api-2.0.1.Final.jar lib/swagge
 r-annotations-1.5.13.jar lib/zyyj-rpc-thrift-1.0.2-SNAPSHOT.jar lib/s
 wift-service-0.23.1-zyyj.jar lib/swift-codec-0.23.1.jar lib/paranamer
 -2.8.jar lib/helper-1.0.1.jar lib/libthrift-0.9.3.jar lib/httpcore-4.
 4.10.jar lib/nifty-client-0.23.0.jar lib/nifty-core-0.23.0.jar lib/gu
 ice-multibindings-4.0.jar lib/nifty-ssl-0.23.0.jar lib/netty-tcnative
 -boringssl-static-2.0.17.Final.jar lib/configuration-0.119.jar lib/cg
 lib-nodep-2.2.2.jar lib/stats-0.119.jar lib/slice-0.10.jar lib/jol-co
 re-0.1.jar lib/units-0.119.jar lib/log-0.119.jar lib/netty-3.10.5.Fin
 al.jar lib/annotations-2.0.3.jar lib/jmxutils-1.18.jar lib/commons-po
 ol2-2.6.0.jar lib/spring-cloud-commons-2.1.2.RELEASE.jar lib/spring-s
 ecurity-crypto-5.1.1.RELEASE.jar lib/guava-24.0-jre.jar lib/checker-c
 ompat-qual-2.0.0.jar lib/error_prone_annotations-2.1.3.jar lib/j2objc
 -annotations-1.1.jar lib/animal-sniffer-annotations-1.14.jar lib/zyyj
 -spark-thrift-1.0.0-SNAPSHOT.jar lib/zyyj-acc-thrift-1.0.0-SNAPSHOT.j
 ar lib/zyyj-processor-1.0.0-SNAPSHOT.jar lib/auto-service-1.0-rc4.jar
  lib/auto-common-0.8.jar lib/javapoet-1.10.0.jar lib/hibernate-jpa-2.
 1-api-1.0.0.Final.jar lib/mapper-core-1.1.1.jar lib/persistence-api-1
 .0.jar lib/mysql-connector-java-8.0.13.jar lib/spring-boot-starter-da
 ta-redis-2.1.0.RELEASE.jar lib/spring-data-redis-2.1.2.RELEASE.jar li
 b/spring-data-keyvalue-2.1.2.RELEASE.jar lib/spring-oxm-5.1.2.RELEASE
 .jar lib/spring-context-support-5.1.2.RELEASE.jar lib/lettuce-core-5.
 1.2.RELEASE.jar lib/reactor-core-3.2.2.RELEASE.jar lib/reactive-strea
 ms-1.0.2.jar lib/netty-common-4.1.29.Final.jar lib/netty-transport-4.
 1.29.Final.jar lib/netty-buffer-4.1.29.Final.jar lib/netty-resolver-4
 .1.29.Final.jar lib/netty-handler-4.1.29.Final.jar lib/netty-codec-4.
 1.29.Final.jar lib/spring-boot-starter-data-jpa-2.1.0.RELEASE.jar lib
 /spring-boot-starter-jdbc-2.1.0.RELEASE.jar lib/HikariCP-3.2.0.jar li
 b/spring-jdbc-5.1.2.RELEASE.jar lib/javax.transaction-api-1.3.jar lib
 /jaxb-api-2.3.1.jar lib/javax.activation-api-1.2.0.jar lib/hibernate-
 core-5.3.7.Final.jar lib/javax.persistence-api-2.2.jar lib/javassist-
 3.23.1-GA.jar lib/byte-buddy-1.9.3.jar lib/antlr-2.7.7.jar lib/jandex
 -2.0.5.Final.jar lib/dom4j-2.1.1.jar lib/hibernate-commons-annotation
 s-5.0.4.Final.jar lib/spring-data-jpa-2.1.2.RELEASE.jar lib/spring-or
 m-5.1.2.RELEASE.jar lib/spring-aspects-5.1.2.RELEASE.jar lib/zyyj-dom
 ain-web-1.0.2-SNAPSHOT.jar lib/poi-4.0.1.jar lib/commons-codec-1.11.j
 ar lib/commons-math3-3.6.1.jar lib/poi-ooxml-4.0.1.jar lib/poi-ooxml-
 schemas-4.0.1.jar lib/xmlbeans-3.0.2.jar lib/commons-compress-1.18.ja
 r lib/curvesapi-1.05.jar lib/commons-io-1.3.2.jar lib/jackson-annotat
 ions-2.9.0.jar lib/springfox-swagger2-2.9.2.jar lib/springfox-spi-2.9
 .2.jar lib/springfox-core-2.9.2.jar lib/springfox-schema-2.9.2.jar li
 b/springfox-swagger-common-2.9.2.jar lib/springfox-spring-web-2.9.2.j
 ar lib/spring-plugin-core-1.2.0.RELEASE.jar lib/spring-plugin-metadat
 a-1.2.0.RELEASE.jar lib/mapstruct-1.2.0.Final.jar lib/swagger-models-
 1.5.21.jar lib/springfox-swagger-ui-2.9.2.jar lib/querydsl-jpa-4.2.1.
 jar lib/querydsl-core-4.2.1.jar lib/jsr305-1.3.9.jar lib/mysema-commo
 ns-lang-0.2.4.jar lib/bridge-method-annotation-1.13.jar lib/javax.inj
 ect-1.jar lib/slf4j-api-1.7.25.jar lib/junit-jupiter-6.0.0-M2.jar lib
 /junit-jupiter-api-5.3.1.jar lib/apiguardian-api-1.0.0.jar lib/opente
 st4j-1.1.1.jar lib/junit-platform-commons-1.3.1.jar lib/junit-jupiter
 -params-5.3.1.jar lib/junit-jupiter-engine-5.3.1.jar lib/junit-platfo
 rm-engine-1.3.1.jar lib/fastjson-1.2.67.jar lib/kafka-clients-2.6.0.j
 ar lib/zstd-jni-1.4.4-7.jar lib/lz4-java-1.7.1.jar lib/snappy-java-1.
 1.7.3.jar lib/hutool-http-5.6.6.jar lib/hutool-core-5.6.6.jar lib/hut
 ool-json-5.6.6.jar lib/spring-boot-starter-2.1.0.RELEASE.jar lib/spri
 ng-boot-2.1.0.RELEASE.jar lib/spring-boot-autoconfigure-2.1.0.RELEASE
 .jar lib/javax.annotation-api-1.3.2.jar lib/spring-core-5.1.2.RELEASE
 .jar lib/spring-jcl-5.1.2.RELEASE.jar lib/snakeyaml-1.23.jar lib/lomb
 ok-1.18.2.jar lib/spring-boot-starter-aop-2.1.0.RELEASE.jar lib/sprin
 g-aop-5.1.2.RELEASE.jar lib/aspectjweaver-1.9.2.jar lib/pagehelper-sp
 ring-boot-starter-1.2.10.jar lib/mybatis-spring-boot-starter-1.3.2.ja
 r lib/mybatis-spring-boot-autoconfigure-1.3.2.jar lib/mybatis-3.4.6.j
 ar lib/mybatis-spring-1.3.2.jar lib/pagehelper-spring-boot-autoconfig
 ure-1.2.10.jar lib/pagehelper-5.1.8.jar lib/jsqlparser-1.2.jar
Created-By: Apache Maven 3.6.0
Build-Jdk: 1.8.0_141
Implementation-URL: https://projects.spring.io/spring-boot/#/spring-bo
 ot-starter-parent/zyyj-cerebro/zyyj-cerebro-rpc-server
Main-Class: com.zyyj.cere.CerebroRPCServerApplication

