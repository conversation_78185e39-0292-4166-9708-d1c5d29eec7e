package com.zyyj.controller;

import com.zyyj.cere.pojo.entity.BusinessEntity;
import com.zyyj.cere.service.BusinessService;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.utils.PublicUtils;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 业务集控制层只做设置路由和参数获取,service做逻辑处理
 *
 * <AUTHOR>
 * @date 2020/9/24 15:25
 */
@RestController
@RequestMapping("/api/v1/business")
public class BusinessController {
    @Autowired
    BusinessService businessService;

    /**
     * @Description: 业务集列表
     * @Param: []
     * @return: Map<String, List < SubjectDTO>>
     * @Author: bravelee
     * @Date: 2020/9/23
     */
    @ApiOperation("业务集列表")
    @GetMapping("/list/{subjectId}")
    public Map<String, List<BusinessEntity>> getBusinessList(@PathVariable Integer subjectId) {
        return businessService.getBusinessList(subjectId);
    }

    /**
     * @Description: 添加业务集
     * @Param: [subjectId]
     * @return:
     * @Author: bravelee
     * @Date: 2020/9/24
     */
    @PostMapping("add")
    public void addBusiness(@RequestBody BusinessEntity businessEntity) {
        PublicUtils.checkNotEmptyArr(businessEntity.getName());
        PublicUtils.checkIntNotNull(businessEntity.getSubjectId());
        System.out.println(businessEntity);
        String errMsg = businessService.addBusiness(businessEntity);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }

    /**
     * @Description: 编辑业务集
     * @Param: [name, prefix, id]
     * @return:
     * @Author: bravelee
     * @Date: 2020/9/23
     */
    @PostMapping("/edit")
    public void editBusiness(@RequestBody BusinessEntity businessEntity) {
        //参数检测
        PublicUtils.checkNotEmptyArr(businessEntity.getName());
        PublicUtils.checkIntNotNull(businessEntity.getId().intValue());
        PublicUtils.checkIntNotNull(businessEntity.getSubjectId());
        String errMsg = businessService.editBusiness(businessEntity);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }

    /**
     * @Description: 删除业务集
     * @Param: [id]
     * @return:
     * @Author: bravelee
     * @Date: 2020/9/23
     */
    @PostMapping("/del/{id}")
    public void delBusiness(@PathVariable Integer id) {
        PublicUtils.checkNotNullArr(id);
        String errMsg = businessService.delBusiness(id);
        if (!errMsg.isEmpty()) {
            throw new ApplicationException(errMsg);
        }
    }
}
