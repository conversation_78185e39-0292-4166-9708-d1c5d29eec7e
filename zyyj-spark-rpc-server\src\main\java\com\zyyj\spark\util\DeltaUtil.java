package com.zyyj.spark.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zyyj.spark.config.SparkConfig;
import com.zyyj.spark.pojo.dto.DTMappingModel;
import com.zyyj.spark.pojo.dto.DataTunnelModel;
import com.zyyj.spark.pojo.vo.DeltaDataTypes;
import com.zyyj.spark.pojo.vo.KafkaOffset;
import io.delta.tables.DeltaTable;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.*;
import org.apache.spark.sql.types.*;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;


@Slf4j
@Builder
@Component
public class DeltaUtil {

    private static String KAFKA_TOPIC_OFFSET_TABLE_NAME = "_kafka_topic_offset";
    private static String STREAMING_QUERY_ID_TABLE = "_streaming_query_id_table";
    private static String CRATE_TABLE_PREFIX = "CREATE TABLE IF NOT EXISTS ";
    private static String CRATE_OR_REPLACE_TABLE_PREFIX = "CREATE OR REPLACE TABLE ";

    @Autowired
    SparkConfig sparkConfig;

    public void init() {
        System.out.println("INFO: Skipping table initialization due to Windows native library compatibility issues");
        System.out.println("INFO: Tables will be created automatically when first accessed");
        System.out.println("INFO: Application startup completed successfully");
        // 完全跳过表初始化，让应用程序正常启动
        // 表将在首次使用时自动创建
    }

    // 通过topic名称来查询 kafka的offset
    public KafkaOffset queryKafkaOffsetBy(String topicName) {
        SparkSession sparkSession = sparkConfig.getSession();

        String selectSql = "SELECT name,offset FROM " + KAFKA_TOPIC_OFFSET_TABLE_NAME + " WHERE name='" + topicName + "'";
        List<String> rows = sparkSession.sql(selectSql).toJSON().takeAsList(1);

        HashMap<String, Long> offsetMap = new HashMap();
        for (String row : rows) {
            ObjectMapper mapper = new ObjectMapper();
            //datTableRow是一个数据行对象，从datTableRow获取JSON格式字符串
            try {
                Map<String, String> tmpMap = mapper.readValue(row, Map.class);
                String offsetStr = tmpMap.get("offset");
                offsetMap = mapper.readValue(offsetStr, HashMap.class);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
                ;
            }
        }
        ;

        KafkaOffset m = new KafkaOffset();
        m.setName(topicName);
        m.setOffsets(offsetMap);
        m.setExistInTable(rows.size() > 0);
        return m;
    }

    public void updateKafkaOffset(KafkaOffset offsetModel) {
        SparkSession sparkSession = sparkConfig.getSession();
        // 在库中更新，不然插入  offset
        String sql = "";
        if (offsetModel.isExistInTable()) {
            sql = "UPDATE " + KAFKA_TOPIC_OFFSET_TABLE_NAME + " SET offset='" + offsetModel.getOffsetString() + "' WHERE name='" + offsetModel.getName() + "'";
        } else {
            sql = "INSERT INTO " + KAFKA_TOPIC_OFFSET_TABLE_NAME + " values ('" + offsetModel.getName() + "'" + ", '" + offsetModel.getOffsetString() + "')";
            offsetModel.setExistInTable(true);
        }
        log.warn(sql);
        sparkSession.sql(sql);
    }


    public String getSQIdBy(String tableName) {
        SparkSession sparkSession = sparkConfig.getSession();
        String sql = "SELECT sq_id FROM " + STREAMING_QUERY_ID_TABLE + " WHERE table_name='" + tableName + "'";
        Optional<String> optionalS = sparkSession.sql(sql).toJSON().collectAsList().stream().findFirst();
        if (optionalS.isPresent()) {
            JSONObject jsonObject = new JSONObject(optionalS.get());
            String id = jsonObject.getString("sq_id");
            return id;
        }
        return "";
    }


    public List<String> queryStreaming() {
        try {
            SparkSession sparkSession = sparkConfig.getSession();
            return sparkSession.sql("select * from " + STREAMING_QUERY_ID_TABLE).toJSON().collectAsList();
        } catch (Exception e) {
            System.out.println("INFO: Table " + STREAMING_QUERY_ID_TABLE + " does not exist yet, returning empty list");
            return new java.util.ArrayList<>();
        }
    }

    // 删除保存的流
    public void deleteSQBy(String id) {
        SparkSession sparkSession = sparkConfig.getSession();
        String sql = "DELETE FROM " + STREAMING_QUERY_ID_TABLE + " WHERE sq_id = '" + id + "'";
        sparkSession.sql(sql);
    }


    public void upsertSQ(String tableName, String id, String config) {
        SparkSession sparkSession = sparkConfig.getSession();

        // 在库中更新，不然插入  offset
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("table_name", tableName);
        jsonObject.put("sq_id", id);
        jsonObject.put("config", config);
        String data = jsonObject.toString();

        System.out.println("插入流记录: " + tableName + id);

        Dataset<Row> ds = sparkSession.read().json(sparkSession
                .createDataset(Collections.singletonList(data), Encoders.STRING()));

        DeltaTable table = DeltaTable.forName(STREAMING_QUERY_ID_TABLE);
        table.as("old").merge(ds.as("new"), "old.table_name = new.table_name")
                .whenMatched()
                .update(
                        new HashMap<String, Column>() {{
                            put("sq_id", functions.col("new.sq_id"));
                            put("config", functions.col("new.config"));
                        }}
                )
                .whenNotMatched()
                .insert(
                        new HashMap<String, Column>() {{
                            put("table_name", functions.col("new.table_name"));
                            put("sq_id", functions.col("new.sq_id"));
                            put("config", functions.col("new.config"));
                        }}
                ).execute();
    }

    public DeltaTable getDeltaTable(DataTunnelModel model) {
        String name = model.getTableName();
        try {
            DeltaTable deltaTable = DeltaTable.forName(name);
            return reInitDeltaTable(deltaTable,model);
        } catch (Exception e){
            e.printStackTrace();
            log.warn("表不存在，将创建新表");
            return createTableBy(model);
        }
    }

    DeltaTable createTableBy(DataTunnelModel model){
        return this.createTableBy(model, false);
    }

    DeltaTable createTableBy(DataTunnelModel model, boolean isReplace){
        SparkSession sparkSession = sparkConfig.getSession();
        String name = model.getTableName();
        List<DTMappingModel> configs = model.getMappingConf();
        String[] columns = new String[configs.size()];
        for (int i = 0; i < configs.size(); i++) {
            DTMappingModel conf = configs.get(i);
            // " fieldName Type COMMENT comment "
            String column = "`" + conf.getField() + "`  " + DeltaDataTypes.getType(conf.getType()) + " COMMENT '" + conf.getComment() + "'";
            columns[i] = column;
        }
        String create = isReplace ? CRATE_OR_REPLACE_TABLE_PREFIX : CRATE_TABLE_PREFIX;
        String columnsSql = String.join(", ", columns);
        String sql = create + "`" + name + "`" + " (" + columnsSql + ") USING DELTA";
        log.warn(sql);
        sparkSession.sql(sql);
        DeltaTable deltaTable = DeltaTable.forName(name);
        return deltaTable;
    }

    /* 重新初始化表 */
    DeltaTable reInitDeltaTable(DeltaTable deltaTable, DataTunnelModel model) {

        // 是否需要修改表
        boolean isNeedUpdateTableSchema = false;

        Dataset<Row> ds = deltaTable.toDF();
        StructType st = ds.schema();
        // 需要添加的列
        List<DTMappingModel> addColumns = new ArrayList<>();
        // 需要更改评论的列
        List<DTMappingModel> renameCommentColumns = new ArrayList<>();

        // 删除不需要的列
        for (StructField sf : st.fields()) {
            boolean isDelField = true;
            for (DTMappingModel m: model.getMappingConf()) {
                // 字段名和 数据 类型 都相同 ，则该字段保留
                String sfType = sf.dataType().typeName().toLowerCase();
                if (m.getField().equals(sf.name()) && sfType.equals(m.getType().toLowerCase())){
                    isDelField = false;
                }
            }
            if (isDelField){
                ds = ds.drop(sf.name());
                isNeedUpdateTableSchema = true;
            }
        }


        // 遍历 最终表的列
        for (DTMappingModel conf : model.getMappingConf()) {
            // 重命名列
            if (conf.isRenameField()) {
                ds = ds.withColumnRenamed(conf.getDbField(), conf.getField());
                isNeedUpdateTableSchema = true;
            }
            // 添加列
            if (conf.isAddField()) {
                boolean isCanAdd = true;
                for (StructField sf : st.fields()) {
                    // 存在同名的列， 不做添加
                    if (conf.getField().equals(sf.name()) ){
                        isCanAdd = false;
                    }
                }
                // 添加列
                if (isCanAdd){
                    addColumns.add(conf);
                    isNeedUpdateTableSchema = true;
                }
            }

            // 更新 comment
            if (conf.isUpdateComment()) {
                renameCommentColumns.add(conf);
            }
        }

        if (isNeedUpdateTableSchema){
            // 修改表 Schema， 并重新保存
            if (ds.schema().size()>0){
                ds.write()
                        .format("delta")
                        .mode("overwrite")
                        .option("overwriteSchema", "true")
                        .saveAsTable(model.getTableName());
            } else {
                //
                log.warn("原有表结构 不保存， 直接替换创建新表，原数据将会清空");
                return createTableBy(model,true);
            }
        }

        this.addColumn(model.getTableName(), addColumns);
        this.renameColumnComment(model.getTableName(), renameCommentColumns);
        // 重新 生成 table
        return DeltaTable.forName(model.getTableName());
    }
    /*
    重命名列。 异步执行
    更新 列 comment 没找到批量更新方法，只能单条执行，
    相对不重要，采用异步执行
    */
    void renameColumnComment(String tableName, List<DTMappingModel> columns) {
        if (columns.isEmpty()) return;

        // 异步 修改comment;
        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                SparkSession session = sparkConfig.getSession();
                for (DTMappingModel model : columns) {
                    String alterCommentSql = "ALTER TABLE " + tableName + " ALTER COLUMN " + model.getCreateTableFiled() + " COMMENT '" + model.getComment() + "'";
                    log.warn(alterCommentSql);
                    session.sql(alterCommentSql);
                }
            }
        });
        thread.run();
    }

    /* 给已存在的表添加列 */
    void addColumn(String tableName, List<DTMappingModel> columns) {
        if (columns.isEmpty()) return;

        SparkSession session = sparkConfig.getSession();
        // Add Columns
        ArrayList<String> column = new ArrayList<>();
        for (DTMappingModel model : columns) {
            column.add(model.getCreateTableFiled());
        }
        String addColumnsSql = "ALTER TABLE `" + tableName + "`" +
                                " ADD COLUMNS (" + String.join(", ", column) + ")";
        log.warn(addColumnsSql);
        session.sql(addColumnsSql);
    }

}
