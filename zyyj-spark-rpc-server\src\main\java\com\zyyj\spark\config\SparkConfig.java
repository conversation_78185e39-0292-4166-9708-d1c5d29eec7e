package com.zyyj.spark.config;


import org.apache.spark.sql.SparkSession;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import scala.Option;

import java.util.List;

/**
 * spark 配置文件
 */
@Component
@ConfigurationProperties(prefix = "spark")
public class SparkConfig {
    private String master;
    private String appName;
    private String warehouseDir;
    private String metastoreUris;
    private String driver;
    private List<String> sparkYarnDistJars;
    private List<String> sparkJars;

    public String getDriver() {
        return driver;
    }

    public void setDriver(String driver) {
        this.driver = driver;
    }

    public String getMetastoreUris() {
        return metastoreUris;
    }

    public void setMetastoreUris(String metastoreUris) {
        this.metastoreUris = metastoreUris;
    }

    public String getMaster() {
        return master;
    }

    public void setMaster(String master) {
        this.master = master;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getWarehouseDir() {
        return warehouseDir;
    }

    public void setWarehouseDir(String warehouseDir) {
        this.warehouseDir = warehouseDir;
    }

    public List<String> getSparkYarnDistJars() {
        return sparkYarnDistJars;
    }

    public void setSparkYarnDistJars(List<String> sparkYarnDistJars) {
        this.sparkYarnDistJars = sparkYarnDistJars;
    }

    public List<String> getSparkJars() {
        return sparkJars;
    }

    public void setSparkJars(List<String> sparkJars) {
        this.sparkJars = sparkJars;
    }



    /*
//            SparkConf conf = new SparkConf();
//            conf.setMaster(master);
//            conf.setAppName(appName);
//            conf.set("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension")
//                    .set("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog")
//                    .set("spark.sql.warehouse.dir", warehouseDir)
//                    .set("spark.jars", String.join(",",sparkJars));
//            SparkContext sc = SparkContext.getOrCreate(conf);
////            SparkContext sc = new SparkContext();
////            sc.setLogLevel("WARN");
////            sc.getConf()
////            sc.getConf().setAppName(appName);
////            sc.getConf().set("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension")
////                    .set("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog")
////                    .set("spark.sql.warehouse.dir", warehouseDir)
////                    .set("spark.jars", String.join(",",sparkJars));
//
//            sc.hadoopConfiguration().set("fs.defaultFS", "hdfs://ns");
//            sc.hadoopConfiguration().set("dfs.nameservices", "ns");
//            sc.hadoopConfiguration().set("dfs.ha.namenodes.ns", "nn1,nn2");
//            sc.hadoopConfiguration().set("dfs.namenode.rpc-address.ns.nn1", "spark01:9000");
//            sc.hadoopConfiguration().set("dfs.namenode.rpc-address.ns.nn2", "spark02:9000");
//            sc.hadoopConfiguration().set("dfs.client.failover.proxy.provider.ns", "org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider");
    * */


    public synchronized SparkSession getSession() {
        Option<SparkSession> session = SparkSession.getActiveSession();
        // 空
        if (session.isEmpty()) {
            // 添加调试信息
            System.out.println("SparkConfig - master: " + master);
            System.out.println("SparkConfig - appName: " + appName);
            System.out.println("SparkConfig - driver: " + driver);
            System.out.println("SparkConfig - warehouseDir: " + warehouseDir);
            System.out.println("SparkConfig - metastoreUris: " + metastoreUris);
            
            // Driver应该绑定到本地IP，但对外通告为可访问的IP
            String driverBindAddress = "***************"; // 绑定到158机器的IP
            String driverHost = "***************"; // 158机器的IP地址，用于对外通告
            System.out.println("Using driver host: " + driverHost);
            System.out.println("Using driver bind address: " + driverBindAddress);
            
            SparkSession spark = SparkSession
                    .builder()
                    .master(master)
                    .appName(appName)
                    .config("deploy-mode","client") // 改为client模式
                    .config("spark.driver.host", driverHost)
                    .config("spark.driver.bindAddress", driverBindAddress)
                    .config("spark.driver.port", "0") // 使用随机端口
                    .config("spark.blockManager.port", "0") // 使用随机端口
                    .config("spark.jars", sparkJars != null ? String.join(",",sparkJars) : "")
                    //yarn模式下jars包路径配置
//                    .config("spark.yarn.dist.jars", String.join(",",sparkYarnDistJars))
//                    .config("spark.yarn.dist.jars", "file:///Users/<USER>/.ivy2/jars/io.delta_delta-core_2.12-0.7.0.jar")

                    .config("spark.sql.warehouse.dir", warehouseDir)
                    .config("hive.metastore.uris", metastoreUris)
                    .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension")
                    .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog")

                    .config("spark.hadoop.fs.defaultFS", "hdfs://***************:9000")
                    // 暂时禁用Hive支持来测试基本功能
                    // .enableHiveSupport()
                    .getOrCreate();

            SparkSession.setDefaultSession(spark);
            SparkSession.setActiveSession(spark);

            return spark;
        }
        return session.get();
    }

    public SparkSession getLocalSession(){
        SparkSession spark = SparkSession
                .builder()
                .master("local[*]")//spark://localhost:7077
                .appName(appName)
                .config("spark.deploy-mode","cluster")
                .config("spark.jars", sparkJars != null ? String.join(",",sparkJars) : "")
                .config("spark.jars.packages", "io.delta:delta-core_2.12:0.7.0")
                .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension")
                .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog")
                // 暂时禁用Hive支持来测试基本功能
                // .enableHiveSupport()
                .getOrCreate();

        SparkSession.setDefaultSession(spark);
        SparkSession.setActiveSession(spark);
        return spark;
    }
}
