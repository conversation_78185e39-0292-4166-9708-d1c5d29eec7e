server:
  port: 8008

eureka:
  instance:
    prefer-ip-address: true
    non-secure-port: 8008
  client:
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://***************:8761/eureka/

spring:
  application:
    name: ZYYJ-SPARK-THRIFT-LOCAL-HH
  redis:
    database: 1
    host: *************
    port: 6379
    password: Ckjl12@#jk11
    timeout: 60000
zyyj:
  rpc:
    thrift:
      server:
        listen_port: 9008
spark:
  master: spark://localhost:7077
  appName: ZYYJ-SPARK
  warehouseDir: file:///Users/<USER>/work_data/warehouse/
  metastoreUris: thrift://localhost:9083
  driver: **************
  sparkYarnDistJars:
    - hdfs://localhost:9000/jars/io.delta_delta-core_2.12-0.7.0.jar
  sparkJars:
    - file:///Users/<USER>/.ivy2/jars/io.delta_delta-core_2.12-0.7.0.jar
#    - hdfs://localhost:9000/jars/mysql-connector-java-8.0.16.jar