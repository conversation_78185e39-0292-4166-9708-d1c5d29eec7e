package com.zyyj.cere.repository;


import com.zyyj.cere.pojo.entity.SubjectEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Transactional //涉及到更新必须加上事物注解否则报错,执行出错回滚
@EnableJpaRepositories
public interface SubjectRepository extends JpaRepository<SubjectEntity, Integer> {
    List<SubjectEntity> findAllByStatus(int status);

    boolean existsByIdAndStatus(Integer id, int status);

    List<SubjectEntity> findByNameAndStatus(String name, int status);

    @Query(value = "SELECT * FROM subject s  WHERE s.status = 1 AND s.id != ?1 AND s.name = ?2", nativeQuery = true)
    List<SubjectEntity> existByIdAndName(Integer id, String name);

    @Modifying //更新插入操作必须加此注解
    @Query(value = "UPDATE subject SET status = 2 WHERE id = ?1", nativeQuery = true)
    void updateById(Integer id);

}