package com.zyyj.cere.pojo.entity;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 数据集
 */

@ThriftStruct
@Data
@Entity
@NoArgsConstructor
@Table(name = "dataset")
@ApiModel(value = "DatasetEntity", description = "数据集")
public class DatasetEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false,length = 11)
    @ApiModelProperty(name = "id", value = "ID")
    private Integer id;


    /**
     * 数据集名
     */
    @NotNull(message = "数据集名不可为空")
    @ApiModelProperty(name = "name", value = "数据集名")
    @Column(name = "name", nullable = false,length = 1)
    private String name;

    /**
     * 父ID
     */
    @ApiModelProperty(name = "parent_id", value = "父ID")
    @Column(name = "parent_id" ,length = 11)
    private Integer parentId;

    /**
     * 类型
     */
    @ApiModelProperty(name = "type", value = "描述")
    @Column(name = "`type`",length = 2)
    private Integer type;

    /**
     * 层级
     */
    @ApiModelProperty(name = "level", value = "层级")
    @Column(name = "level" ,length = 2)
    private Integer level ;

    /**
     * 0 正常 2 删除
     */
    @ApiModelProperty(name = "status", value = "状态")
    @Column(name = "status")
    private Integer status = 0;

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public String getName() {
        return name;
    }

    @ThriftField(3)
    public Integer getParentId() {
        return parentId;
    }

    @ThriftField(4)
    public Integer getType() {
        return type;
    }

    @ThriftField(5)
    public Integer getLevel() {
        return level;
    }

    @ThriftField(6)
    public Integer getStatus() {
        return status;
    }

    @ThriftConstructor
    public DatasetEntity(Integer id, @NotNull(message = "数据集名不可为空") String name, Integer parentId, Integer type, Integer level, Integer status) {
        this.id = id;
        this.name = name;
        this.parentId = parentId;
        this.type = type;
        this.level = level;
        this.status = status;
    }
}
