# 手动启动服务进行调试测试
Write-Host "=== 手动启动调试服务 ===" -ForegroundColor Green
Write-Host "时间: $(Get-Date)"
Write-Host ""

# 创建日志目录
if (-not (Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs" -Force
    Write-Host "创建日志目录: logs"
}

# 设置Java环境
$env:JAVA_HOME = "C:\Program Files\Java\jdk1.8.0_141"
Write-Host "Java环境: $env:JAVA_HOME"
Write-Host ""

Write-Host "📝 注意: Spark RPC服务运行在158服务器上" -ForegroundColor Yellow
Write-Host ""

# 手动启动 Cerebro RPC 服务（使用新构建的assembly包）
Write-Host "🚀 手动启动 Cerebro RPC 服务（包含调试日志）..."
$cerebroJarPath = "zyyj-cerebro-rpc-server\target\zyyj-cerebro-rpc-server-bin\zyyj-cerebro-rpc-server\zyyj-cerebro-rpc-server.jar"
$cerebroLibPath = "zyyj-cerebro-rpc-server\target\zyyj-cerebro-rpc-server-bin\zyyj-cerebro-rpc-server\lib\*"

Write-Host "主jar路径: $cerebroJarPath" -ForegroundColor Cyan
Write-Host "依赖库路径: $cerebroLibPath" -ForegroundColor Cyan

$cerebroProcess = Start-Process -FilePath "$env:JAVA_HOME\bin\java" -ArgumentList `
    "-cp","$cerebroJarPath;$cerebroLibPath",
    "-Dspring.profiles.active=dev",
    "-Dspring.main.allow-bean-definition-overriding=true",
    "-Dserver.port=9007",
    "-Dzyyj.rpc.thrift.server.listen_port=9006",
    "-Deureka.client.serviceUrl.defaultZone=******************************************/eureka/",
    "-Dspring.config.location=file:E:\dev\project\zyyj-cerebro\zyyj-cerebro-rpc-server\src\main\resources\dev\application.yml",
    "com.zyyj.cere.CerebroRPCServerApplication" `
    -RedirectStandardOutput "logs\cerebro-rpc-debug.log" `
    -RedirectStandardError "logs\cerebro-rpc-debug-error.log" `
    -PassThru

Write-Host "Cerebro RPC 进程ID: $($cerebroProcess.Id)" -ForegroundColor Green
Write-Host ""

# 等待20秒
Write-Host "等待Cerebro RPC服务启动 (20秒)..."
Start-Sleep -Seconds 20

# 启动 REST API 服务
Write-Host "🚀 启动 REST API 服务..."
$restProcess = Start-Process -FilePath "$env:JAVA_HOME\bin\java" -ArgumentList `
    "-Dserver.port=8005",
    "-Dspring.profiles.active=dev",
    "-Dspring.application.name=ZYYJ-CEREBRO-REST",
    "-Deureka.instance.appname=ZYYJ-CEREBRO-REST",
    "-Deureka.instance.non-secure-port=8005",
    "-Dspring.main.allow-bean-definition-overriding=true",
    "-Deureka.client.serviceUrl.defaultZone=******************************************/eureka/",
    "-Dservice.cerebro=ZYYJ-CEREBRO-THRIFT",
    "-Dscan.package=com.zyyj.controller",
    "-jar","E:\dev\project\zyyj-cerebro\zyyj-cerebro-rest\target\zyyj-cerebro-rest.jar" `
    -RedirectStandardOutput "logs\rest-api-debug.log" `
    -RedirectStandardError "logs\rest-api-debug-error.log" `
    -PassThru

Write-Host "REST API 进程ID: $($restProcess.Id)" -ForegroundColor Green
Write-Host ""

# 等待10秒
Write-Host "等待REST API服务启动 (10秒)..."
Start-Sleep -Seconds 10

Write-Host ""
Write-Host "=== 启动完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "服务访问地址:" -ForegroundColor Yellow
Write-Host "  REST API: http://localhost:8005" -ForegroundColor White
Write-Host "  Cerebro RPC HTTP: http://localhost:9007" -ForegroundColor White
Write-Host "  Spark RPC: http://***************:8081 (远程)" -ForegroundColor White
Write-Host ""
Write-Host "进程ID:" -ForegroundColor Yellow
Write-Host "  Cerebro RPC: $($cerebroProcess.Id)" -ForegroundColor White
Write-Host "  REST API: $($restProcess.Id)" -ForegroundColor White
Write-Host ""
Write-Host "调试日志查看:" -ForegroundColor Yellow
Write-Host "  Get-Content logs\cerebro-rpc-debug.log -Tail 50 -Wait" -ForegroundColor White
Write-Host "  Get-Content logs\rest-api-debug.log -Tail 50 -Wait" -ForegroundColor White
Write-Host ""
Write-Host "测试创建表接口:" -ForegroundColor Yellow
Write-Host "  .\test-create-table-debug.ps1" -ForegroundColor White
Write-Host ""
Write-Host "停止服务:" -ForegroundColor Yellow
Write-Host "  Stop-Process -Id $($cerebroProcess.Id)" -ForegroundColor White
Write-Host "  Stop-Process -Id $($restProcess.Id)" -ForegroundColor White
Write-Host ""
Write-Host "🔍 现在可以测试创建表接口，查看详细的调试日志！" -ForegroundColor Green
