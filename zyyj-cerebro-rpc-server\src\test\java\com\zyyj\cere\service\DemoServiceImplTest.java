package com.zyyj.cere.service;

import com.zyyj.cere.CerebroRPCServerApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CerebroRPCServerApplication.class)
public class DemoServiceImplTest {

    @Autowired
    private DemoService demoService;

    @Test
    public void testGetId() {
        Long n = demoService.getId(1L);
        System.out.println(n);
    }

    @Test
    public void testGet() {
        String n = demoService.get();
        System.out.println(n);
    }
}