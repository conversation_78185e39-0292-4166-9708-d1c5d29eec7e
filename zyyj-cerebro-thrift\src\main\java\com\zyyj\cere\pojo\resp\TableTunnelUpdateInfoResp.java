package com.zyyj.cere.pojo.resp;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@ThriftStruct
@Setter
@ToString
@NoArgsConstructor
public class TableTunnelUpdateInfoResp {


    Byte sync_type;
    Byte status;
    Long tunnel_id;
    String name;
    String sync_type_name;
    String status_name;

    @ThriftConstructor
    public TableTunnelUpdateInfoResp(Byte sync_type, Byte status, Long tunnelId, String name, String sync_type_name, String status_name) {
        this.sync_type = sync_type;
        this.status = status;
        this.tunnel_id = tunnelId;
        this.name = name;
        this.sync_type_name = sync_type_name;
        this.status_name = status_name;
    }


    @ThriftField(1)
    public String getName() {
        return name;
    }

    @ThriftField(2)
    public Byte getSync_type() {
        return sync_type;
    }

    @ThriftField(3)
    public String getSync_type_name() {
        return sync_type_name;
    }

    @ThriftField(4)
    public Byte getStatus() {
        return status;
    }

    @ThriftField(5)
    public String getStatus_name() {
        return status_name;
    }

    @ThriftField(6)
    public Long getTunnelId() {
        return tunnel_id;
    }
}
