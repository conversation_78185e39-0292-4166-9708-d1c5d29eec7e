-- 数据库初始化脚本
-- 根据实体类还原的DDL语句

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `cerebro` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 使用数据库
USE `cerebro`;

-- 创建数据源类型表
CREATE TABLE IF NOT EXISTS `datasource_type` (
  `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` VARCHAR(20) NOT NULL COMMENT '名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据源类型';

-- 创建数据源表
CREATE TABLE IF NOT EXISTS `datasource` (
  `id` BIGINT(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type_id` BIGINT(1) NOT NULL COMMENT '数据源类型',
  `name` VARCHAR(10) NOT NULL COMMENT '名称',
  `describe` VARCHAR(100) DEFAULT NULL COMMENT '描述',
  `host` VARCHAR(200) NOT NULL COMMENT '连接地址',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `password` VARCHAR(50) NOT NULL COMMENT '密码',
  `database` VARCHAR(50) NOT NULL COMMENT '数据库名称',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1正常 2删除',
  PRIMARY KEY (`id`),
  KEY `idx_type_id` (`type_id`) COMMENT '数据源类型索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据源';

-- 添加外键约束
ALTER TABLE `datasource` ADD CONSTRAINT `fk_datasource_type` FOREIGN KEY (`type_id`) REFERENCES `datasource_type` (`id`);

-- 初始化数据源类型数据
INSERT INTO `datasource_type` (`name`) VALUES ('MySQL');
INSERT INTO `datasource_type` (`name`) VALUES ('PostgreSQL');
INSERT INTO `datasource_type` (`name`) VALUES ('Oracle');
INSERT INTO `datasource_type` (`name`) VALUES ('SQL Server');

-- 初始化数据源示例数据
INSERT INTO `datasource` (`type_id`, `name`, `describe`, `host`, `username`, `password`, `database`, `status`) VALUES 
(1, 'MySQL测试', 'MySQL测试数据源', 'localhost:3306', 'root', 'password', 'test_db', 1),
(2, 'PG测试', 'PostgreSQL测试数据源', 'localhost:5432', 'postgres', 'password', 'test_db', 1);

