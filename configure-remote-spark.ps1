# =============================================================================
# 配置使用***************远程Spark集群
# =============================================================================

function Log-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Log-Warn {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Log-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Log-Step {
    param([string]$Message)
    Write-Host "[STEP] $Message" -ForegroundColor Blue
}

# 停止Windows本地Spark服务
function Stop-LocalSparkService {
    Log-Step "停止Windows本地Spark服务..."
    
    try {
        taskkill /F /IM java.exe 2>$null
        Start-Sleep -Seconds 3
        Log-Info "本地服务已停止"
    } catch {
        Log-Warn "停止服务时出现警告"
    }
}

# 创建远程Spark配置的启动脚本
function Create-RemoteSparkScript {
    Log-Step "创建远程Spark配置启动脚本..."
    
    $remoteScript = @'
# =============================================================================
# 使用远程Spark集群启动脚本
# =============================================================================

Write-Host "🚀 启动 Spark RPC 服务 (使用远程集群)..."

$env:JAVA_HOME = "C:\Program Files\Java\jdk1.8.0_141"

# 启动参数 - 使用配置文件中的远程集群设置
$javaArgs = @(
    "-Xms512m",
    "-Xmx1g",
    "-Dserver.port=8081",
    "-Dspring.profiles.active=dev",
    "-Dspring.application.name=ZYYJ-SPARK-THRIFT",
    "-Deureka.instance.appname=ZYYJ-SPARK-THRIFT", 
    "-Deureka.instance.non-secure-port=8081",
    "-Deureka.client.serviceUrl.defaultZone=******************************************/eureka/",
    "-Dspring.main.allow-bean-definition-overriding=true",
    # 注意：不覆盖配置文件中的远程集群设置
    # 让应用使用application.yml中的配置：
    # spark.master=spark://***************:7077
    # spark.warehouseDir=hdfs://***************:9000/
    # spark.metastoreUris=thrift://***************:9083
    "-Dzyyj.rpc.thrift.server.listen_port=9009",
    "-jar",
    "E:\dev\project\zyyj-cerebro\zyyj-spark-rpc-server\target\zyyj-spark-rpc-server.jar"
)

Write-Host "启动参数配置完成，使用远程集群配置..."
Write-Host "远程Spark Master: spark://***************:7077"
Write-Host "远程HDFS: hdfs://***************:9000/"
Write-Host "远程Hive Metastore: thrift://***************:9083"

# 启动服务
& "$env:JAVA_HOME\bin\java" @javaArgs
'@

    $remoteScript | Out-File -FilePath "start-spark-remote-cluster.ps1" -Encoding UTF8
    Log-Info "远程集群启动脚本已创建: start-spark-remote-cluster.ps1"
}

# 创建SSH命令脚本用于启动远程服务
function Create-RemoteServerCommands {
    Log-Step "创建远程服务器启动命令..."
    
    $sshCommands = @'
# =============================================================================
# *************** 服务器启动命令
# =============================================================================

# 请在***************服务器上执行以下命令：

# 1. 连接到服务器
ssh root@***************

# 2. 检查当前状态
jps
netstat -tlnp | grep -E '7077|9000|9083'

# 3. 设置环境变量（根据实际安装路径调整）
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export HADOOP_HOME=/opt/hadoop  # 或实际的Hadoop安装路径
export SPARK_HOME=/opt/spark    # 或实际的Spark安装路径
export PATH=$JAVA_HOME/bin:$HADOOP_HOME/bin:$HADOOP_HOME/sbin:$SPARK_HOME/bin:$SPARK_HOME/sbin:$PATH

# 4. 如果Hadoop未安装，先安装：
# cd /root
# tar -xzf hadoop-*.tar.gz
# mv hadoop-* /opt/hadoop
# 配置Hadoop...

# 5. 如果Spark未安装，先安装：
# cd /root  
# tar -xzf spark-*.tgz
# mv spark-* /opt/spark
# 配置Spark...

# 6. 启动Hadoop（如果需要HDFS）
$HADOOP_HOME/sbin/start-dfs.sh
$HADOOP_HOME/sbin/start-yarn.sh

# 7. 启动Spark集群
$SPARK_HOME/sbin/start-master.sh
$SPARK_HOME/sbin/start-worker.sh spark://$(hostname):7077

# 8. 启动Hive Metastore（如果需要）
nohup hive --service metastore > /var/log/hive-metastore.log 2>&1 &

# 9. 验证启动
jps
# 应该看到：NameNode, DataNode, ResourceManager, NodeManager, Master, Worker

# 10. 检查端口
netstat -tlnp | grep -E '7077|8080|9000|9083|8088|9870'

# 11. 访问Web界面验证
# Spark Master: http://***************:8080
# HDFS: http://***************:9870  
# YARN: http://***************:8088
'@

    $sshCommands | Out-File -FilePath "remote-server-setup-commands.txt" -Encoding UTF8
    Log-Info "远程服务器命令已保存到: remote-server-setup-commands.txt"
}

# 测试远程集群连接
function Test-RemoteClusterConnection {
    Log-Step "测试远程集群连接..."
    
    Write-Host ""
    Write-Host "=== 连接测试结果 ==="
    
    # 测试Spark Master
    $sparkMaster = Test-NetConnection -ComputerName "***************" -Port 7077 -InformationLevel Quiet -WarningAction SilentlyContinue
    if ($sparkMaster) {
        Log-Info "✅ Spark Master (7077) - 可连接"
    } else {
        Log-Warn "❌ Spark Master (7077) - 不可连接，需要启动"
    }
    
    # 测试HDFS
    $hdfs = Test-NetConnection -ComputerName "***************" -Port 9000 -InformationLevel Quiet -WarningAction SilentlyContinue
    if ($hdfs) {
        Log-Info "✅ HDFS NameNode (9000) - 可连接"
    } else {
        Log-Warn "❌ HDFS NameNode (9000) - 不可连接，需要启动"
    }
    
    # 测试Hive Metastore
    $hive = Test-NetConnection -ComputerName "***************" -Port 9083 -InformationLevel Quiet -WarningAction SilentlyContinue
    if ($hive) {
        Log-Info "✅ Hive Metastore (9083) - 可连接"
    } else {
        Log-Warn "❌ Hive Metastore (9083) - 不可连接，需要启动"
    }
    
    # 测试Spark Web UI
    try {
        $webUI = Invoke-WebRequest -Uri "http://***************:8080" -TimeoutSec 5 -UseBasicParsing
        Log-Info "✅ Spark Web UI - 可访问"
    } catch {
        Log-Warn "❌ Spark Web UI - 不可访问"
    }
}

# 生成下一步操作指南
function Generate-NextSteps {
    Log-Step "生成下一步操作指南..."
    
    Write-Host ""
    Write-Host "=== 下一步操作指南 ===" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "1. 首先启动***************服务器上的服务："
    Write-Host "   - 查看文件: remote-server-setup-commands.txt"
    Write-Host "   - SSH到服务器执行启动命令"
    Write-Host ""
    
    Write-Host "2. 验证远程服务启动成功："
    Write-Host "   - Spark Master: http://***************:8080"
    Write-Host "   - 端口7077, 9000, 9083应该可以连接"
    Write-Host ""
    
    Write-Host "3. 启动Windows端的Spark RPC服务："
    Write-Host "   .\start-spark-remote-cluster.ps1"
    Write-Host ""
    
    Write-Host "4. 启动其他Windows服务："
    Write-Host "   - Cerebro RPC服务"
    Write-Host "   - REST API服务"
    Write-Host ""
    
    Write-Host "5. 测试接口："
    Write-Host "   POST http://localhost:8005/api/v1/table/add"
    Write-Host ""
    
    Write-Host "优势："
    Write-Host "✅ 使用生产级Hadoop/Spark集群"
    Write-Host "✅ 避免Windows兼容性问题"
    Write-Host "✅ 数据存储在HDFS上"
    Write-Host "✅ 可以利用集群的计算资源"
}

# 主函数
function Main {
    Write-Host "=== 配置使用***************远程Spark集群 ===" -ForegroundColor Cyan
    Write-Host ""
    
    # 停止本地服务
    Stop-LocalSparkService
    
    # 创建远程配置脚本
    Create-RemoteSparkScript
    
    # 创建远程服务器命令
    Create-RemoteServerCommands
    
    # 测试连接
    Test-RemoteClusterConnection
    
    # 生成操作指南
    Generate-NextSteps
    
    Write-Host ""
    Write-Host "=== 配置完成 ===" -ForegroundColor Cyan
}

# 执行主函数
Main
