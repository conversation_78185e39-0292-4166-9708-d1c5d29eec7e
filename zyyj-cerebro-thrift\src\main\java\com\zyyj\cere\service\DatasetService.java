package com.zyyj.cere.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.zyyj.cere.pojo.dto.DatasetAttributeDto;
import com.zyyj.cere.pojo.entity.*;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.domain.pagination.Paging;

import java.util.List;
import java.util.Map;


/**
 * 数据源
 */
@ThriftService
public interface DatasetService {

    /**
     * 保存数据集
     *
     * @return
     * @throws ApplicationException
     */
    @ThriftMethod
    void saveDataset(DatasetEntity datasetEntity);

    /**
     * 删除数据集
     *
     * @return
     * @throws ApplicationException
     */
    @ThriftMethod
    void datasetDelete(Integer id);


    /**
     * 保存数据标准
     * @param datasetStandardEntity
     */
    @ThriftMethod
    void saveDatasetStandard(DatasetStandardEntity datasetStandardEntity);

    /**
     * 删除数据标准
     * @param id
     */
    @ThriftMethod
    void standardDelete(Integer id);

    /**
     * 保存数据标准属性
     * @param datasetAttributeDto
     */
    @ThriftMethod
    void saveDatasetAttribute(DatasetAttributeDto datasetAttributeDto);

    @ThriftMethod
    void attributeDelete(Integer id,Integer datasetId,Integer propertyTypeId);

    @ThriftMethod
    Map<String, List<DatasetAttributeEntity>> getInnerAttributeList(Integer id);

    @ThriftMethod
    String getAllAttributeList(Integer id,String name,String type, Paging paging);

    @ThriftMethod
    List<DatasetEntity> getDatasetList(Integer parentId,String name);

    @ThriftMethod
    String getStandardList(Integer datasetId, String name, Paging paging);

    @ThriftMethod
    List<DatasetAttributeEntity> getStandardTitle(Integer datasetId);

    @ThriftMethod
    String getAllAttributeMoveList();

    @ThriftMethod
    String getAllAttributeCopyList();

    @ThriftMethod
    void saveCopyAttribute(Integer id, Integer copyId);

    @ThriftMethod
    void saveInnerAttribute(DatasetRelationEntity datasetRelationEntity);
}
