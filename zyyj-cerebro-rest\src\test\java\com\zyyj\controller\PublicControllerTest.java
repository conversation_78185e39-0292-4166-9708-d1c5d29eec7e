package com.zyyj.controller;

import com.zyyj.CerebroRestApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = CerebroRestApplication.class)
public class PublicControllerTest {

    @Autowired
    private PublicController controller;

    @Test
    public void getQiniuToken() {
        Map<String, String> token = controller.getQiniuToken();
        System.out.println(token);
    }
}