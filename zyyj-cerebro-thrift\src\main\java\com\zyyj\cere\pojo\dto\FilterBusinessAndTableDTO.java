package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2020/11/17 14:59
 */
@ThriftStruct
@Setter
@Builder
@ToString
@NoArgsConstructor
public class FilterBusinessAndTableDTO {
    private int id;
    private String name;
    private String tableName;
    private int businessId;
    private int subjectId;
    private int status;
    private String businessName;

    @ThriftConstructor
    public FilterBusinessAndTableDTO(int id, String name, String tableName, int businessId, int subjectId, int status, String businessName) {
        this.id = id;
        this.name = name;
        this.tableName = tableName;
        this.businessId = businessId;
        this.subjectId = subjectId;
        this.status = status;
        this.businessName = businessName;
    }

    @ThriftField(1)
    public int getId() {
        return id;
    }

    @ThriftField(2)
    public String getName() {
        return name;
    }

    @ThriftField(3)
    public String getTableName() {
        return tableName;
    }

    @ThriftField(4)
    public int getBusinessId() {
        return businessId;
    }

    @ThriftField(5)
    public int getSubjectId() {
        return subjectId;
    }

    @ThriftField(6)
    public int getStatus() {
        return status;
    }

    @ThriftField(7)
    public String getBusinessName() {
        return businessName;
    }
}
