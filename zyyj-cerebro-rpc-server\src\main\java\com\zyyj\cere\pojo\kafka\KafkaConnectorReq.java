package com.zyyj.cere.pojo.kafka;

import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
@Setter
public class KafkaConnectorReq {
    private String name;
    private Map<String, String> config;

    public String getName() {
        return name;
    }
    public Map<String, String> getConfig() {
        return config;
    }
}