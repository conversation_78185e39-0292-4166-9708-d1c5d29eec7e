package com.zyyj.cere.pojo.kafka;

import lombok.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.stream.Collectors;



/* Kafka Connector Mysql Source connector config */


@Setter
@NoArgsConstructor
@AllArgsConstructor
public class KCMySqlSourceConfig extends DataBaseConfig {
    // kafka connect name 唯一
    String name;
    // server id
    String serverId;
    // server name
    String serverName;
    // database name
    String dbName;
    // 白名单表
    String[] tableNames;
    // kafka bootstrapServer
    String[] bootstrapServer;

    private String getTimeStamp() {
        long millis = System.currentTimeMillis();
        return String.valueOf(millis);
    }

    public String getName() {
        if (name==null || name.isEmpty()) {
            this.name = "mysql_source_" + getTimeStamp();
        }
        return this.name;
    }

    public String getServerId() {
        return serverId;
    }

    public String getServerName() {
        return serverName;
    }

    public String getDbName() {
        return dbName;
    }

    public String[] getTableName() {
        return tableNames;
    }

    public String getTableWhiteList(){
        if (this.tableNames != null && this.tableNames.length > 0){
            String tableWhiteList = Arrays.stream(tableNames).map(val -> {
                return getDbName() + "." + val;
            }).collect(Collectors.joining(","));
            return  tableWhiteList;
        }
        return "";
    }



    public HashMap<String, String> getKCConfig(){
        HashMap<String, String> conf = new HashMap<>();
        conf.put("connector.class","io.debezium.connector.mysql.MySqlConnector");
        conf.put("include.schema.changes", "true");
        conf.put("database.history.skip.unparseable.ddl", "true");
        conf.put("database.hostname", this.getHostName());
        conf.put("database.port", this.getPort());
        conf.put("database.user", this.getUser());
        conf.put("database.password", this.getPassword());
        conf.put("database.server.id", this.getServerId());
        conf.put("database.server.name", this.getServerName());
        conf.put("database.whitelist", this.getDbName());
        conf.put("database.history.kafka.bootstrap.servers", String.join(",",this.bootstrapServer));
        conf.put("database.history.kafka.topic", "dbhistory" + "." + this.getDbName());

        // 设置表白名单
        if (!this.getTableWhiteList().isEmpty()){
            conf.put("table.whitelist", this.getTableWhiteList());
        }

        return conf;
    }
}
