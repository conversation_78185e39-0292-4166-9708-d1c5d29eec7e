server:
  port: 8008

eureka:
  instance:
    prefer-ip-address: true
    non-secure-port: 8008
  client:
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: ******************************************/eureka/

spring:
  application:
    name: ZYYJ-SPARK-THRIFT
  main:
    allow-bean-definition-overriding: true
  redis:
    database: 1
    host: **************
    port: 6379
    password: Vhzu4U5MZJw2lmc
    timeout: 60000

logging:
  level:
    root: INFO
    com.facebook.swift: DEBUG
    org.apache.thrift: DEBUG
    com.zyyj.spark: DEBUG
    com.zyyj.rpc: DEBUG
zyyj:
  rpc:
    thrift:
      server:
        listen_port: 9009
        request_timeout: 300000  # 5 minutes in milliseconds
        max_frame_size: 16777216  # 16MB
        client_idle_timeout: 300000  # 5 minutes
      client:
        connection_timeout: 60000  # 60 seconds
        receive_timeout: 300000    # 5 minutes
        max_connections_per_host: 50
        max_frame_size: 16777216   # 16MB
spark:
  master: spark://***************:7077
  appName: ZYYJ-SPARK
  warehouseDir: hdfs://***************:9000/spark-warehouse
  metastoreUris: thrift://***************:9083
  driver: ***************
  sparkYarnDistJars:
    - hdfs://***************:9000/jars/io.delta_delta-core_2.12-0.7.0.jar
  sparkJars:
    - hdfs://***************:9000/jars/mysql-connector-java*.jar
    - hdfs://***************:9000/jars/delta-core_2.12-0.7.0.jar
