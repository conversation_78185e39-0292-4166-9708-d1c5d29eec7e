package com.zyyj.cere.repository;

import com.zyyj.cere.pojo.entity.ApplicationEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/10 16:08
 */

public interface ApplicationRepository extends JpaRepository<ApplicationEntity, Integer> {

    //查询第1页2条名字含有name的应用，模糊查询Containing关键字将name拼接成：%name%
    Page<ApplicationEntity> findByTypeIdAndNameContaining(Integer typeId, String name, Pageable pageable);

    Page<ApplicationEntity> findByNameContaining(String name, Pageable pageable);

    List<ApplicationEntity> findByName(String name);

    @Query(value = "SELECT * FROM application a  WHERE a.id != ?1 AND a.name = ?2", nativeQuery = true)
    List<ApplicationEntity> existByIdAndName(Integer id, String name);

    @Modifying //更新插入操作必须加此注解
    @Query(value = "UPDATE application SET name = ?1 AND `describe` = ?2 AND type_id = ?3 WHERE id = ?4", nativeQuery = true)
    void updateApplicationById(String name, String describe, Integer typeId, Integer id);

    ApplicationEntity findByAppId(String appId);

    ApplicationEntity findByAppSecret(String appSecret);

    List<ApplicationEntity> findByTypeId(Integer typeId);

}
