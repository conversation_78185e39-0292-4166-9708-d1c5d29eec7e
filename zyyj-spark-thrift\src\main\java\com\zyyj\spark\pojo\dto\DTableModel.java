package com.zyyj.spark.pojo.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;


@ThriftStruct
@NoArgsConstructor
@Setter
public class DTableModel implements Serializable {

    private static final long serialVersionUID = 1L;

    String database;

    String tableName;

    boolean isTemporary;

    @ThriftField(1)
    public String getDatabase() {
        return database;
    }

    @ThriftField(2)
    public String getTableName() {
        return tableName;
    }

    @ThriftField(3)
    public boolean isTemporary() {
        return isTemporary;
    }
}
