package com.zyyj.spark.init;

import com.google.gson.Gson;
import com.zyyj.spark.config.SparkConfig;
import com.zyyj.spark.pojo.dto.DataTunnelModel;
import com.zyyj.spark.service.DeltaTunnelServiceImpl;
import com.zyyj.spark.util.DeltaUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.SparkSession;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class SparkSessionInit implements ApplicationRunner {
    @Autowired
    SparkConfig sparkConfig;

    @Autowired
    DeltaUtil deltaUtil;

    @Autowired
    DeltaTunnelServiceImpl service;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        System.out.println("通过实现ApplicationRunner接口，在spring boot项目启动后打印参数");
        System.out.println("开始获取sparkSession");
        sparkConfig.getSession();
        System.out.println("成功获取sparkSession");

        // 创建 默认 spark 表
        deltaUtil.init();
        restartStreamingService();
    }

    //需要重启停掉的流服务
    private void restartStreamingService(){
        SparkSession session = sparkConfig.getSession();
        List<String> lists = deltaUtil.queryStreaming();

        Gson gson = new Gson();
        for (String l:  lists) {
            JSONObject jsonObject = new JSONObject(l);
            // 重启流
            if (!jsonObject.isNull("config")){
                String config = jsonObject.optString("config");
                log.warn(config);
                Thread thread = new Thread(new Runnable() {
                    @Override
                    public void run() {
                        DataTunnelModel dtModel = gson.fromJson(config, DataTunnelModel.class);
                        service.initTunnel(dtModel);
                    }
                });
                thread.run();
            }

        }

    }
}
