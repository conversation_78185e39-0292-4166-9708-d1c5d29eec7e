package com.zyyj.cere.pojo.dto;

import java.util.List;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.sdk.processor.annotation.ThriftPaged;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * 物理表创建对象
 */
@ThriftStruct
@ThriftPaged
@Setter
@NoArgsConstructor
@ToString
@Builder
public class TableCreateDTO {

    @ApiModelProperty(name = "tableName", value = "表名")
    private String tableName;

    @ApiModelProperty(name = "field",value = "字段")
    private List<String> field;

    @ApiModelProperty(name = "comment",value = "注释")
    private String comment="";

    @ApiModelProperty(name = "priKey",value = "主键字段")
    private String priKey;

    @ThriftConstructor
    public TableCreateDTO(String tableName, List<String> field, String comment, String priKey) {
        this.tableName = tableName;
        this.field = field;
        this.comment = comment;
        this.priKey = priKey;
    }

    @ThriftField(1)
    public String getTableName() {
        return tableName;
    }

    @ThriftField(2)
    public List<String> getField() {
        return field;
    }

    @ThriftField(3)
    public String getComment() {
        return comment;
    }

    @ThriftField(4)
    public String getPriKey() {
        return priKey;
    }

    @ThriftField
    public void setFieldArr(TableFieldDTO t){
        if (t.getPriKey() == 1) this.priKey=t.getField();
        this.field.add("`"+t.getField()+"` "+t.getType()+" COMMENT '"+t.getComment()+"'");
    }

    /**
     * Delta格式建表语句
     * @return sql
     */
    @ThriftField(5)
    public String getSqlForDelta(){
        String sql = "CREATE OR REPLACE TABLE `"+this.tableName+"` (" +
                String.join(",", this.field)+
                ") USING DELTA ";

        // 只有当主键字段不为空时才添加分区
        if (this.priKey != null && !this.priKey.trim().isEmpty()) {
            sql += "PARTITIONED BY (`"+this.priKey+"`) ";
        }

        sql += "COMMENT '"+this.comment+"'";
        return sql;
    }
}
