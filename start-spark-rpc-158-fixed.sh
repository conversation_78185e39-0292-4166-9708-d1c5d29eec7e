#!/bin/bash
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export HADOOP_USER_NAME=root

echo "=== 启动Spark RPC服务（基于成功参数）==="
echo "时间: $(date)"
echo "Java版本: $($JAVA_HOME/bin/java -version 2>&1 | head -1)"
echo ""

# 停止旧进程
pkill -f zyyj-spark-rpc-server
sleep 3

# 创建日志目录
mkdir -p /opt/logs

echo "🚀 启动Spark RPC服务..."

nohup $JAVA_HOME/bin/java \
    -Xms512m \
    -Xmx1g \
    -Dserver.port=8081 \
    -Dspring.profiles.active=dev \
    -Dspring.application.name=ZYYJ-SPARK-THRIFT \
    -Deureka.instance.appname=ZYYJ-SPARK-THRIFT \
    -Deureka.instance.non-secure-port=9009 \
    -Deureka.instance.hostname=*************** \
    -Deureka.instance.ip-address=*************** \
    -Deureka.client.serviceUrl.defaultZone=******************************************/eureka/ \
    -Dspring.main.allow-bean-definition-overriding=true \
    -Dspark.master=local[*] \
    -Dspark.appName=ZYYJ-SPARK-RPC-SERVER \
    -Dspark.warehouseDir=hdfs://***************:9000/spark-warehouse \
    -Dspark.metastoreUris=thrift://***************:9083 \
    -Dspark.driver.host=*************** \
    -Dspark.driver.bindAddress=*************** \
    -Dspark.driver.port=0 \
    -Dspark.blockManager.port=0 \
    -Dspark.sql.adaptive.enabled=false \
    -Dspark.serializer=org.apache.spark.serializer.KryoSerializer \
    -Dspark.serializer.objectStreamReset=100 \
    -Dspark.kryo.unsafe=true \
    -Dzyyj.rpc.thrift.server.listen_port=9009 \
    -DHADOOP_USER_NAME=root \
    -Duser.name=root \
    -Dhadoop.security.authentication=simple \
    -Dspark.hadoop.hadoop.security.authentication=simple \
    -Dspark.hadoop.hadoop.security.authorization=false \
    -Dspark.sql.warehouse.dir=hdfs://***************:9000/spark-warehouse \
    -jar /opt/zyyj-spark-rpc-server.jar \
    > /opt/logs/spark-rpc.log 2>&1 &

# 获取进程ID
sleep 2
PID=$(pgrep -f zyyj-spark-rpc-server)

if [ -n "$PID" ]; then
    echo "✅ 服务启动成功，进程ID: $PID"
    echo "📋 日志文件: /opt/logs/spark-rpc.log"
    echo "🔍 查看日志: tail -f /opt/logs/spark-rpc.log"
    echo "🛑 停止服务: kill $PID"
else
    echo "❌ 服务启动失败"
    echo "📋 查看错误日志: tail -20 /opt/logs/spark-rpc.log"
fi

echo ""
echo "=== 启动脚本执行完成 ==="
