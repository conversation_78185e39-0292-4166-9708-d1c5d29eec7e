package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.domain.pagination.Paged;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/17 14:45
 */
@ThriftStruct
public class PageListStringDTO extends Paged<String> {

    private List<String> headList;

    @ThriftField(3)
    public List<String> getHeadList() {
        return headList;
    }

    public void setHeadList(List<String> headList) {
        this.headList = headList;
    }


    @ThriftConstructor
    public PageListStringDTO(long total, List<String> data, List<String> headList) {
        super(total, data);
        this.headList = headList;
    }
}
