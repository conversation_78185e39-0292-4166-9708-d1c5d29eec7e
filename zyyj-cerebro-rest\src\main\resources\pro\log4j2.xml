<?xml version="1.0" encoding="UTF-8"?>
<configuration status="info" monitorinterval="60">
	<Properties>
		<Property name="layoutPattern">%X{LogId} %d [%t] %level %logger{36} (%F:%L) - %msg%n</Property>
		<Property name="basePath">/data/logs/zyyj-cerebro-api</Property>
	</Properties>
	<!-- 定义所有的appender -->
	<appenders>
		<!-- 输出控制台的配置 -->
		<Console name="CONSOLE" target="SYSTEM_OUT">
			<PatternLayout pattern="${layoutPattern}" />
		</Console>
		<!-- ERROR 日志 -->
		<RollingFile name="ERROR-FILE" fileName="${basePath}/zyyj-cerebro-api-error.log"
					 filePattern="${basePath}/zyyj-cerebro-api-error-%d{yyyy-MM-dd}.log.gz">
			<ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY" />
			<PatternLayout pattern="${layoutPattern}" />
			<Policies>
				<TimeBasedTriggeringPolicy modulate="true" interval="1" />
			</Policies>
		</RollingFile>
		<!-- log 日志 -->
		<RollingFile name="LOG-File" fileName="${basePath}/zyyj-cerebro-api.log"
					 filePattern="${basePath}/zyyj-cerebro-api-%d{yyyy-MM-dd}.log.gz">
			<PatternLayout pattern="${layoutPattern}" />
			<Policies>
				<TimeBasedTriggeringPolicy modulate="true" interval="1" />
			</Policies>
		</RollingFile>
	</appenders>
	<loggers>
		<Root level="info" includeLocation="true">
			<appender-ref ref="CONSOLE" />
			<appender-ref ref="LOG-File" />
			<appender-ref ref="ERROR-FILE" />
		</Root>
	</loggers>
</configuration>