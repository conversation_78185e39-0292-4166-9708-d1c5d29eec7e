package com.zyyj.cere.service;

import com.alibaba.fastjson.JSONObject;
import com.zyyj.cere.pojo.dto.DatasetAttributeDto;
import com.zyyj.cere.pojo.dto.DatasetDto;
import com.zyyj.cere.pojo.entity.*;
import com.zyyj.cere.repository.DatasetAttributeRepository;
import com.zyyj.cere.repository.DatasetRelationRepository;
import com.zyyj.cere.repository.DatasetRepository;
import com.zyyj.cere.repository.DatasetStandardRepository;
import com.zyyj.cere.utils.PageUtils;
import com.zyyj.domain.pagination.Paging;
import com.zyyj.rpc.thrift.server.ThriftServiceHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据源实现类
 */
@Slf4j
@Service
@ThriftServiceHandler
public class DatasetServiceImpl implements DatasetService {

    @Autowired
    DatasetRepository datasetRepository;
    @Autowired
    DatasetStandardRepository datasetStandardRepository;
    @Autowired
    DatasetAttributeRepository datasetAttributeRepository;
    @Autowired
    DatasetRelationRepository datasetRelationRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDataset(DatasetEntity datasetEntity){
        Boolean newRecord = false;
        if(datasetEntity.getId()==null&& datasetEntity.getType()==2) newRecord = true;
        if(datasetEntity.getLevel()==null){
            DatasetEntity datasetParent= datasetRepository.findById(datasetEntity.getParentId()).get();
            if(datasetParent!=null){
                datasetEntity.setLevel(datasetParent.getLevel()+1);
            }
        }
        datasetEntity = datasetRepository.saveAndFlush(datasetEntity);
        if(newRecord){
            DatasetRelationEntity datasetRelationEntity = new DatasetRelationEntity();
            datasetRelationEntity.setDatasetId(datasetEntity.getId());
            datasetRelationEntity.setInnerAttribute("1,2,3");
            datasetRelationRepository.save(datasetRelationEntity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void datasetDelete(Integer id) {
        datasetRepository.deleteByParentIdOrId(id);
    }

    @Override
    public void saveDatasetStandard(DatasetStandardEntity datasetStandardEntity) {
        datasetStandardRepository.saveAndFlush(datasetStandardEntity);
    }

    @Override
    public void standardDelete(Integer id) {
        datasetStandardRepository.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDatasetAttribute(DatasetAttributeDto datasetAttributeDto) {
        Integer datasetId = datasetAttributeDto.getDatasetId();
        DatasetAttributeEntity datasetAttributeEntity = new DatasetAttributeEntity();
        BeanUtils.copyProperties(datasetAttributeDto,datasetAttributeEntity);
        datasetAttributeEntity = datasetAttributeRepository.saveAndFlush(datasetAttributeEntity);
        if(datasetAttributeDto.getId()==null){
            DatasetRelationEntity datasetRelationEntity = datasetRelationRepository.findByDatasetId(datasetId);
            if(datasetRelationEntity.getCustomizeAttribute()==null
                    ||"".equals(datasetRelationEntity.getCustomizeAttribute())){
                datasetRelationEntity.setCustomizeAttribute( String.valueOf(datasetAttributeEntity.getId()));
            }else{
                datasetRelationEntity.setCustomizeAttribute(datasetRelationEntity.getCustomizeAttribute()+","+datasetAttributeEntity.getId());
            }
            datasetRelationRepository.saveAndFlush(datasetRelationEntity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void attributeDelete(Integer id,Integer datasetId,Integer propertyTypeId) {
        DatasetRelationEntity datasetRelationEntity = datasetRelationRepository.findByDatasetId(datasetId);
        if(propertyTypeId==7){
            datasetRelationEntity.setCustomizeAttribute(removeEle(datasetRelationEntity.getCustomizeAttribute(),id.toString()));
            datasetAttributeRepository.deleteById(id);
        }else{
            datasetRelationEntity.setInnerAttribute(removeEle(datasetRelationEntity.getInnerAttribute(),id.toString()));
        }
        datasetRelationRepository.save(datasetRelationEntity);
    }

    @Override
    public Map<String, List<DatasetAttributeEntity>> getInnerAttributeList(Integer id) {
        String ids = datasetRelationRepository.findByDatasetId(id).getInnerAttribute();
        List<Integer> attributeIds =Arrays.asList(ids.split(","))
                .stream().map(s -> Integer.parseInt(s.trim()))
                .collect(Collectors.toList());
        List<DatasetAttributeEntity> innerOwnList = datasetAttributeRepository.findByIdIn(attributeIds);
        List<DatasetAttributeEntity> innerNoList = datasetAttributeRepository.findByIdNotInAndIdLessThan(attributeIds,100);
        HashMap<String, List<DatasetAttributeEntity>> map = new HashMap<>();
        map.put("isOwn",innerOwnList);
        map.put("notOwn",innerNoList);
        return map;
    }

    @Override
    public String getAllAttributeList(Integer id, String name,String type,Paging paging) {
        return JSONObject.toJSONString(PageUtils.getPageList(getAllAttribute(id,name,type),paging));
    }

    @Override
    public List<DatasetEntity> getDatasetList(Integer parentId,String name) {
        if(name!=null && !"".equals(name)){
            return getSearchList(parentId,name);
        }else{
            return datasetRepository.findByParentIdAndStatus(parentId,0);
        }
    }

    @Override
    public String getStandardList(Integer datasetId, String name, Paging paging){
        Specification<DatasetStandardEntity> specification = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<Predicate>();
            predicates.add(cb.equal(root.get("status"), 0));
            predicates.add(cb.equal(root.get("datasetId"), datasetId));
            if(name != null&& !"".equals(name)){
                predicates.add(cb.or(cb.like(root.get("code"), "%"+name+"%"),
                        (cb.like(root.get("cname"), "%"+name+"%")),
                        (cb.like(root.get("ename"), "%"+name+"%"))));
            }
            Predicate[] pre = new Predicate[predicates.size()];
            return query.where(predicates.toArray(pre)).getRestriction();
        };
        List<DatasetStandardEntity> list = datasetStandardRepository.findAll(specification);
        return JSONObject.toJSONString(PageUtils.getPageList(list,paging));
    }

    @Override
    public List<DatasetAttributeEntity> getStandardTitle(Integer datasetId) {
        return getAllAttribute(datasetId,null,null);
    }

    public List<DatasetAttributeEntity> getAllAttribute(Integer datasetId,String name,String type){
        DatasetRelationEntity data = datasetRelationRepository.findByDatasetId(datasetId);
        List<DatasetAttributeEntity>  resultList = new ArrayList<>();
        if(data!=null){
            List<Integer> attributeIds =Arrays.asList(data.getInnerAttribute().split(","))
                    .stream().map(s -> Integer.parseInt(s.trim()))
                    .collect(Collectors.toList());
            if(data.getCustomizeAttribute()!=null){
                List<Integer> customizeAttributeIds =Arrays.asList(data.getCustomizeAttribute().split(","))
                        .stream().map(s -> Integer.parseInt(s.trim()))
                        .collect(Collectors.toList());
                attributeIds.addAll(customizeAttributeIds);
            }
            Specification<DatasetAttributeEntity> specification = (root, query, cb) -> {
                List<Predicate> predicates = new ArrayList<Predicate>();
                CriteriaBuilder.In<Integer> in = cb.in(root.get("id"));
                for (Integer id : attributeIds) {
                    in.value(id);
                }
                if(type != null&& !"".equals(type)){
                    predicates.add(cb.equal(root.get("propertyTypeName"), type));
                }
                if(name != null&& !"".equals(name)){
                    predicates.add(cb.or(cb.like(root.get("code"), "%"+name+"%"),
                            (cb.like(root.get("name"), "%"+name+"%"))));
                }
                predicates.add(in);
                Predicate[] pre = new Predicate[predicates.size()];
                return query.where(predicates.toArray(pre)).getRestriction();
            };
            resultList =  datasetAttributeRepository.findAll(specification);
        }
        return resultList;
    }

    public List<DatasetEntity> getSearchList(Integer parentId,String name) {
        List<DatasetEntity> list = datasetRepository.findAllByStatus(0);
        //获取根节点
        List<DatasetEntity> nav = list.stream()
                .filter(b -> b.getParentId()==parentId)
                .collect(Collectors.toList());
        List<DatasetEntity>  resultResult = list.stream()
                .filter(b -> (b.getParentId()==parentId))
                .collect(Collectors.toList());
        //除了总结点的节点
        list.removeAll(nav);
        for(DatasetEntity dataset:nav){
            getResultResult(dataset,list,resultResult);
        }
        int size = resultResult.size();
        for(int i=0;i<size;i++){
            if(!resultResult.get(i).getName().contains(name)){
                resultResult.remove(resultResult.get(i));
                size--;i--;
            }

        }
        return resultResult;
    }

    @Override
    public String getAllAttributeMoveList() {
        List<DatasetDto> list = datasetRepository.findByType(1);
        //获取根节点
        List<DatasetDto> nav = list.stream()
                .filter(b -> b.getParentId()==0)
                .collect(Collectors.toList());
        //除了总结点的节点
        list.removeAll(nav);
        for(DatasetDto dataset:nav){
            setDatasetDto(dataset,list);
        }
        return JSONObject.toJSONString(nav);
    }

    /**
     * 递归赋值
     */
    public static void setDatasetDto(DatasetDto nav,List<DatasetDto> cList){
        List<DatasetDto> cc = new ArrayList<>();
        int size = cList.size();
        for(int i=0;i<size;i++){
            if(nav.getId()==cList.get(i).getParentId()){
                cc.add(cList.remove(i));//从原数组拿出已匹配的数据放入新的List
                size--;i--;
            }
        }
        for(int j=0;j<cc.size();j++){
            setDatasetDto(cc.get(j),cList);
        }
        nav.setList(cc);
    }

    /**
     * 递归赋值
     */
    public static void getResultResult(DatasetEntity nav,List<DatasetEntity> cList,List<DatasetEntity> resultList){
        List<DatasetEntity> cc = new ArrayList<>();
        int size = cList.size();
        for(int i=0;i<size;i++){
            if(nav.getId()==cList.get(i).getParentId()){
                DatasetEntity ele = cList.remove(i);
                cc.add(ele);//从原数组拿出已匹配的数据放入新的List
                resultList.add(ele);
                size--;i--;
            }
        }
        for(int j=0;j<cc.size();j++){
            getResultResult(cc.get(j),cList,resultList);
        }
    }

    @Override
    public String getAllAttributeCopyList() {
        List<DatasetDto> list = datasetRepository.findAllDto();
        //获取根节点
        List<DatasetDto> nav = list.stream()
                .filter(b -> b.getParentId()==0)
                .collect(Collectors.toList());
        //除了总结点的节点
        list.removeAll(nav);
        Iterator<DatasetDto> iterator = nav.iterator();
        while(iterator.hasNext()){
            DatasetDto dataset = iterator.next();
            if(!setIsDatasetDto(dataset,list))
                iterator.remove();
        }
        return JSONObject.toJSONString(nav);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCopyAttribute(Integer id, Integer copyId) {
        DatasetRelationEntity datasetRelationEntity = datasetRelationRepository.findByDatasetId(copyId);
        datasetRelationRepository.updateByDatasetId(datasetRelationEntity.getInnerAttribute(),datasetRelationEntity.getCustomizeAttribute(),id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveInnerAttribute(DatasetRelationEntity datasetRelationEntity) {
        datasetRelationRepository.updateInnerAttributeByDatasetId(datasetRelationEntity.getInnerAttribute(),datasetRelationEntity.getDatasetId());
    }

    public String removeEle(String arrString,String target){
        List<String> attributeIds =Arrays.asList(arrString.split(","))
                .stream().collect(Collectors.toList());
        attributeIds.remove(target);
        return attributeIds.stream().collect(Collectors.joining(","));
    }

    /**
     * 获取数据标准集的列表并递归赋值
     */
    public static boolean setIsDatasetDto(DatasetDto nav,List<DatasetDto> cList){
        if(nav.getType()==2){
            return true;
        }
        List<DatasetDto> cc = new ArrayList<>();
        int size = cList.size();
        for(int i=0;i<size;i++){
            if(nav.getId()==cList.get(i).getParentId()){
                cc.add(cList.remove(i));//从原数组拿出已匹配的数据放入新的List
                size--;i--;
            }
        }
        boolean flag = false;
        if(cc.size()==0){
            return false;
        }
        Iterator<DatasetDto> iterator = cc.iterator();
        while(iterator.hasNext()){
            DatasetDto dataset = iterator.next();
            if(!setIsDatasetDto(dataset,cList))
                iterator.remove();
        }
        if(cc.size()>0)flag=true;
        nav.setList(cc);
        return flag;
    }
}
