#!/bin/bash

# =============================================================================
# 192.168.121.157 服务器 Hadoop/Spark 停止脚本
# =============================================================================

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 设置环境变量
setup_environment() {
    log_step "设置环境变量..."
    
    # 自动检测安装路径
    if [ -z "$JAVA_HOME" ]; then
        if [ -d "/usr/lib/jvm/java-8-openjdk-amd64" ]; then
            export JAVA_HOME="/usr/lib/jvm/java-8-openjdk-amd64"
        elif [ -d "/usr/lib/jvm/java-1.8.0-openjdk" ]; then
            export JAVA_HOME="/usr/lib/jvm/java-1.8.0-openjdk"
        else
            JAVA_HOME=$(find /usr/lib/jvm -name "java-8-openjdk*" | head -n 1)
            export JAVA_HOME
        fi
    fi
    
    if [ -z "$HADOOP_HOME" ]; then
        if [ -d "/opt/hadoop" ]; then
            export HADOOP_HOME="/opt/hadoop"
        elif [ -d "/usr/local/hadoop" ]; then
            export HADOOP_HOME="/usr/local/hadoop"
        fi
    fi
    
    if [ -z "$SPARK_HOME" ]; then
        if [ -d "/opt/spark" ]; then
            export SPARK_HOME="/opt/spark"
        elif [ -d "/usr/local/spark" ]; then
            export SPARK_HOME="/usr/local/spark"
        fi
    fi
    
    if [ -z "$HIVE_HOME" ]; then
        if [ -d "/opt/hive" ]; then
            export HIVE_HOME="/opt/hive"
        elif [ -d "/usr/local/hive" ]; then
            export HIVE_HOME="/usr/local/hive"
        fi
    fi
    
    # 设置PATH
    export PATH=$JAVA_HOME/bin:$HADOOP_HOME/bin:$HADOOP_HOME/sbin:$SPARK_HOME/bin:$SPARK_HOME/sbin:$HIVE_HOME/bin:$PATH
    
    log_info "环境变量设置完成"
}

# 停止Hive Metastore
stop_hive_metastore() {
    log_step "停止Hive Metastore..."
    
    # 查找并停止Hive Metastore进程
    HIVE_PIDS=$(pgrep -f "hive.*metastore" || true)
    if [ -n "$HIVE_PIDS" ]; then
        log_info "停止Hive Metastore进程: $HIVE_PIDS"
        kill $HIVE_PIDS || true
        sleep 3
        
        # 强制停止
        HIVE_PIDS=$(pgrep -f "hive.*metastore" || true)
        if [ -n "$HIVE_PIDS" ]; then
            log_warn "强制停止Hive Metastore进程: $HIVE_PIDS"
            kill -9 $HIVE_PIDS || true
        fi
        
        log_info "✅ Hive Metastore已停止"
    else
        log_info "Hive Metastore未运行"
    fi
}

# 停止Spark服务
stop_spark() {
    log_step "停止Spark服务..."
    
    if [ -n "$SPARK_HOME" ] && [ -d "$SPARK_HOME" ]; then
        # 使用Spark自带的停止脚本
        if [ -f "$SPARK_HOME/sbin/stop-all.sh" ]; then
            log_info "使用stop-all.sh停止Spark服务..."
            $SPARK_HOME/sbin/stop-all.sh || true
        fi
        
        # 单独停止Worker和Master
        if [ -f "$SPARK_HOME/sbin/stop-worker.sh" ]; then
            log_info "停止Spark Worker..."
            $SPARK_HOME/sbin/stop-worker.sh || true
        fi
        
        if [ -f "$SPARK_HOME/sbin/stop-master.sh" ]; then
            log_info "停止Spark Master..."
            $SPARK_HOME/sbin/stop-master.sh || true
        fi
        
        # 手动停止残留进程
        SPARK_PIDS=$(pgrep -f "spark.*Master\|spark.*Worker" || true)
        if [ -n "$SPARK_PIDS" ]; then
            log_warn "强制停止Spark进程: $SPARK_PIDS"
            kill -9 $SPARK_PIDS || true
        fi
        
        log_info "✅ Spark服务已停止"
    else
        log_info "Spark未安装，跳过停止"
    fi
}

# 停止Hadoop服务
stop_hadoop() {
    log_step "停止Hadoop服务..."
    
    if [ -n "$HADOOP_HOME" ] && [ -d "$HADOOP_HOME" ]; then
        # 停止YARN
        if [ -f "$HADOOP_HOME/sbin/stop-yarn.sh" ]; then
            log_info "停止YARN..."
            $HADOOP_HOME/sbin/stop-yarn.sh || true
        fi
        
        # 停止HDFS
        if [ -f "$HADOOP_HOME/sbin/stop-dfs.sh" ]; then
            log_info "停止HDFS..."
            $HADOOP_HOME/sbin/stop-dfs.sh || true
        fi
        
        # 手动停止残留进程
        HADOOP_PIDS=$(pgrep -f "hadoop.*NameNode\|hadoop.*DataNode\|hadoop.*ResourceManager\|hadoop.*NodeManager" || true)
        if [ -n "$HADOOP_PIDS" ]; then
            log_warn "强制停止Hadoop进程: $HADOOP_PIDS"
            kill -9 $HADOOP_PIDS || true
        fi
        
        log_info "✅ Hadoop服务已停止"
    else
        log_info "Hadoop未安装，跳过停止"
    fi
}

# 清理临时文件
cleanup_temp_files() {
    log_step "清理临时文件..."
    
    # 清理PID文件
    rm -f /tmp/hadoop/*.pid 2>/dev/null || true
    rm -f /tmp/spark/*.pid 2>/dev/null || true
    
    # 清理锁文件
    rm -f /tmp/hadoop-*/*.lock 2>/dev/null || true
    
    log_info "✅ 临时文件清理完成"
}

# 验证停止状态
verify_stop_status() {
    log_step "验证停止状态..."
    
    echo ""
    echo "=== 剩余Java进程 ==="
    REMAINING_JAVA=$(jps | grep -v "Jps" || true)
    if [ -n "$REMAINING_JAVA" ]; then
        log_warn "仍有Java进程在运行:"
        echo "$REMAINING_JAVA"
    else
        log_info "✅ 所有Java进程已停止"
    fi
    
    echo ""
    echo "=== 端口监听状态 ==="
    LISTENING_PORTS=$(netstat -tlnp 2>/dev/null | grep -E ':7077|:8080|:9000|:9083|:8088|:9870' || true)
    if [ -n "$LISTENING_PORTS" ]; then
        log_warn "仍有端口在监听:"
        echo "$LISTENING_PORTS"
    else
        log_info "✅ 所有相关端口已释放"
    fi
    echo ""
}

# 主函数
main() {
    echo "============================================================================="
    echo "192.168.121.157 服务器 Hadoop/Spark 停止脚本"
    echo "============================================================================="
    echo "停止时间: $(date)"
    echo ""
    
    setup_environment
    stop_hive_metastore
    stop_spark
    stop_hadoop
    cleanup_temp_files
    verify_stop_status
    
    echo ""
    echo "============================================================================="
    echo "停止完成！"
    echo "============================================================================="
    echo ""
    echo "如果需要重新启动服务，请运行:"
    echo "  ./start-hadoop-spark-157.sh"
    echo ""
}

# 执行主函数
main "$@"
