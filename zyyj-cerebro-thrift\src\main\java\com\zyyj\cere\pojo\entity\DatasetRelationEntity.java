package com.zyyj.cere.pojo.entity;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 数据集与对应属性关联表
 */

@ThriftStruct
@Data
@Entity
@NoArgsConstructor
@Table(name = "dataset_relation")
@ApiModel(value = "DatasetEntity", description = "数据集与对应属性关联表")
public class DatasetRelationEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false,length = 11)
    @ApiModelProperty(name = "id", value = "ID")
    private Integer id;


    /**
     * 数据集Id
     */
    @NotNull(message = "数据集Id")
    @ApiModelProperty(name = "dataset_id", value = "数据集Id")
    @Column(name = "dataset_id", nullable = false,length = 10)
    private Integer datasetId;

    /**
     * 内置属性ID集合
     */
    @ApiModelProperty(name = "innerAttribute", value = "属性ID")
    @Column(name = "inner_attribute" ,length = 100)
    private String innerAttribute;

    /**
     * 自定义属性集合
     */
    @ApiModelProperty(name = "customizeAttribute", value = "自定义属性集合")
    @Column(name = "customize_attribute")
    private String customizeAttribute;

    @ThriftConstructor
    public DatasetRelationEntity(Integer id, Integer datasetId, String innerAttribute, String customizeAttribute) {
        this.id = id;
        this.datasetId = datasetId;
        this.innerAttribute = innerAttribute;
        this.customizeAttribute = customizeAttribute;
    }

    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField(2)
    public Integer getDatasetId() {
        return datasetId;
    }

    @ThriftField(3)
    public String getInnerAttribute() {
        return innerAttribute;
    }

    @ThriftField(4)
    public String getCustomizeAttribute() {
        return customizeAttribute;
    }
}
