package com.zyyj.cere.repository;

import com.zyyj.cere.pojo.entity.ApiServiceEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2020/11/16 16:14
 */
public interface ApiServiceRepository extends JpaRepository<ApiServiceEntity, Integer> {

    List<ApiServiceEntity> findByName(String name);

    @Query(value = "SELECT * FROM api_service WHERE id != ?1 AND name = ?2", nativeQuery = true)
    List<ApiServiceEntity> existByIdAndName(Integer id, String name);

}
