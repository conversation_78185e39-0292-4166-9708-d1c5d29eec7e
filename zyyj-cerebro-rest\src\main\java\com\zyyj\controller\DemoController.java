package com.zyyj.controller;


import com.zyyj.cere.service.DemoService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.lang.Thread.*;

@Slf4j
@RestController
@RequestMapping("/demo")
public class DemoController {

    @Autowired
    DemoService demoService;

    @ApiOperation("demo模板")
    @GetMapping("/info/{id}")
    public List<Map<String,Object>> getId(@PathVariable Long id) {

        List<Map<String,Object>> l = new ArrayList<>();
        l.add(new HashMap<String,Object>(){{
            put("Id",demoService.getId(id));
        }});
        return l;
    }

    @ApiOperation("demo模板")
    @PostMapping("/list")
    public String getList() throws InterruptedException {
        sleep(1000 * 80);
        System.out.println("-===--=-==-");
        return "11";
    }

    @PostMapping("/kafka/connectors/del")
    public String delKafka() {
        demoService.deleteKafkaConnector();
        return "11";
    }


}
