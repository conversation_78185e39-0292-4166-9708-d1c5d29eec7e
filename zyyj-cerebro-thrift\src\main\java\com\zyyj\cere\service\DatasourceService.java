package com.zyyj.cere.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.zyyj.cere.pojo.dto.*;
import com.zyyj.cere.pojo.entity.DatasourceEntity;
import com.zyyj.cere.pojo.entity.DatasourceTypeEntity;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.domain.pagination.Paging;
import java.util.List;
import java.util.Map;


/**
 * 数据源
 */
@ThriftService
public interface DatasourceService {

    /**
     * 获取数据源列表--分页
     *
     * @return
     * @throws ApplicationException
     */
    @ThriftMethod
    PagedDatasourceDTO getListPage(Paging pageable);

    /**
     * 获取数据源列表
     *
     * @return
     * @throws ApplicationException
     */
    @ThriftMethod
    List<DatasourceEntity> getList();

    /**
     * 获取数据域对象
     *
     * @param id
     * @return
     */
    @ThriftMethod
    DatasourceDTO get(Long id);

    /**
     * 添加数据域
     *
     * @param data
     */
    @ThriftMethod
    void set(DatasourceEntity data);

    /**
     * 编辑数据域
     *
     * @param data
     */
    @ThriftMethod
    void update(DatasourceEntity data);

    /**
     * 删除数据域
     *
     * @param id
     */
    @ThriftMethod
    void delete(Long id);

    /**
     * @Description: 测试连接
     * @Param: [data]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/9
     */
    @ThriftMethod
    int testConnect(DatasourceEntity data);

    @ThriftMethod
    Map<String, List<TableNameDTO>> getDataSourceTables(Integer dataSourceId);

    @ThriftMethod
    Map<String, List<TableFieldDTO>> getDataSourceTableFields(Integer dataSourceId, String tableName);

    @ThriftMethod
    List<DatasourceTypeEntity> getDatasourceType();


}
