package com.zyyj.cere.pojo.dto;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.zyyj.cere.pojo.entity.BusinessEntity;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/16 11:01
 */
@ThriftStruct
@Setter
@NoArgsConstructor
@ToString
public class SubjectBusinessDTO {
    private Integer subjectId;
    private String subjectName;
    private List<BusinessEntity> list;

    @ThriftConstructor
    public SubjectBusinessDTO(Integer subjectId, String subjectName, List<BusinessEntity> list) {
        this.subjectId = subjectId;
        this.subjectName = subjectName;
        this.list = list;
    }

    @ThriftField(1)
    public Integer getSubjectId() {
        return subjectId;
    }

    @ThriftField(2)
    public String getSubjectName() {
        return subjectName;
    }

    @ThriftField(3)
    public List<BusinessEntity> getList() {
        return list;
    }
}
