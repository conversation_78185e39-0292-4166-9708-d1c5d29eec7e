package com.zyyj.controller;

import com.zyyj.cere.pojo.dto.DataMsgDTO;
import com.zyyj.cere.pojo.dto.PageListStringDTO;
import com.zyyj.cere.pojo.entity.TableModelingEntity;
import com.zyyj.cere.service.TableModelingService;
import com.zyyj.domain.exception.ApplicationException;
import com.zyyj.domain.pagination.Paging;
import com.zyyj.utils.PublicUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/19 17:38
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/modeling")
public class TableModelingController {

    @Autowired
    TableModelingService tableModelingService;


    /**
     * @Description: 新增建模/编辑  (编辑参数传入json必须有id)
     * @Param: [businessId, tableModelingEntity]
     * @return: void
     * @Author: bravelee
     * @Date: 2020/11/19
     */
    @PostMapping("/component/{businessId}")
    public Map<String, Integer> addTableModelingComponent(@PathVariable Integer businessId, @RequestBody TableModelingEntity tableModelingEntity) {
        if (!PublicUtils.checkTableName(tableModelingEntity.getTableName())) {
            throw new ApplicationException("表英文名不合法");
        }
        PublicUtils.checkIntNotNull(businessId);
        PublicUtils.checkNotEmptyArr(tableModelingEntity.getName());
        PublicUtils.checkNotEmptyArr(tableModelingEntity.getTableName());
        PublicUtils.checkNotEmptyArr(tableModelingEntity.getDataJson());
        PublicUtils.checkNotEmptyArr(tableModelingEntity.getDataSql());
        DataMsgDTO<Map<String, Integer>> data = tableModelingService.addTableModelingComponent(businessId, tableModelingEntity);
        if (!data.getErrMsg().isEmpty()) {
            throw new ApplicationException(data.getErrMsg());
        }
        Map<String, Integer> map = new HashMap<>();
        map.put("id", data.getData().get("modeling_id"));
        map.put("table_id", data.getData().get("table_id"));
        return map;

    }

    /**
     * @Description: 预览数据
     * @Param: [data_sql]
     * @return: java.util.Map<java.lang.String, java.util.List < java.lang.String>>
     * @Author: bravelee
     * @Date: 2020/11/20
     */
    @GetMapping("/preview")
    public PageListStringDTO previewData(String dataSql, Paging paging) {
        System.out.println("=======sql======" + dataSql);
        System.out.println("=======sql======" + paging);
        if (paging.getPage() == 0) {
            paging.setPage(1);
        }
        if (paging.getSize() == 0) {
            paging.setSize(10);
        }
        return tableModelingService.previewData(dataSql, paging);
    }

    /**
     * @Description: 建模详情
     * @Param: [id]
     * @return: com.zyyj.cere.pojo.entity.TableModelingEntity
     * @Author: bravelee
     * @Date: 2020/11/19
     */
    @GetMapping("/info/{id}")
    public TableModelingEntity getTableModelingComponent(@PathVariable Integer id) {
        PublicUtils.checkIntNotNull(id);
        return tableModelingService.getTableModelingComponent(id);
    }


}
