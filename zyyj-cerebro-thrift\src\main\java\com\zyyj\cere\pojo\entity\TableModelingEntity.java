package com.zyyj.cere.pojo.entity;


import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 数据建模
 */
@ThriftStruct
@Setter
@NoArgsConstructor
@ToString
@Builder
@ApiModel(value = "TableModelingEntity", description = "数据建模")
@Entity
@Table(name = "`table_modeling`")
@EntityListeners(TableModelingEntity.class)
public class TableModelingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    /**
     * 类型 1 组件建模  2 敏捷建模
     */
    @Column(name = "type", nullable = false)
    private Byte type;

    /**
     * 名称
     */
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 对应表名
     */
    @Column(name = "table_name", nullable = false)
    private String tableName;

    /**
     * 建模内容
     */
    @Column(name = "data_json", nullable = false, columnDefinition = "LONGTEXT")
    private String dataJson;

    /**
     * 建模sql
     */
    @Column(name = "data_sql", nullable = false, columnDefinition = "LONGTEXT")
    private String dataSql;

    @ThriftConstructor
    public TableModelingEntity(Long id, Byte type, String name, String tableName, String dataJson, String dataSql) {
        this.id = id;
        this.type = type;
        this.name = name;
        this.tableName = tableName;
        this.dataJson = dataJson;
        this.dataSql = dataSql;
    }

    @ThriftField(1)
    public Long getId() {
        return id;
    }

    @ThriftField(2)
    public Byte getType() {
        return type;
    }

    @ThriftField(3)
    public String getName() {
        return name;
    }

    @ThriftField(4)
    public String getTableName() {
        return tableName;
    }

    @ThriftField(5)
    public String getDataJson() {
        return dataJson;
    }

    @ThriftField(6)
    public String getDataSql() {
        return dataSql;
    }
}
