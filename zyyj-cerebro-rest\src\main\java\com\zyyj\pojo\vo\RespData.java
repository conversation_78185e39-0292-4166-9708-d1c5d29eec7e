package com.zyyj.pojo;


import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public final class RespData<T> {


    public enum Status {

        SUCCESS(200, "请求成功"),
        EMPTY(220, "数据空"),
        ERROR(400, "请求出错"),
        ;

        private int code;
        private String message;

        Status(int i, String s) {
            this.code=i;
            this.message=s;
        }

    }

    private int status;
    private String msg;
    private T data;

    public RespData(Status status, String msg) {
        this.status = status.code;
        this.msg = msg;
    }

    public RespData(Status status, String msg, T data) {
        this.status = status.code;
        this.msg = msg;
        this.data = data;
    }

    public RespData(T data) {
        this.status = 200;
        this.msg = "";
        this.data = data;
    }

    public int getStatus() {
        return this.status;
    }

    public Object getData() {
        return this.data;
    }

    public String getMsg() { return this.msg; }
}
