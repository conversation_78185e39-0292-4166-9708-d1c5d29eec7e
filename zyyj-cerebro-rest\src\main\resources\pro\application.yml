env-name: dev
eureka:
  instance:
    prefer-ip-address: true
  client:
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://************:8761/eureka/

# actuator security switch
management:
  security:
    enabled: false

server:
  port: 8005

spring:
  application:
    name: ZYYJ-CEREBRO-REST
  main:
    allow-bean-definition-overriding: true
  redis:
    database: 1
    host: *************
    port: 6379
    password: Ubq1jSDR64S8ovlYl
    timeout: 60000
service:
  #thrift 客户端配置(demo)
  cerebro: ZYYJ-CEREBRO-THRIFT
scan:
  package: com.zyyj.controller

qiniu:
  bucket: "k12-hub"
  accessKey: "4D-Ef42EOmhLlhdGt2HbGTaWlbvWPrEFgzLv3HiD"
  secretKey: "hZBAp3F7_Y5Lt92jJ4ke5QjAo5zUO22E9JdV33Ra"