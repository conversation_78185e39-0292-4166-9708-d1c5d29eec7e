#!/bin/bash

# =============================================================================
# 修复版 *************** 服务器 Hadoop/Spark 启动脚本
# =============================================================================

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 设置环境变量
setup_environment() {
    log_step "设置环境变量..."
    
    # 基本环境变量
    export JAVA_HOME="/usr/lib/jvm/java-8-openjdk-amd64"
    export HADOOP_HOME="/opt/hadoop"
    export SPARK_HOME="/opt/spark"
    export HIVE_HOME="/opt/hive"
    export PATH=$JAVA_HOME/bin:$HADOOP_HOME/bin:$HADOOP_HOME/sbin:$SPARK_HOME/bin:$SPARK_HOME/sbin:$HIVE_HOME/bin:$PATH
    
    # Hadoop 3.x 需要的用户环境变量
    export HDFS_NAMENODE_USER="root"
    export HDFS_DATANODE_USER="root"
    export HDFS_SECONDARYNAMENODE_USER="root"
    export YARN_RESOURCEMANAGER_USER="root"
    export YARN_NODEMANAGER_USER="root"
    
    log_info "环境变量设置完成"
}

# 停止现有服务
stop_services() {
    log_step "停止现有服务..."
    
    # 停止Hive Metastore
    pkill -f "hive.*metastore" || true
    
    # 停止Spark
    if [ -f "$SPARK_HOME/sbin/stop-all.sh" ]; then
        $SPARK_HOME/sbin/stop-all.sh || true
    fi
    
    # 停止Hadoop
    if [ -f "$HADOOP_HOME/sbin/stop-yarn.sh" ]; then
        $HADOOP_HOME/sbin/stop-yarn.sh || true
    fi
    
    if [ -f "$HADOOP_HOME/sbin/stop-dfs.sh" ]; then
        $HADOOP_HOME/sbin/stop-dfs.sh || true
    fi
    
    # 强制停止残留进程
    pkill -f "hadoop.*NameNode" || true
    pkill -f "hadoop.*DataNode" || true
    pkill -f "hadoop.*ResourceManager" || true
    pkill -f "hadoop.*NodeManager" || true
    pkill -f "spark.*Master" || true
    pkill -f "spark.*Worker" || true
    
    sleep 5
    log_info "✅ 现有服务已停止"
}

# 启动Hadoop服务
start_hadoop() {
    log_step "启动Hadoop服务..."
    
    # 启动HDFS
    log_info "启动HDFS..."
    $HADOOP_HOME/sbin/start-dfs.sh
    
    # 等待HDFS启动
    sleep 10
    
    # 启动YARN
    log_info "启动YARN..."
    $HADOOP_HOME/sbin/start-yarn.sh
    
    # 等待YARN启动
    sleep 10
    
    # 创建必要的HDFS目录
    log_info "创建HDFS目录..."
    $HADOOP_HOME/bin/hdfs dfs -mkdir -p /spark-logs || true
    $HADOOP_HOME/bin/hdfs dfs -mkdir -p /spark-warehouse || true
    $HADOOP_HOME/bin/hdfs dfs -mkdir -p /hive/warehouse || true
    $HADOOP_HOME/bin/hdfs dfs -chmod 777 /spark-logs || true
    $HADOOP_HOME/bin/hdfs dfs -chmod 777 /spark-warehouse || true
    $HADOOP_HOME/bin/hdfs dfs -chmod 777 /hive/warehouse || true
    
    log_info "✅ Hadoop服务启动完成"
}

# 启动Spark服务
start_spark() {
    log_step "启动Spark服务..."
    
    # 启动Spark Master
    log_info "启动Spark Master..."
    $SPARK_HOME/sbin/start-master.sh
    
    # 等待Master启动
    sleep 5
    
    # 启动Spark Worker
    log_info "启动Spark Worker..."
    $SPARK_HOME/sbin/start-worker.sh spark://$(hostname):7077
    
    # 等待Worker启动
    sleep 5
    
    log_info "✅ Spark服务启动完成"
}

# 启动Hive Metastore
start_hive_metastore() {
    log_step "启动Hive Metastore..."
    
    # 初始化Schema（如果需要）
    if [ ! -f "/tmp/hive_schema_initialized" ]; then
        log_info "初始化Hive Schema..."
        $HIVE_HOME/bin/schematool -dbType derby -initSchema || true
        touch /tmp/hive_schema_initialized
    fi
    
    # 启动Metastore服务
    log_info "启动Hive Metastore服务..."
    nohup $HIVE_HOME/bin/hive --service metastore > /var/log/hive-metastore.log 2>&1 &
    
    # 等待服务启动
    sleep 10
    
    log_info "✅ Hive Metastore启动完成"
}

# 验证服务状态
verify_services() {
    log_step "验证服务状态..."
    
    echo ""
    echo "=== Java进程状态 ==="
    jps
    
    echo ""
    echo "=== 端口监听状态 ==="
    
    check_port() {
        local port=$1
        local name=$2
        if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
            log_info "✅ $name (端口 $port) - 正在监听"
        else
            log_warn "❌ $name (端口 $port) - 未监听"
        fi
    }
    
    check_port 7077 "Spark Master"
    check_port 8080 "Spark Web UI"
    check_port 9000 "HDFS NameNode"
    check_port 9083 "Hive Metastore"
    check_port 8088 "YARN ResourceManager"
    check_port 9870 "HDFS Web UI"
    
    echo ""
    echo "=== Web界面访问地址 ==="
    HOST_IP=$(hostname -I | awk '{print $1}')
    echo "Spark Master Web UI: http://$HOST_IP:8080"
    echo "HDFS Web UI: http://$HOST_IP:9870"
    echo "YARN ResourceManager: http://$HOST_IP:8088"
    echo ""
}

# 生成状态检查命令
generate_status_commands() {
    log_step "生成状态检查命令..."
    
    cat > /root/check-status.sh << 'EOF'
#!/bin/bash
echo "=== 服务状态检查 ==="
echo "时间: $(date)"
echo ""

echo "=== Java进程 ==="
jps

echo ""
echo "=== 端口监听 ==="
netstat -tlnp | grep -E ':7077|:8080|:9000|:9083|:8088|:9870'

echo ""
echo "=== HDFS状态 ==="
if command -v hdfs &> /dev/null; then
    export JAVA_HOME="/usr/lib/jvm/java-8-openjdk-amd64"
    export HADOOP_HOME="/opt/hadoop"
    export PATH=$JAVA_HOME/bin:$HADOOP_HOME/bin:$PATH
    hdfs dfsadmin -report
fi

echo ""
echo "=== Web界面 ==="
HOST_IP=$(hostname -I | awk '{print $1}')
echo "Spark Master: http://$HOST_IP:8080"
echo "HDFS: http://$HOST_IP:9870"
echo "YARN: http://$HOST_IP:8088"
EOF
    
    chmod +x /root/check-status.sh
    log_info "✅ 状态检查脚本已创建: /root/check-status.sh"
}

# 主函数
main() {
    echo "============================================================================="
    echo "修复版 *************** 服务器 Hadoop/Spark 启动脚本"
    echo "============================================================================="
    echo "启动时间: $(date)"
    echo ""
    
    setup_environment
    stop_services
    start_hadoop
    start_spark
    start_hive_metastore
    verify_services
    generate_status_commands
    
    echo ""
    echo "============================================================================="
    echo "启动完成！"
    echo "============================================================================="
    echo ""
    echo "服务状态检查命令: /root/check-status.sh"
    echo ""
}

# 执行主函数
main "$@"
