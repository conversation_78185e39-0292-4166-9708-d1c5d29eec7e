package com.zyyj.controller;

import com.zyyj.CerebroRestApplication;
import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CerebroRestApplication.class)
public class DemoControllerTest extends TestCase {

    @Autowired
    private DemoController demoController;

    @Test
    public void testGetId(){
        System.out.println(demoController.getId(1L));
    }
}